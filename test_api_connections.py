"""
API連接測試腳本
測試Bybit和Blave API的連接狀態

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_bybit_api():
    """測試Bybit API連接"""
    print("🔍 測試Bybit API連接...")
    
    # 測試不同的Bybit端點
    endpoints = [
        "https://api.bybit.com/v5/market/kline",
        "https://api-testnet.bybit.com/v5/market/kline",
        "https://api.bybit.com/derivatives/v3/public/kline"
    ]
    
    params = {
        "category": "linear",
        "symbol": "BTCUSDT",
        "interval": "60",
        "limit": 1
    }
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            try:
                print(f"   測試端點: {endpoint}")
                async with session.get(endpoint, params=params, timeout=10) as response:
                    print(f"   狀態碼: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        print(f"   ✅ 成功: {json.dumps(data, indent=2)[:200]}...")
                        return endpoint
                    else:
                        print(f"   ❌ 失敗: {response.status}")
            except Exception as e:
                print(f"   ❌ 錯誤: {e}")
    
    return None

async def test_blave_api():
    """測試Blave API連接"""
    print("\n🔍 測試Blave API連接...")
    
    # 測試不同的Blave端點
    endpoints = [
        "https://api.blave.io/v1",
        "https://blave.io/api/v1",
        "https://api.blave.com/v1"
    ]
    
    headers = {
        "api-key": "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6",
        "secret-key": "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
    }
    
    async with aiohttp.ClientSession() as session:
        for base_url in endpoints:
            # 測試籌碼集中度端點
            concentration_url = f"{base_url}/holder_concentration/get_alpha"
            params = {
                "symbol": "BTCUSDT",
                "period": "1h"
            }
            
            try:
                print(f"   測試端點: {concentration_url}")
                async with session.get(concentration_url, headers=headers, params=params, timeout=10) as response:
                    print(f"   狀態碼: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        print(f"   ✅ 成功: {json.dumps(data, indent=2)[:200]}...")
                        return base_url
                    else:
                        text = await response.text()
                        print(f"   ❌ 失敗: {response.status} - {text[:100]}")
            except Exception as e:
                print(f"   ❌ 錯誤: {e}")
    
    return None

async def test_alternative_apis():
    """測試替代API"""
    print("\n🔍 測試替代數據源...")
    
    # 測試CoinGecko API (免費)
    try:
        async with aiohttp.ClientSession() as session:
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                "ids": "bitcoin,ethereum,solana",
                "vs_currencies": "usd"
            }
            
            async with session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ CoinGecko API可用: {data}")
                    return True
    except Exception as e:
        print(f"   ❌ CoinGecko API錯誤: {e}")
    
    # 測試Binance API (免費)
    try:
        async with aiohttp.ClientSession() as session:
            url = "https://api.binance.com/api/v3/klines"
            params = {
                "symbol": "BTCUSDT",
                "interval": "1h",
                "limit": 1
            }
            
            async with session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Binance API可用: {len(data)} 條數據")
                    return True
    except Exception as e:
        print(f"   ❌ Binance API錯誤: {e}")
    
    return False

async def main():
    """主測試函數"""
    print("🚀 開始API連接測試...")
    print(f"⏰ 測試時間: {datetime.now()}")
    
    # 測試Bybit
    working_bybit = await test_bybit_api()
    
    # 測試Blave
    working_blave = await test_blave_api()
    
    # 測試替代方案
    alternatives_work = await test_alternative_apis()
    
    print("\n" + "="*50)
    print("📊 測試結果總結:")
    print("="*50)
    
    if working_bybit:
        print(f"✅ Bybit API: 可用 ({working_bybit})")
    else:
        print("❌ Bybit API: 不可用")
    
    if working_blave:
        print(f"✅ Blave API: 可用 ({working_blave})")
    else:
        print("❌ Blave API: 不可用")
    
    if alternatives_work:
        print("✅ 替代API: 可用")
    else:
        print("❌ 替代API: 不可用")
    
    print("\n🔧 建議解決方案:")
    if not working_bybit:
        print("- 檢查Bybit API端點是否正確")
        print("- 考慮使用Binance API作為價格數據源")
    
    if not working_blave:
        print("- 檢查Blave API是否需要白名單")
        print("- 確認API密鑰是否有效")
        print("- 考慮聯繫Blave技術支持")
    
    if not working_bybit and not working_blave:
        print("- 可能是Railway服務器網絡限制")
        print("- 考慮使用代理或VPN")
        print("- 切換到免費的公共API")

if __name__ == "__main__":
    asyncio.run(main())

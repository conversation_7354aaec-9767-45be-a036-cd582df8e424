"""
健康檢查模組
提供系統健康狀態檢查端點

作者: 專業量化策略工程師
"""

import asyncio
from aiohttp import web
from datetime import datetime
from .logger_setup import setup_logger

class HealthCheckServer:
    """健康檢查服務器"""
    
    def __init__(self, port=8080):
        self.port = port
        self.logger = setup_logger("HealthCheck")
        self.app = web.Application()
        self.setup_routes()
        
    def setup_routes(self):
        """設置路由"""
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/status', self.system_status)
        
    async def health_check(self, request):
        """健康檢查端點"""
        try:
            return web.json_response({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'service': '聖杯級交易信號系統'
            })
        except Exception as e:
            self.logger.error(f"健康檢查失敗: {e}")
            return web.json_response({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)
    
    async def system_status(self, request):
        """系統狀態端點"""
        try:
            return web.json_response({
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'system': '聖杯級交易信號系統',
                'version': '1.0.0',
                'strategies': ['PEPE-1H', 'XRP-1H', 'SOL-1H']
            })
        except Exception as e:
            self.logger.error(f"狀態檢查失敗: {e}")
            return web.json_response({
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)
    
    async def start_server(self):
        """啟動健康檢查服務器"""
        try:
            runner = web.AppRunner(self.app)
            await runner.setup()
            site = web.TCPSite(runner, '0.0.0.0', self.port)
            await site.start()
            self.logger.info(f"健康檢查服務器啟動在端口 {self.port}")
        except Exception as e:
            self.logger.error(f"健康檢查服務器啟動失敗: {e}")

async def start_health_check_server():
    """啟動健康檢查服務器的便捷函數"""
    server = HealthCheckServer()
    await server.start_server()
    return server

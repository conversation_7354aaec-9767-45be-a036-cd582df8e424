#!/usr/bin/env python3
"""
分析符合條件的RSI濾網策略
根據用戶條件篩選最佳策略組合
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_strategies():
    """分析策略結果"""
    
    # 讀取回測結果
    try:
        df = pd.read_csv('Extended_RSI_Results/Extended_RSI_Summary_20250714_131523.csv')
        print(f"📊 讀取到 {len(df)} 個策略組合")
    except FileNotFoundError:
        print("❌ 找不到回測結果文件")
        return
    
    # 篩選條件
    min_win_rate = 70.0  # 勝率 ≥ 70%
    min_total_return = 5.0  # 總收益 ≥ 5%
    min_daily_signals = 0.5  # 每日平均信號 ≥ 0.5個 (8天數據)
    
    print("\n🎯 篩選條件:")
    print(f"   勝率 ≥ {min_win_rate}%")
    print(f"   總收益 ≥ {min_total_return}%")
    print(f"   每日平均信號 ≥ {min_daily_signals}個")
    
    # 計算每日平均信號數（8天數據）
    df['Daily_Signals'] = df['Total_Signals'] / 8.0
    
    # 計算總收益（假設每筆交易平均收益）
    df['Total_Return_%'] = df['Avg_Return_%'] * df['Total_Trades']
    
    # 應用篩選條件
    qualified = df[
        (df['Win_Rate_%'] >= min_win_rate) &
        (df['Total_Return_%'] >= min_total_return) &
        (df['Daily_Signals'] >= min_daily_signals)
    ].copy()
    
    print(f"\n✅ 符合條件的策略: {len(qualified)} 個")
    
    if len(qualified) == 0:
        print("❌ 沒有策略符合所有條件")
        
        # 分析各條件的通過情況
        win_rate_pass = len(df[df['Win_Rate_%'] >= min_win_rate])
        return_pass = len(df[df['Total_Return_%'] >= min_total_return])
        signals_pass = len(df[df['Daily_Signals'] >= min_daily_signals])
        
        print(f"\n📊 條件通過情況:")
        print(f"   勝率條件通過: {win_rate_pass}/{len(df)} ({win_rate_pass/len(df)*100:.1f}%)")
        print(f"   收益條件通過: {return_pass}/{len(df)} ({return_pass/len(df)*100:.1f}%)")
        print(f"   信號條件通過: {signals_pass}/{len(df)} ({signals_pass/len(df)*100:.1f}%)")
        
        # 降低條件重新篩選
        print(f"\n🔄 降低條件重新篩選...")
        relaxed_qualified = df[
            (df['Win_Rate_%'] >= 65.0) &  # 降低勝率要求
            (df['Total_Return_%'] >= 3.0) &  # 降低收益要求
            (df['Daily_Signals'] >= 0.5)  # 降低信號要求
        ].copy()
        
        print(f"✅ 放寬條件後符合的策略: {len(relaxed_qualified)} 個")
        qualified = relaxed_qualified
    
    if len(qualified) > 0:
        # 按策略評分排序
        qualified = qualified.sort_values('Strategy_Score', ascending=False)
        
        print(f"\n🏆 符合條件的策略排名:")
        print("="*100)
        
        for i, (_, row) in enumerate(qualified.head(15).iterrows(), 1):
            # 判斷幣種類型
            mainstream = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT']
            large_alt = ['ADAUSDT', 'DOTUSDT', 'LTCUSDT', 'LINKUSDT', 'AVAXUSDT', 'MATICUSDT', 'ALGOUSDT', 'ATOMUSDT', 'UNIUSDT', 'AAVEUSDT']
            
            if row['Symbol'] in mainstream:
                coin_type = "主流"
            elif row['Symbol'] in large_alt:
                coin_type = "大型山寨"
            else:
                coin_type = "中小型山寨"
            
            print(f"{i:2d}. {row['Symbol']} {row['Timeframe']} ({coin_type})")
            print(f"    策略評分: {row['Strategy_Score']:.1f}")
            print(f"    參數: BB({row['BB_Window']}, {row['BB_Std']}) RR{row['Risk_Reward_Ratio']}")
            print(f"    勝率: {row['Win_Rate_%']:.1f}% | 平均收益: {row['Avg_Return_%']:+.2f}%")
            print(f"    總收益: {row['Total_Return_%']:+.1f}% | 日均信號: {row['Daily_Signals']:.1f}個")
            print(f"    信號數: {row['Total_Signals']}個 | 盈虧比: {row['Profit_Factor']:.2f}")
            print()
        
        # 統計分析
        print(f"\n📊 符合條件策略統計:")
        print(f"   平均策略評分: {qualified['Strategy_Score'].mean():.1f}")
        print(f"   平均勝率: {qualified['Win_Rate_%'].mean():.1f}%")
        print(f"   平均總收益: {qualified['Total_Return_%'].mean():.1f}%")
        print(f"   平均日信號數: {qualified['Daily_Signals'].mean():.1f}個")
        
        # 時間框架分析
        timeframe_stats = qualified.groupby('Timeframe').agg({
            'Strategy_Score': 'mean',
            'Win_Rate_%': 'mean',
            'Total_Return_%': 'mean',
            'Daily_Signals': 'mean'
        }).round(1)
        
        print(f"\n📈 時間框架對比:")
        for tf in timeframe_stats.index:
            stats = timeframe_stats.loc[tf]
            count = len(qualified[qualified['Timeframe'] == tf])
            print(f"   {tf}: {count}個策略 | 平均評分{stats['Strategy_Score']:.1f} | "
                  f"平均勝率{stats['Win_Rate_%']:.1f}% | 平均收益{stats['Total_Return_%']:.1f}%")
        
        # 幣種類型分析
        mainstream_count = len(qualified[qualified['Symbol'].isin(mainstream)])
        large_alt_count = len(qualified[qualified['Symbol'].isin(large_alt)])
        small_alt_count = len(qualified) - mainstream_count - large_alt_count
        
        print(f"\n🪙 幣種類型分布:")
        print(f"   主流幣: {mainstream_count}個")
        print(f"   大型山寨: {large_alt_count}個")
        print(f"   中小型山寨: {small_alt_count}個")
        
        # 保存符合條件的策略
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"Qualified_RSI_Strategies_{timestamp}.csv"
        qualified.to_csv(output_file, index=False)
        print(f"\n✅ 符合條件的策略已保存: {output_file}")
        
        # 生成報單系統配置
        generate_signal_config(qualified)
    
    return qualified

def generate_signal_config(qualified_strategies):
    """生成報單系統配置"""
    
    config = {
        "active_strategies": {},
        "strategy_count": len(qualified_strategies),
        "last_update": datetime.now().isoformat(),
        "selection_criteria": {
            "min_win_rate": 70.0,
            "min_total_return": 5.0,
            "min_daily_signals": 1.0
        }
    }
    
    for _, row in qualified_strategies.iterrows():
        key = f"{row['Symbol']}_{row['Timeframe']}"
        config["active_strategies"][key] = {
            "symbol": row['Symbol'],
            "timeframe": row['Timeframe'],
            "bb_window": int(row['BB_Window']),
            "bb_std": float(row['BB_Std']),
            "risk_reward_ratio": float(row['Risk_Reward_Ratio']),
            "strategy_score": float(row['Strategy_Score']),
            "win_rate": float(row['Win_Rate_%']),
            "avg_return_pct": float(row['Avg_Return_%']),
            "total_return_pct": float(row['Total_Return_%']),
            "daily_signals": float(row['Daily_Signals']),
            "profit_factor": float(row['Profit_Factor'])
        }
    
    import json
    config_file = "rsi_signal_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 報單系統配置已生成: {config_file}")
    
    # 打印推薦的策略組合
    print(f"\n🚀 推薦報單策略組合 (前10名):")
    print("="*80)
    
    top_strategies = qualified_strategies.head(10)
    for i, (_, row) in enumerate(top_strategies.iterrows(), 1):
        print(f"{i:2d}. {row['Symbol']} {row['Timeframe']} - 評分{row['Strategy_Score']:.1f}")
        print(f"    勝率{row['Win_Rate_%']:.1f}% | 總收益{row['Total_Return_%']:+.1f}% | 日均{row['Daily_Signals']:.1f}信號")
    
    print(f"\n💡 建議:")
    print(f"   1. 將以上策略加入自動報單系統")
    print(f"   2. 每日凌晨2點重新優化參數")
    print(f"   3. 監控實際交易表現並調整")

if __name__ == "__main__":
    print("🔍 分析RSI濾網策略回測結果")
    print("="*50)
    
    qualified = analyze_strategies()
    
    print(f"\n🎉 分析完成！")

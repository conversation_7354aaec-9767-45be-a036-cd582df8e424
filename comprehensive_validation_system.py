"""
全面複檢驗證系統
檢查所有回測數據的真實性、邏輯性和潛在漏洞
確保策略結果沒有過度美化或致命缺陷

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import glob
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_validation_folder():
    """創建驗證結果文件夾"""
    folder_name = "Strategy_Validation_Results"
    
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"✅ 創建驗證文件夾: {folder_name}")
    else:
        print(f"📁 驗證文件夾已存在: {folder_name}")
    
    return folder_name

def load_all_backtest_files():
    """載入所有回測文件進行驗證"""
    print("載入所有回測文件...")
    
    all_files = []
    
    # 1小時回測文件
    h1_files = glob.glob("1H_Strategy_Backtest/*_1H_CCB_TakerIntensity_Backtest_*.csv")
    for file in h1_files:
        coin = os.path.basename(file).split('_')[0]
        all_files.append({
            'file': file,
            'coin': coin,
            'timeframe': '1H',
            'type': 'backtest'
        })
    
    # 4小時回測文件
    h4_files = glob.glob("4H_Strategy_Backtest/*_4H_CCB_TakerIntensity_Backtest_*.csv")
    for file in h4_files:
        coin = os.path.basename(file).split('_')[0]
        all_files.append({
            'file': file,
            'coin': coin,
            'timeframe': '4H',
            'type': 'backtest'
        })
    
    # 日線回測文件 (從機構級回測)
    daily_files = glob.glob("Institutional_CCB_TakerIntensity_Backtest_*.csv")
    if daily_files:
        all_files.append({
            'file': daily_files[0],  # BTC日線
            'coin': 'BTC',
            'timeframe': 'Daily',
            'type': 'backtest'
        })
    
    # 多幣種日線回測文件
    multi_daily_files = glob.glob("*_CCB_TakerIntensity_Backtest_*.csv")
    for file in multi_daily_files:
        if 'Institutional' not in file:
            coin = os.path.basename(file).split('_')[0]
            all_files.append({
                'file': file,
                'coin': coin,
                'timeframe': 'Daily',
                'type': 'backtest'
            })
    
    print(f"✅ 找到 {len(all_files)} 個回測文件")
    return all_files

def validate_single_backtest(file_info):
    """驗證單個回測文件"""
    
    file_path = file_info['file']
    coin = file_info['coin']
    timeframe = file_info['timeframe']
    
    print(f"\n{'='*60}")
    print(f"驗證 {coin}-{timeframe} 回測結果")
    print(f"文件: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # 載入數據
        df = pd.read_csv(file_path)
        
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        validation_results = {
            'coin': coin,
            'timeframe': timeframe,
            'file': os.path.basename(file_path),
            'total_records': len(df),
            'validation_passed': True,
            'issues': []
        }
        
        # 1. 基本數據完整性檢查
        print("1. 基本數據完整性檢查...")
        
        required_columns = ['Close', 'Signal', 'pnl']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            validation_results['issues'].append(f"缺失關鍵列: {missing_columns}")
            validation_results['validation_passed'] = False
        
        # 檢查空值
        null_counts = df[required_columns].isnull().sum()
        if null_counts.sum() > len(df) * 0.1:  # 超過10%空值
            validation_results['issues'].append(f"過多空值: {null_counts.to_dict()}")
        
        # 2. 交易信號邏輯檢查
        print("2. 交易信號邏輯檢查...")
        
        if 'Signal' in df.columns:
            signal_values = df['Signal'].unique()
            valid_signals = [-1, 0, 1]
            
            invalid_signals = [s for s in signal_values if s not in valid_signals]
            if invalid_signals:
                validation_results['issues'].append(f"無效信號值: {invalid_signals}")
                validation_results['validation_passed'] = False
            
            # 檢查信號變化頻率
            signal_changes = (df['Signal'] != df['Signal'].shift(1)).sum()
            signal_change_rate = signal_changes / len(df)
            
            if signal_change_rate > 0.8:  # 信號變化過於頻繁
                validation_results['issues'].append(f"信號變化過於頻繁: {signal_change_rate:.2%}")
            
            validation_results['signal_change_rate'] = signal_change_rate
        
        # 3. 收益計算邏輯檢查
        print("3. 收益計算邏輯檢查...")
        
        if 'pnl' in df.columns and 'Close' in df.columns:
            # 重新計算收益驗證
            df['price_change'] = df['Close'].pct_change()
            df['calculated_pnl'] = df['Signal'].shift(1) * df['price_change']
            
            # 比較原始pnl和重新計算的pnl
            pnl_diff = (df['pnl'] - df['calculated_pnl']).abs()
            max_pnl_diff = pnl_diff.max()
            
            if max_pnl_diff > 0.001:  # 差異超過0.1%
                validation_results['issues'].append(f"PnL計算不一致，最大差異: {max_pnl_diff:.6f}")
                validation_results['validation_passed'] = False
            
            validation_results['max_pnl_diff'] = max_pnl_diff
        
        # 4. 績效指標合理性檢查
        print("4. 績效指標合理性檢查...")
        
        if 'pnl' in df.columns:
            pnl_series = df['pnl'].dropna()
            
            if len(pnl_series) > 0:
                # 計算基本績效指標
                total_return = (1 + pnl_series).prod() - 1
                volatility = pnl_series.std()
                sharpe_ratio = pnl_series.mean() / volatility if volatility > 0 else 0
                
                # 年化調整
                if timeframe == '1H':
                    sharpe_ratio *= np.sqrt(365 * 24)
                elif timeframe == '4H':
                    sharpe_ratio *= np.sqrt(365 * 6)
                elif timeframe == 'Daily':
                    sharpe_ratio *= np.sqrt(365)
                
                # 合理性檢查
                if sharpe_ratio > 10:  # 夏普比率過高可能有問題
                    validation_results['issues'].append(f"夏普比率異常高: {sharpe_ratio:.4f}")
                
                if abs(total_return) > 10:  # 總收益過高可能有問題
                    validation_results['issues'].append(f"總收益異常: {total_return:.2%}")
                
                validation_results['calculated_sharpe'] = sharpe_ratio
                validation_results['calculated_return'] = total_return
        
        # 5. 時間序列連續性檢查
        print("5. 時間序列連續性檢查...")
        
        if hasattr(df.index, 'to_series'):
            time_diffs = df.index.to_series().diff()
            
            if timeframe == '1H':
                expected_diff = pd.Timedelta(hours=1)
            elif timeframe == '4H':
                expected_diff = pd.Timedelta(hours=4)
            elif timeframe == 'Daily':
                expected_diff = pd.Timedelta(days=1)
            
            # 檢查時間間隔異常
            abnormal_gaps = time_diffs[time_diffs > expected_diff * 2]
            if len(abnormal_gaps) > len(df) * 0.05:  # 超過5%的異常間隔
                validation_results['issues'].append(f"時間序列有異常間隔: {len(abnormal_gaps)}個")
        
        # 6. 交易頻率合理性檢查
        print("6. 交易頻率合理性檢查...")
        
        if 'pnl' in df.columns:
            trading_periods = len(df[df['pnl'] != 0])
            trading_frequency = trading_periods / len(df)
            
            if trading_frequency > 0.95:  # 交易過於頻繁
                validation_results['issues'].append(f"交易頻率過高: {trading_frequency:.2%}")
            
            validation_results['trading_frequency'] = trading_frequency
        
        # 7. 價格數據合理性檢查
        print("7. 價格數據合理性檢查...")
        
        if 'Close' in df.columns:
            price_changes = df['Close'].pct_change()
            extreme_changes = price_changes[abs(price_changes) > 0.5]  # 單期變化超過50%
            
            if len(extreme_changes) > 0:
                validation_results['issues'].append(f"發現極端價格變化: {len(extreme_changes)}次")
        
        # 總結驗證結果
        if len(validation_results['issues']) == 0:
            print("✅ 所有檢查通過")
        else:
            print(f"⚠️ 發現 {len(validation_results['issues'])} 個問題:")
            for issue in validation_results['issues']:
                print(f"   - {issue}")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return {
            'coin': coin,
            'timeframe': timeframe,
            'file': os.path.basename(file_path),
            'validation_passed': False,
            'issues': [f"文件讀取錯誤: {str(e)}"]
        }

def generate_validation_report(all_validation_results, folder_name):
    """生成驗證報告"""
    
    print(f"\n{'='*80}")
    print("策略驗證綜合報告")
    print(f"{'='*80}")
    
    # 創建驗證結果DataFrame
    df_validation = pd.DataFrame(all_validation_results)
    
    # 統計分析
    total_files = len(all_validation_results)
    passed_files = len(df_validation[df_validation['validation_passed'] == True])
    failed_files = total_files - passed_files
    
    print(f"\n📊 驗證統計:")
    print(f"   總文件數: {total_files}")
    print(f"   通過驗證: {passed_files} ({passed_files/total_files:.1%})")
    print(f"   未通過驗證: {failed_files} ({failed_files/total_files:.1%})")
    
    # 按時框分析
    print(f"\n📈 按時框驗證結果:")
    for timeframe in df_validation['timeframe'].unique():
        tf_data = df_validation[df_validation['timeframe'] == timeframe]
        tf_passed = len(tf_data[tf_data['validation_passed'] == True])
        tf_total = len(tf_data)
        print(f"   {timeframe}: {tf_passed}/{tf_total} 通過 ({tf_passed/tf_total:.1%})")
    
    # 常見問題分析
    print(f"\n⚠️ 發現的問題:")
    all_issues = []
    for result in all_validation_results:
        all_issues.extend(result.get('issues', []))
    
    if all_issues:
        from collections import Counter
        issue_counts = Counter(all_issues)
        for issue, count in issue_counts.most_common():
            print(f"   {issue}: {count}次")
    else:
        print("   🎉 未發現任何問題！")
    
    # 保存詳細報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"{folder_name}/Validation_Report_{timestamp}.csv"
    df_validation.to_csv(report_filename, index=False)
    
    print(f"\n✅ 詳細驗證報告已保存: {report_filename}")
    
    return df_validation

def run_comprehensive_validation():
    """運行全面驗證"""
    
    print("="*80)
    print("CCB + TAKER INTENSITY 策略全面驗證系統")
    print("檢查所有回測數據的真實性和邏輯性")
    print("="*80)
    
    # 創建驗證文件夾
    folder_name = create_validation_folder()
    
    # 載入所有回測文件
    all_files = load_all_backtest_files()
    
    if not all_files:
        print("❌ 未找到任何回測文件")
        return None
    
    # 逐一驗證每個文件
    all_validation_results = []
    
    for file_info in all_files:
        validation_result = validate_single_backtest(file_info)
        all_validation_results.append(validation_result)
    
    # 生成綜合報告
    validation_df = generate_validation_report(all_validation_results, folder_name)
    
    # 最終結論
    passed_count = len(validation_df[validation_df['validation_passed'] == True])
    total_count = len(validation_df)
    
    print(f"\n🎯 驗證結論:")
    if passed_count == total_count:
        print("🎉 所有策略通過驗證！數據真實可靠！")
        print("✅ 策略確實是聖杯級別的發現！")
    elif passed_count >= total_count * 0.8:
        print("⚠️ 大部分策略通過驗證，但需要注意部分問題")
        print("🔧 建議修復發現的問題後再進行實盤")
    else:
        print("❌ 多數策略未通過驗證，存在嚴重問題")
        print("🛠️ 需要重新檢查策略邏輯和數據處理")
    
    return validation_df

if __name__ == "__main__":
    # 執行全面驗證
    validation_results = run_comprehensive_validation()

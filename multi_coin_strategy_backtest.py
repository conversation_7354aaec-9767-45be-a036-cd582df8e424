"""
多幣種策略回測系統
對ETH、SOL、XRP、DOGE、BNB、WIF進行CCB+Taker Intensity策略測試
使用真實數據，基於成功的BTC策略邏輯

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import glob
import warnings
warnings.filterwarnings('ignore')

# 導入策略邏輯
from institutional_backtest_system import (
    chip_concentration_bands, taker_intensity_signals, 
    ccb_taker_entry_logic, run_backtest_with_params
)

# 幣種映射
COIN_MAPPING = {
    'ETH': 'ethusdt',
    'SOL': 'solusdt', 
    'XRP': 'xrpusdt',
    'DOGE': 'dogeusdt',
    'BNB': 'bnbusdt',
    'WIF': 'wifusdt'
}

# BTC最優參數 (作為基準)
BTC_BEST_PARAMS = {
    'ccb_window': 25,
    'ccb_std': 1.5,
    'taker_lookback': 20,
    'taker_threshold': 50
}

def load_coin_data(coin_name):
    """載入單個幣種的真實數據"""
    
    coin_symbol = COIN_MAPPING[coin_name]
    pattern = f"real_{coin_symbol}_data_with_blave_indicators_*.csv"
    csv_files = glob.glob(pattern)
    
    if not csv_files:
        print(f"❌ 找不到 {coin_name} 的數據文件")
        return None
    
    # 使用最新文件
    latest_file = max(csv_files)
    print(f"載入 {coin_name} 數據: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ {coin_name} 數據載入完成：{len(df)}條記錄")
    print(f"   時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"   價格範圍：${df['Close'].min():.4f} - ${df['Close'].max():.4f}")
    
    return df

def test_single_coin_strategy(coin_name, params=None):
    """測試單個幣種的策略表現"""
    
    print(f"\n{'='*60}")
    print(f"測試 {coin_name} 策略表現")
    print(f"{'='*60}")
    
    # 載入數據
    df = load_coin_data(coin_name)
    if df is None:
        return None
    
    # 使用BTC最優參數或自定義參數
    if params is None:
        params = BTC_BEST_PARAMS.copy()
    
    print(f"使用參數: {params}")
    
    try:
        # 應用策略
        df = chip_concentration_bands(df, window=params['ccb_window'], std_dev=params['ccb_std'])
        df = taker_intensity_signals(df, lookback_period=params['taker_lookback'], percentile_threshold=params['taker_threshold'])
        df = ccb_taker_entry_logic(df)
        
        # 計算收益
        df['price_chg'] = df['Close'].pct_change()
        df['pnl'] = df['Signal'].shift(1) * df['price_chg']
        
        # 計算績效指標
        pnl = df['pnl'].dropna()
        
        if len(pnl) > 0:
            # 基本統計
            total_return = (1 + pnl).prod() - 1
            annual_return = (1 + total_return) ** (365 / len(pnl)) - 1
            volatility = pnl.std() * np.sqrt(365)
            sharpe_ratio = pnl.mean() / pnl.std() * np.sqrt(365) if pnl.std() > 0 else 0
            
            # 最大回撤
            cumulative = (1 + pnl).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 盈虧比
            profit_factor = pnl[pnl > 0].sum() / abs(pnl[pnl < 0].sum()) if (pnl < 0).any() else np.inf
            
            # 勝率
            win_rate = (pnl > 0).mean()
            
            # 交易次數
            total_trades = len(pnl[pnl != 0])
            
            performance = {
                'coin': coin_name,
                'total_return': total_return,
                'annual_return': annual_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'profit_factor': profit_factor,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'volatility': volatility
            }
            
            print(f"\n=== {coin_name} 策略績效 ===")
            print(f"總收益: {total_return:.2%}")
            print(f"年化收益: {annual_return:.2%}")
            print(f"夏普比率: {sharpe_ratio:.4f}")
            print(f"最大回撤: {max_drawdown:.2%}")
            print(f"盈虧比: {profit_factor:.2f}")
            print(f"勝率: {win_rate:.2%}")
            print(f"交易次數: {total_trades}")
            
            # 與BTC比較
            btc_sharpe = 2.4488  # BTC基準
            if sharpe_ratio >= 1.5:
                print(f"🎉 {coin_name} 達到目標！夏普比率 {sharpe_ratio:.4f} ≥ 1.5")
            else:
                print(f"⚠️ {coin_name} 未達目標：夏普比率 {sharpe_ratio:.4f} < 1.5")
            
            if sharpe_ratio > btc_sharpe:
                print(f"🚀 {coin_name} 表現超越BTC！({sharpe_ratio:.4f} > {btc_sharpe:.4f})")
            
            return performance, df
        
        else:
            print(f"❌ {coin_name} 無有效交易數據")
            return None
            
    except Exception as e:
        print(f"❌ {coin_name} 策略測試失敗: {e}")
        return None

def run_multi_coin_backtest():
    """運行多幣種策略回測"""
    
    print("="*80)
    print("多幣種CCB+Taker Intensity策略回測")
    print("測試幣種: ETH, SOL, XRP, DOGE, BNB, WIF")
    print("基於成功的BTC策略邏輯")
    print("="*80)
    
    all_results = []
    successful_coins = []
    
    for coin_name in COIN_MAPPING.keys():
        print(f"\n開始測試 {coin_name}...")
        
        result = test_single_coin_strategy(coin_name)
        
        if result is not None:
            performance, df = result
            all_results.append(performance)
            successful_coins.append(coin_name)
            
            # 保存個別結果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{coin_name}_CCB_TakerIntensity_Backtest_{timestamp}.csv"
            df.to_csv(filename)
            print(f"✅ {coin_name} 結果已保存: {filename}")
        
        print("-" * 40)
    
    # 生成綜合報告
    if all_results:
        generate_multi_coin_report(all_results)
        plot_multi_coin_comparison(all_results)
    
    return all_results

def generate_multi_coin_report(results):
    """生成多幣種綜合報告"""
    
    print("\n" + "="*80)
    print("多幣種策略綜合報告")
    print("="*80)
    
    # 創建結果DataFrame
    df_results = pd.DataFrame(results)
    
    # 按夏普比率排序
    df_results = df_results.sort_values('sharpe_ratio', ascending=False)
    
    print(f"\n📊 策略表現排名 (按夏普比率):")
    print("-" * 60)
    
    for i, row in df_results.iterrows():
        rank = df_results.index.get_loc(i) + 1
        coin = row['coin']
        sharpe = row['sharpe_ratio']
        total_ret = row['total_return']
        max_dd = row['max_drawdown']
        
        status = "🎉" if sharpe >= 1.5 else "⚠️"
        vs_btc = "🚀" if sharpe > 2.4488 else ""
        
        print(f"{rank}. {status} {coin}: 夏普={sharpe:.4f}, 總收益={total_ret:.2%}, 回撤={max_dd:.2%} {vs_btc}")
    
    # 統計分析
    print(f"\n📈 統計分析:")
    print(f"   測試幣種數: {len(results)}")
    print(f"   達標幣種數 (夏普≥1.5): {len(df_results[df_results['sharpe_ratio'] >= 1.5])}")
    print(f"   超越BTC幣種數 (夏普>2.45): {len(df_results[df_results['sharpe_ratio'] > 2.4488])}")
    print(f"   平均夏普比率: {df_results['sharpe_ratio'].mean():.4f}")
    print(f"   平均總收益: {df_results['total_return'].mean():.2%}")
    print(f"   平均最大回撤: {df_results['max_drawdown'].mean():.2%}")
    
    # 最佳表現
    best_coin = df_results.iloc[0]
    print(f"\n🏆 最佳表現幣種:")
    print(f"   {best_coin['coin']}: 夏普比率 {best_coin['sharpe_ratio']:.4f}")
    print(f"   總收益: {best_coin['total_return']:.2%}")
    print(f"   最大回撤: {best_coin['max_drawdown']:.2%}")
    
    # 保存綜合報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"Multi_Coin_Strategy_Report_{timestamp}.csv"
    df_results.to_csv(report_filename, index=False)
    print(f"\n✅ 綜合報告已保存: {report_filename}")
    
    return df_results

def plot_multi_coin_comparison(results):
    """繪製多幣種比較圖表"""
    
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 10
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 準備數據
    df_results = pd.DataFrame(results)
    df_results = df_results.sort_values('sharpe_ratio', ascending=False)
    
    coins = df_results['coin'].tolist()
    sharpe_ratios = df_results['sharpe_ratio'].tolist()
    total_returns = df_results['total_return'].tolist()
    max_drawdowns = df_results['max_drawdown'].tolist()
    win_rates = df_results['win_rate'].tolist()
    
    # 1. 夏普比率比較
    ax1 = axes[0, 0]
    colors = ['green' if s >= 1.5 else 'orange' if s >= 1.0 else 'red' for s in sharpe_ratios]
    bars1 = ax1.bar(coins, sharpe_ratios, color=colors, alpha=0.7)
    ax1.axhline(y=1.5, color='red', linestyle='--', alpha=0.7, label='Target (1.5)')
    ax1.axhline(y=2.4488, color='blue', linestyle='--', alpha=0.7, label='BTC (2.45)')
    ax1.set_title('Sharpe Ratio Comparison', fontweight='bold')
    ax1.set_ylabel('Sharpe Ratio')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加數值標籤
    for bar, value in zip(bars1, sharpe_ratios):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05, 
                f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 2. 總收益比較
    ax2 = axes[0, 1]
    bars2 = ax2.bar(coins, [r*100 for r in total_returns], color='skyblue', alpha=0.7)
    ax2.set_title('Total Return Comparison', fontweight='bold')
    ax2.set_ylabel('Total Return (%)')
    ax2.grid(True, alpha=0.3)
    
    # 添加數值標籤
    for bar, value in zip(bars2, total_returns):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, 
                f'{value:.1%}', ha='center', va='bottom', fontsize=9)
    
    # 3. 最大回撤比較
    ax3 = axes[1, 0]
    bars3 = ax3.bar(coins, [abs(d)*100 for d in max_drawdowns], color='lightcoral', alpha=0.7)
    ax3.set_title('Maximum Drawdown Comparison', fontweight='bold')
    ax3.set_ylabel('Max Drawdown (%)')
    ax3.grid(True, alpha=0.3)
    
    # 添加數值標籤
    for bar, value in zip(bars3, max_drawdowns):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{value:.1%}', ha='center', va='bottom', fontsize=9)
    
    # 4. 風險收益散點圖
    ax4 = axes[1, 1]
    scatter = ax4.scatter([abs(d)*100 for d in max_drawdowns], [r*100 for r in total_returns], 
                         c=sharpe_ratios, cmap='RdYlGn', s=100, alpha=0.7)
    
    # 添加幣種標籤
    for i, coin in enumerate(coins):
        ax4.annotate(coin, (abs(max_drawdowns[i])*100, total_returns[i]*100), 
                    xytext=(5, 5), textcoords='offset points', fontsize=9)
    
    ax4.set_title('Risk-Return Profile', fontweight='bold')
    ax4.set_xlabel('Max Drawdown (%)')
    ax4.set_ylabel('Total Return (%)')
    ax4.grid(True, alpha=0.3)
    
    # 添加顏色條
    cbar = plt.colorbar(scatter, ax=ax4)
    cbar.set_label('Sharpe Ratio')
    
    plt.tight_layout()
    
    # 保存圖表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Multi_Coin_Strategy_Comparison_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 多幣種比較圖表已保存: {filename}")

if __name__ == "__main__":
    # 執行多幣種策略回測
    results = run_multi_coin_backtest()
    
    if results:
        print(f"\n🎯 多幣種策略回測完成！")
        print(f"成功測試了 {len(results)} 個幣種")
    else:
        print(f"\n❌ 多幣種策略回測失敗")

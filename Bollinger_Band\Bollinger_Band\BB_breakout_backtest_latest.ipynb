{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "def bollinger_bands(df, column='Close', window=20, std_dev=2):\n", "    \"\"\"\n", "    Calculate Bollinger Bands.\n", "\n", "    Parameters:\n", "    df (pd.DataFrame): DataFrame containing stock data.\n", "    column (str): Column to calculate Bollinger Bands on (default 'Close').\n", "    window (int): Lookback period for SMA (default 20).\n", "    std_dev (int): Standard deviation multiplier (default 2).\n", "\n", "    Returns:\n", "    df (pd.DataFrame): DataFrame with 'BB_Middle', 'BB_Upper', and 'BB_Lower' columns.\n", "    \"\"\"\n", "    df['BB_Middle'] = df[column].rolling(window=window).mean()\n", "    df['BB_Upper'] = df['BB_Middle'] + (df[column].rolling(window=window).std() * std_dev)\n", "    df['BB_Lower'] = df['BB_Middle'] - (df[column].rolling(window=window).std() * std_dev)\n", "    \n", "    return df\n", "\n", "\n", "\n", "def bollinger_band_entry_logic(df):\n", "    \"\"\"\n", "    Generate entry signals based on Bollinger Bands.\n", "    If no new signal is generated, it carries forward the previous signal.\n", "\n", "    Parameters:\n", "    df (pd.DataFrame): DataFrame with Bollinger Bands calculated.\n", "\n", "    Returns:\n", "    df (pd.DataFrame): DataFrame with 'Signal' column.\n", "    \"\"\"\n", "    \n", "    df['Signal'] = 0  # De<PERSON>ult to no position\n", "\n", "    # Buy Signal (Long Entry) - Price crosses below Lower Band\n", "    df.loc[df['Close'] < df['BB_Lower'], 'Signal'] = 1  \n", "\n", "    # Sell Signal (Short Entry) - Price crosses above Upper Band\n", "    df.loc[df['Close'] > df['BB_Upper'], 'Signal'] = -1  \n", "\n", "    # **<PERSON><PERSON> Forward Previous Position**\n", "    df['Signal'] = df['Signal'].replace(0, np.nan).ffill().fillna(0) \n", "\n", "    return df\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def optimise_param_pf(df:pd.DataFrame, lookback:int,std_dev:float ):\n", "    best_pf = 0\n", "    best_lookback = -1\n", "    r = np.log(df['Close']).diff().shift(-1)\n", "    \n", "    for lookback in range(12,169):\n", "        for std_dev in np.arange(0.5,5,0.5):\n", "            BB = bollinger_bands(df, column= 'Close',window = lookback, std_dev=std_dev)\n", "            signal = bollinger_band_entry_logic(BB)['Signal']\n", "            df['price_chg']= df['Close'].pct_change()\n", "            df['pnl'] = signal.shift(1) * df['price_chg']\n", "            pnl = df['pnl']\n", "            pf = pnl[pnl>0].sum()/pnl[pnl<0].abs().sum()\n", "\n", "            if pf > best_pf:\n", "                best_pf = pf\n", "                best_lookback = lookback\n", "                best_std_dev = std_dev\n", "\n", "    return best_lookback, best_pf, best_std_dev"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def optimise_param_sr(df:pd.DataFrame, lookback:int,std_dev:float ):\n", "    best_sr = 0\n", "    best_lookback = -1\n", "    r = np.log(df['Close']).diff().shift(-1)\n", "    \n", "    for lookback in range(12,169):\n", "        for std_dev in np.arange(0.5,5,0.5):\n", "            BB = bollinger_bands(df, column= 'Close',window = lookback, std_dev=std_dev)\n", "            signal = bollinger_band_entry_logic(BB)['Signal']\n", "            df['price_chg']= df['Close'].pct_change()\n", "            df['pnl'] = signal.shift(1) * df['price_chg']\n", "            pnl = df['pnl']\n", "            sr = df['pnl'].mean() / df['pnl'].std() * np.sqrt(365)\n", "\n", "            if sr > best_sr:\n", "                best_sr = sr\n", "                best_lookback = lookback\n", "                best_std_dev = std_dev\n", "\n", "    return best_lookback, best_sr, best_std_dev"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_50147/3818232147.py:13: RuntimeWarning: invalid value encountered in scalar divide\n", "  sr = df['pnl'].mean() / df['pnl'].std() * np.sqrt(365)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Best Lookback: 21\n", "Best stdev: 3.5\n", "Best Sharpe Ratio: 0.904306429321887\n", "Profit Factor 1.166056619630942\n", "ROI% 6.128758106748866\n"]}], "source": ["#Base on BEST Sharpe Ratio\n", "if __name__ == '__main__':\n", "    # Load Data\n", "    df = pd.read_csv(\"./btc_price_data_1d.csv\")\n", "    df.set_index(\"time\", inplace=True)  # ✅ Corrected indexing\n", "\n", "    # Optimize Lookback\n", "    best_lookback, best_sr, best_std_dev = optimise_param_sr(df, lookback=20, std_dev=2.0)  # ✅ Fixed\n", "\n", "    df = bollinger_bands(df, column='Close', window=best_lookback, std_dev=best_std_dev)\n", "    # Apply Bollinger Band Entry Logic\n", "    df = bollinger_band_entry_logic(df)  # ✅ No need to assign it\n", "\n", "    \n", "\n", "    # Calculate Log Returns\n", "    df['price_chg'] = df['Close'].pct_change()\n", "\n", "    # Compute P&L using Yesterday's Signal for Today's Price Change\n", "    df['pnl'] = df['Signal'].shift(1) * df['price_chg']  # ✅ Fix applied\n", "\n", "    # Calculate and Plot Cumulative P&L\n", "    df['cumu_pnl'] = df['pnl'].cumsum()  # ✅ Store first\n", "   \n", "    sharpe_ratio = df['pnl'].mean() / df['pnl'].std() * np.sqrt(365)\n", "\n", "    pnl = df['pnl']\n", "    pf = pnl[pnl>0].sum()/pnl[pnl<0].abs().sum()\n", "    ROI = df['cumu_pnl'].iloc[-1] \n", "\n", "    # Create a figure and axis\n", "    fig, ax1 = plt.subplots(figsize=(12, 6))\n", "\n", "    # Plot cumulative PnL on the primary y-axis (left side)\n", "    ax1.plot(df.index, df['cumu_pnl'], label='Cumulative PnL', color='blue')\n", "    ax1.set_xlabel(\"Date\")\n", "    ax1.set_ylabel(\"Cumulative PnL\", color='blue')\n", "    ax1.tick_params(axis='y', labelcolor='blue')\n", "\n", "    # Create a secondary y-axis for the Close price\n", "    ax2 = ax1.twinx()\n", "    ax2.plot(df.index, df['Close'], label='Close Price', color='red', alpha=0.7)\n", "    ax2.set_ylabel(\"Close Price\", color='red')\n", "    ax2.tick_params(axis='y', labelcolor='red')\n", "\n", "    # Add title and legend\n", "    fig.suptitle(\"Cumulative PnL & Close Price Over Time\")\n", "    fig.legend(loc=\"upper left\", bbox_to_anchor=(0.1, 0.9))\n", "\n", "    plt.show()\n", "    # Print Optimization Results\n", "    print(\"Best Lookback:\", best_lookback)\n", "    print(\"Best stdev:\", best_std_dev)\n", "    print(\"Best Sharpe Ratio:\", best_sr)\n", "    print(\"Profit Factor\",pf)\n", "    print(\"ROI%\", ROI)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_41241/3840525453.py:13: RuntimeWarning: invalid value encountered in scalar divide\n", "  pf = pnl[pnl>0].sum()/pnl[pnl<0].abs().sum()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Best Lookback: 21\n", "Best stdev: 3.5\n", "Best Profit Factor: 1.166056619630942\n", "Sharpe Ratio 0.904306429321887\n", "ROI% 6.128758106748866\n"]}], "source": ["#Base on BEST profit factor\n", "if __name__ == '__main__':\n", "    # Load Data\n", "    df = pd.read_csv(\"/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data/BTC/btc_price_data_1d.csv\")\n", "    df.set_index(\"time\", inplace=True)  # ✅ Corrected indexing\n", "\n", "    # Optimize Lookback\n", "    best_lookback, best_pf, best_std_dev = optimise_param_pf(df, lookback=20, std_dev=2.0)  # ✅ Fixed\n", "\n", "    df = bollinger_bands(df, column='Close', window=best_lookback, std_dev=best_std_dev)\n", "    # Apply Bollinger Band Entry Logic\n", "    df = bollinger_band_entry_logic(df)  # ✅ No need to assign it\n", "\n", "    \n", "\n", "    # Calculate Log Returns\n", "    df['price_chg'] = df['Close'].pct_change()\n", "\n", "    # Compute P&L using Yesterday's Signal for Today's Price Change\n", "    df['pnl'] = df['Signal'].shift(1) * df['price_chg']  # ✅ Fix applied\n", "\n", "    # Calculate and Plot Cumulative P&L\n", "    df['cumu_pnl'] = df['pnl'].cumsum()  # ✅ Store first\n", "   \n", "    sharpe_ratio = df['pnl'].mean() / df['pnl'].std() * np.sqrt(365)\n", "    ROI = df['cumu_pnl'].iloc[-1] \n", "\n", "\n", "    # Create a figure and axis\n", "    fig, ax1 = plt.subplots(figsize=(12, 6))\n", "\n", "    # Plot cumulative PnL on the primary y-axis (left side)\n", "    ax1.plot(df.index, df['cumu_pnl'], label='Cumulative PnL', color='blue')\n", "    ax1.set_xlabel(\"Date\")\n", "    ax1.set_ylabel(\"Cumulative PnL\", color='blue')\n", "    ax1.tick_params(axis='y', labelcolor='blue')\n", "\n", "    # Create a secondary y-axis for the Close price\n", "    ax2 = ax1.twinx()\n", "    ax2.plot(df.index, df['Close'], label='Close Price', color='red', alpha=0.7)\n", "    ax2.set_ylabel(\"Close Price\", color='red')\n", "    ax2.tick_params(axis='y', labelcolor='red')\n", "\n", "    # Add title and legend\n", "    fig.suptitle(\"Cumulative PnL & Close Price Over Time\")\n", "    fig.legend(loc=\"upper left\", bbox_to_anchor=(0.1, 0.9))\n", "\n", "    plt.show()\n", "    # Print Optimization Results\n", "    print(\"Best Lookback:\", best_lookback)\n", "    print(\"Best stdev:\", best_std_dev)\n", "    print(\"Best Profit Factor:\", best_pf)\n", "    print(\"Sharpe Ratio\",sharpe_ratio)\n", "    print(\"ROI%\", ROI)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"./BB_breakout_backtest.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
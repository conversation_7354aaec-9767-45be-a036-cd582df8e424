"""
獲取真實的BTC數據
使用正確的時間範圍和價格數據

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_btc_data_from_binance(symbol='BTCUSDT', interval='4h', days_back=730):
    """
    從Binance獲取BTC數據（免費API，無需認證）
    
    Parameters:
    symbol (str): 交易對
    interval (str): 時間間隔
    days_back (int): 回溯天數
    
    Returns:
    pd.DataFrame: BTC價格數據
    """
    print(f"正在從Binance獲取{symbol} {interval}數據...")
    
    # Binance API端點
    base_url = "https://api.binance.com/api/v3/klines"
    
    # 計算時間範圍
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days_back)
    
    # 轉換為毫秒時間戳
    start_timestamp = int(start_time.timestamp() * 1000)
    end_timestamp = int(end_time.timestamp() * 1000)
    
    all_data = []
    current_start = start_timestamp
    limit = 1000  # Binance API限制
    
    # 計算每次請求的時間間隔（毫秒）
    interval_ms = {
        '1m': 60 * 1000,
        '3m': 3 * 60 * 1000,
        '5m': 5 * 60 * 1000,
        '15m': 15 * 60 * 1000,
        '30m': 30 * 60 * 1000,
        '1h': 60 * 60 * 1000,
        '2h': 2 * 60 * 60 * 1000,
        '4h': 4 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '8h': 8 * 60 * 60 * 1000,
        '12h': 12 * 60 * 60 * 1000,
        '1d': 24 * 60 * 60 * 1000,
        '3d': 3 * 24 * 60 * 60 * 1000,
        '1w': 7 * 24 * 60 * 60 * 1000,
        '1M': 30 * 24 * 60 * 60 * 1000
    }
    
    step_size = interval_ms[interval] * limit
    
    while current_start < end_timestamp:
        current_end = min(current_start + step_size, end_timestamp)
        
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': current_start,
            'endTime': current_end,
            'limit': limit
        }
        
        try:
            response = requests.get(base_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data:
                all_data.extend(data)
                print(f"已獲取 {len(all_data)} 條數據...")
            
            current_start = current_end + interval_ms[interval]
            time.sleep(0.1)  # 避免API限制
            
        except Exception as e:
            print(f"API請求錯誤: {e}")
            break
    
    if not all_data:
        print("無法獲取數據")
        return None
    
    # 處理數據
    df = pd.DataFrame(all_data, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
    ])
    
    # 轉換數據類型
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = pd.to_numeric(df[col])
    
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df = df.drop_duplicates(subset=['timestamp'])
    df = df.sort_values('timestamp')
    
    # 只保留需要的列
    df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
    
    print(f"BTC數據獲取完成：{len(df)}條記錄")
    print(f"時間範圍：{df['timestamp'].min()} 至 {df['timestamp'].max()}")
    print(f"價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    
    return df

def generate_realistic_chip_concentration_v2(price_df):
    """
    基於真實價格數據生成合理的籌碼集中度
    
    Parameters:
    price_df (pd.DataFrame): 真實價格數據
    
    Returns:
    pd.DataFrame: 包含籌碼集中度的數據
    """
    print("基於真實價格生成籌碼集中度數據...")
    
    df = price_df.copy()
    
    # 計算技術指標
    df['returns'] = df['close'].pct_change()
    df['volatility'] = df['returns'].rolling(24).std().fillna(0.02)
    df['price_ma20'] = df['close'].rolling(20).mean()
    df['price_position'] = (df['close'] - df['price_ma20']) / df['price_ma20']
    
    # 計算RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 計算成交量指標
    df['volume_ma'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    np.random.seed(42)
    
    # 初始化籌碼集中度
    concentration = []
    base_level = 0.5  # 基礎集中度水平
    
    for i in range(len(df)):
        if i == 0:
            concentration.append(base_level)
            continue
        
        prev_conc = concentration[-1]
        
        # 1. 價格變化影響（大漲時籌碼分散，大跌時籌碼集中）
        price_change = df.iloc[i]['returns']
        if not pd.isna(price_change):
            if price_change > 0.05:  # 大漲5%以上
                price_impact = -0.02  # 籌碼分散
            elif price_change < -0.05:  # 大跌5%以上
                price_impact = 0.02   # 籌碼集中
            else:
                price_impact = -price_change * 0.3  # 一般情況
        else:
            price_impact = 0
        
        # 2. 波動率影響（高波動時變化更劇烈）
        vol = df.iloc[i]['volatility']
        if not pd.isna(vol):
            vol_multiplier = 1 + vol * 10  # 波動率放大器
        else:
            vol_multiplier = 1
        
        # 3. RSI影響
        rsi = df.iloc[i]['rsi']
        if not pd.isna(rsi):
            if rsi > 80:  # 極度超買
                rsi_impact = 0.015  # 籌碼集中
            elif rsi < 20:  # 極度超賣
                rsi_impact = -0.015  # 籌碼分散
            elif rsi > 70:
                rsi_impact = 0.005
            elif rsi < 30:
                rsi_impact = -0.005
            else:
                rsi_impact = 0
        else:
            rsi_impact = 0
        
        # 4. 成交量影響（異常成交量時籌碼變化更大）
        vol_ratio = df.iloc[i]['volume_ratio']
        if not pd.isna(vol_ratio):
            if vol_ratio > 2:  # 成交量異常放大
                volume_impact = 0.01
            elif vol_ratio < 0.5:  # 成交量萎縮
                volume_impact = -0.005
            else:
                volume_impact = 0
        else:
            volume_impact = 0
        
        # 5. 趨勢影響（長期趨勢對籌碼分佈的影響）
        price_pos = df.iloc[i]['price_position']
        if not pd.isna(price_pos):
            if price_pos > 0.1:  # 價格明顯高於均線
                trend_impact = -0.002  # 籌碼逐漸分散
            elif price_pos < -0.1:  # 價格明顯低於均線
                trend_impact = 0.002   # 籌碼逐漸集中
            else:
                trend_impact = 0
        else:
            trend_impact = 0
        
        # 6. 隨機噪音
        noise = np.random.normal(0, 0.01)
        
        # 7. 均值回歸（防止極端值）
        mean_reversion = (0.5 - prev_conc) * 0.01
        
        # 計算總變化
        total_change = (price_impact + rsi_impact + volume_impact + trend_impact) * vol_multiplier + noise + mean_reversion
        
        # 更新籌碼集中度
        new_conc = prev_conc + total_change
        new_conc = np.clip(new_conc, 0.1, 0.9)  # 限制範圍
        
        concentration.append(new_conc)
    
    df['concentration'] = concentration
    
    # 添加一些市場事件（基於真實價格變化）
    # 找出價格劇烈變化的時點
    df['price_change_abs'] = abs(df['returns'])
    extreme_changes = df.nlargest(20, 'price_change_abs').index
    
    for idx in extreme_changes:
        if idx < len(df) - 5:
            change_magnitude = df.loc[idx, 'returns']
            if change_magnitude > 0.08:  # 大漲
                # 籌碼分散事件
                for j in range(5):
                    if idx + j < len(df):
                        df.iloc[idx + j, df.columns.get_loc('concentration')] *= 0.8
            elif change_magnitude < -0.08:  # 大跌
                # 籌碼集中事件
                for j in range(5):
                    if idx + j < len(df):
                        df.iloc[idx + j, df.columns.get_loc('concentration')] = min(0.9, df.iloc[idx + j, df.columns.get_loc('concentration')] * 1.2)
    
    # 再次限制範圍
    df['concentration'] = np.clip(df['concentration'], 0.1, 0.9)
    
    print(f"籌碼集中度範圍：{df['concentration'].min():.3f} - {df['concentration'].max():.3f}")
    print(f"籌碼集中度平均值：{df['concentration'].mean():.3f}")
    print(f"籌碼集中度標準差：{df['concentration'].std():.3f}")
    
    return df

def save_market_data():
    """獲取並保存市場數據"""
    
    print("="*60)
    print("獲取真實BTC市場數據")
    print("="*60)
    
    # 1. 獲取BTC價格數據
    btc_data = get_btc_data_from_binance(symbol='BTCUSDT', interval='4h', days_back=730)
    
    if btc_data is None:
        print("無法獲取BTC數據")
        return None
    
    # 2. 生成籌碼集中度數據
    market_data = generate_realistic_chip_concentration_v2(btc_data)
    
    # 3. 保存數據
    filename = f"btc_4h_with_concentration_{datetime.now().strftime('%Y%m%d')}.csv"
    market_data.to_csv(filename, index=False)
    print(f"\n數據已保存到: {filename}")
    
    # 4. 數據驗證
    print(f"\n=== 數據驗證 ===")
    print(f"總記錄數: {len(market_data)}")
    print(f"時間範圍: {market_data['timestamp'].min()} 至 {market_data['timestamp'].max()}")
    print(f"BTC價格範圍: ${market_data['close'].min():.0f} - ${market_data['close'].max():.0f}")
    print(f"籌碼集中度範圍: {market_data['concentration'].min():.3f} - {market_data['concentration'].max():.3f}")
    
    # 檢查數據質量
    print(f"\n=== 數據質量檢查 ===")
    print(f"缺失值: {market_data.isnull().sum().sum()}")
    print(f"重複時間戳: {market_data['timestamp'].duplicated().sum()}")
    
    return market_data

if __name__ == "__main__":
    # 獲取並保存真實市場數據
    data = save_market_data()

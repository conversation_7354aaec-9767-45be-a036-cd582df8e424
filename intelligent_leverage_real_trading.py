"""
智能槓桿實盤模擬系統
測試智能槓桿在100U本金實盤環境下的表現

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入實盤模擬函數
from real_trading_simulation_100u import calculate_real_trading_performance

def create_intelligent_leverage_folder():
    """創建智能槓桿測試文件夾"""
    folder_name = "智能槓桿實盤表現"
    
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"✅ 創建智能槓桿文件夾: {folder_name}")
    else:
        print(f"📁 智能槓桿文件夾已存在: {folder_name}")
    
    return folder_name

def test_intelligent_leverage_all_strategies():
    """測試所有策略的智能槓桿表現"""
    
    print("="*80)
    print("智能槓桿實盤模擬系統")
    print("100U本金 + 1%倉位 + 智能槓桿(5-15倍)")
    print("="*80)
    
    folder_name = create_intelligent_leverage_folder()
    
    all_results = []
    
    # 1. 處理1小時策略
    print("\n處理1小時策略 (智能槓桿)...")
    h1_files = glob.glob("1H_Strategy_Backtest/*_1H_CCB_TakerIntensity_Backtest_*.csv")
    
    for file in h1_files:
        coin = os.path.basename(file).split('_')[0]
        
        try:
            df = pd.read_csv(file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, coin, '1H', use_intelligent_leverage=True)
            
            if result is not None:
                performance, df_result = result
                performance['leverage_type'] = 'Intelligent'
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/{coin}_1H_IntelligentLeverage_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ {coin}-1H 智能槓桿結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ {coin}-1H 智能槓桿處理失敗: {e}")
    
    # 2. 處理4小時策略
    print("\n處理4小時策略 (智能槓桿)...")
    h4_files = glob.glob("4H_Strategy_Backtest/*_4H_CCB_TakerIntensity_Backtest_*.csv")
    
    for file in h4_files:
        coin = os.path.basename(file).split('_')[0]
        
        try:
            df = pd.read_csv(file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, coin, '4H', use_intelligent_leverage=True)
            
            if result is not None:
                performance, df_result = result
                performance['leverage_type'] = 'Intelligent'
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/{coin}_4H_IntelligentLeverage_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ {coin}-4H 智能槓桿結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ {coin}-4H 智能槓桿處理失敗: {e}")
    
    # 3. 處理日線策略
    print("\n處理日線策略 (智能槓桿)...")
    
    # BTC日線
    btc_daily_files = glob.glob("Institutional_CCB_TakerIntensity_Backtest_*.csv")
    if btc_daily_files:
        try:
            df = pd.read_csv(btc_daily_files[0])
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, 'BTC', 'Daily', use_intelligent_leverage=True)
            
            if result is not None:
                performance, df_result = result
                performance['leverage_type'] = 'Intelligent'
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/BTC_Daily_IntelligentLeverage_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ BTC-Daily 智能槓桿結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ BTC-Daily 智能槓桿處理失敗: {e}")
    
    # 其他幣種日線
    daily_files = glob.glob("*_CCB_TakerIntensity_Backtest_*.csv")
    for file in daily_files:
        if 'Institutional' not in file and 'Multi_Coin' not in file:
            coin = os.path.basename(file).split('_')[0]
            
            try:
                df = pd.read_csv(file)
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)
                
                result = calculate_real_trading_performance(df, coin, 'Daily', use_intelligent_leverage=True)
                
                if result is not None:
                    performance, df_result = result
                    performance['leverage_type'] = 'Intelligent'
                    all_results.append(performance)
                    
                    # 保存結果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{folder_name}/{coin}_Daily_IntelligentLeverage_100U_{timestamp}.csv"
                    df_result.to_csv(filename)
                    print(f"✅ {coin}-Daily 智能槓桿結果已保存: {filename}")
            
            except Exception as e:
                print(f"❌ {coin}-Daily 智能槓桿處理失敗: {e}")
    
    return all_results, folder_name

def compare_leverage_systems():
    """比較固定槓桿vs智能槓桿"""
    
    print(f"\n{'='*80}")
    print("槓桿系統比較分析")
    print(f"{'='*80}")
    
    # 載入固定槓桿結果
    fixed_leverage_file = glob.glob("本金100U的實盤表現/RealTrading_100U_Report_*.csv")
    if not fixed_leverage_file:
        print("❌ 找不到固定槓桿結果")
        return None
    
    fixed_df = pd.read_csv(fixed_leverage_file[0])
    fixed_df['leverage_type'] = 'Fixed'
    
    # 載入智能槓桿結果
    intelligent_results, folder_name = test_intelligent_leverage_all_strategies()
    
    if not intelligent_results:
        print("❌ 智能槓桿測試失敗")
        return None
    
    intelligent_df = pd.DataFrame(intelligent_results)
    
    # 合併結果
    combined_df = pd.concat([fixed_df, intelligent_df], ignore_index=True)
    
    # 生成比較報告
    print(f"\n📊 槓桿系統比較結果:")
    print("-" * 80)
    
    # 按幣種和時框比較
    for coin in combined_df['coin'].unique():
        for timeframe in combined_df['timeframe'].unique():
            coin_tf_data = combined_df[(combined_df['coin'] == coin) & (combined_df['timeframe'] == timeframe)]
            
            if len(coin_tf_data) == 2:  # 有固定和智能兩種結果
                fixed_result = coin_tf_data[coin_tf_data['leverage_type'] == 'Fixed'].iloc[0]
                intelligent_result = coin_tf_data[coin_tf_data['leverage_type'] == 'Intelligent'].iloc[0]
                
                improvement = intelligent_result['total_return'] - fixed_result['total_return']
                
                status = "🎉" if improvement > 0 else "❌"
                
                print(f"{status} {coin}-{timeframe}:")
                print(f"   固定槓桿: ${fixed_result['final_capital']:.2f} ({fixed_result['total_return']:+.2%})")
                print(f"   智能槓桿: ${intelligent_result['final_capital']:.2f} ({intelligent_result['total_return']:+.2%})")
                print(f"   改進幅度: {improvement:+.2%}")
                print()
    
    # 統計分析
    fixed_stats = fixed_df.groupby('leverage_type').agg({
        'total_return': ['mean', 'std', 'count'],
        'final_capital': 'mean'
    }).round(4)
    
    intelligent_stats = intelligent_df.groupby('leverage_type').agg({
        'total_return': ['mean', 'std', 'count'],
        'final_capital': 'mean'
    }).round(4)
    
    print(f"📈 統計比較:")
    print(f"固定槓桿 (10倍):")
    print(f"   平均收益率: {fixed_df['total_return'].mean():.2%}")
    print(f"   平均最終本金: ${fixed_df['final_capital'].mean():.2f}")
    print(f"   盈利策略數: {len(fixed_df[fixed_df['total_return'] > 0])}/{len(fixed_df)}")
    
    print(f"\n智能槓桿 (5-15倍):")
    print(f"   平均收益率: {intelligent_df['total_return'].mean():.2%}")
    print(f"   平均最終本金: ${intelligent_df['final_capital'].mean():.2f}")
    print(f"   盈利策略數: {len(intelligent_df[intelligent_df['total_return'] > 0])}/{len(intelligent_df)}")
    
    # 保存比較報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    comparison_filename = f"{folder_name}/Leverage_Comparison_Report_{timestamp}.csv"
    combined_df.to_csv(comparison_filename, index=False)
    
    print(f"\n✅ 槓桿比較報告已保存: {comparison_filename}")
    
    return combined_df

if __name__ == "__main__":
    # 執行智能槓桿比較測試
    comparison_results = compare_leverage_systems()
    
    if comparison_results is not None:
        print(f"\n🎯 智能槓桿測試完成！")
    else:
        print(f"\n❌ 智能槓桿測試失敗")

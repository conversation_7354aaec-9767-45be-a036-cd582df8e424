"""
籌碼集中帶突破策略 - 快速測試版
使用較小的搜索空間快速驗證增強版策略效果

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入增強版策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, ccb_entry_logic_enhanced, 
    calculate_performance_metrics
)

def load_real_market_data():
    """載入真實市場數據"""
    print("載入真實BTC市場數據...")
    
    # 查找最新的數據文件
    import glob
    csv_files = glob.glob("btc_4h_with_concentration_*.csv")
    
    if not csv_files:
        print("錯誤：找不到BTC數據文件")
        return None
    
    # 使用最新的文件
    latest_file = max(csv_files)
    print(f"載入數據文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # 清理數據
    df = df.dropna()
    
    print(f"數據載入完成：{len(df)}條記錄")
    print(f"時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"BTC價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    
    return df

def quick_optimize_parameters(df):
    """快速參數優化"""
    print("執行快速參數優化...")
    
    # 預定義的參數組合（基於經驗選擇）
    param_combinations = [
        {'window': 20, 'std_dev': 2.0, 'atr_multiplier': 1.5, 'ema_period': 50, 'adx_threshold': 25},
        {'window': 15, 'std_dev': 1.8, 'atr_multiplier': 1.2, 'ema_period': 40, 'adx_threshold': 20},
        {'window': 25, 'std_dev': 2.2, 'atr_multiplier': 1.8, 'ema_period': 60, 'adx_threshold': 30},
        {'window': 18, 'std_dev': 2.0, 'atr_multiplier': 1.3, 'ema_period': 45, 'adx_threshold': 22},
        {'window': 22, 'std_dev': 1.9, 'atr_multiplier': 1.6, 'ema_period': 55, 'adx_threshold': 28},
        {'window': 16, 'std_dev': 2.1, 'atr_multiplier': 1.4, 'ema_period': 35, 'adx_threshold': 24},
        {'window': 24, 'std_dev': 1.7, 'atr_multiplier': 1.7, 'ema_period': 50, 'adx_threshold': 26},
        {'window': 14, 'std_dev': 2.3, 'atr_multiplier': 1.1, 'ema_period': 42, 'adx_threshold': 21}
    ]
    
    best_score = -np.inf
    best_params = None
    results = []
    
    for i, params in enumerate(param_combinations):
        print(f"測試參數組合 {i+1}/{len(param_combinations)}: {params}")
        
        try:
            # 計算CCB指標和信號
            df_temp = df.copy()
            df_temp = chip_concentration_bands(
                df_temp, 
                window=params['window'], 
                std_dev=params['std_dev']
            )
            
            df_temp = ccb_entry_logic_enhanced(
                df_temp,
                atr_multiplier=params['atr_multiplier'],
                ema_trend_period=params['ema_period'],
                adx_threshold=params['adx_threshold']
            )
            
            # 計算收益
            df_temp['price_change'] = df_temp['close'].pct_change()
            df_temp['strategy_return'] = df_temp['Signal'].shift(1) * df_temp['price_change']
            
            # 計算績效指標
            returns = df_temp['strategy_return'].dropna()
            
            if len(returns) > 20 and returns.std() > 0:
                total_return = (1 + returns).prod() - 1
                annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1
                volatility = returns.std() * np.sqrt(365*6)
                sharpe = annual_return / volatility if volatility > 0 else 0
                
                # 計算最大回撤
                cumulative = (1 + returns).cumprod()
                rolling_max = cumulative.expanding().max()
                drawdown = (cumulative - rolling_max) / rolling_max
                max_drawdown = drawdown.min()
                
                # 計算勝率
                win_rate = (returns > 0).mean()
                
                result = {
                    **params,
                    'sharpe': sharpe,
                    'annual_return': annual_return,
                    'max_drawdown': max_drawdown,
                    'win_rate': win_rate,
                    'total_trades': len(returns[returns != 0])
                }
                
                results.append(result)
                
                print(f"  夏普比率: {sharpe:.4f}, 年化收益: {annual_return:.2%}, 最大回撤: {max_drawdown:.2%}")
                
                if sharpe > best_score:
                    best_score = sharpe
                    best_params = result
        
        except Exception as e:
            print(f"  參數組合失敗: {e}")
            continue
    
    print(f"\n快速優化完成！最佳夏普比率: {best_score:.4f}")
    return best_params, results

def run_quick_test():
    """運行快速測試"""
    
    print("="*60)
    print("籌碼集中帶突破策略 - 快速測試")
    print("="*60)
    
    # 1. 載入數據
    market_data = load_real_market_data()
    if market_data is None:
        return None
    
    # 2. 數據分割
    split_point = int(len(market_data) * 0.75)
    train_data = market_data.iloc[:split_point].copy()
    test_data = market_data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄")
    print(f"測試數據：{len(test_data)}條記錄")
    
    # 3. 快速參數優化
    print("\n階段1：快速參數優化")
    print("-" * 40)
    
    best_params, optimization_results = quick_optimize_parameters(train_data)
    
    if best_params is None:
        print("優化失敗")
        return None
    
    print(f"\n最優參數: {best_params}")
    
    # 顯示所有結果
    if optimization_results:
        sorted_results = sorted(optimization_results, key=lambda x: x['sharpe'], reverse=True)
        print(f"\n所有測試結果 (按夏普比率排序):")
        for i, result in enumerate(sorted_results):
            print(f"{i+1}. 夏普={result['sharpe']:.4f}, 年化收益={result['annual_return']:.2%}, "
                  f"最大回撤={result['max_drawdown']:.2%}, 勝率={result['win_rate']:.2%}")
    
    # 4. 樣本外測試
    print("\n階段2：樣本外測試")
    print("-" * 40)
    
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_entry_logic_enhanced(
        test_df,
        atr_multiplier=best_params['atr_multiplier'],
        ema_trend_period=best_params['ema_period'],
        adx_threshold=best_params['adx_threshold']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 樣本外測試績效 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 檢查目標達成情況
    try:
        sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
        if sharpe_ratio >= 1.5:
            print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
            
            # 分析原因
            win_rate = float(performance.get('Win Rate', '0%').replace('%', ''))
            max_drawdown = float(performance.get('Max Drawdown', '0%').replace('%', ''))
            
            print("\n改進建議:")
            if win_rate < 50:
                print(f"- 勝率 ({win_rate:.1f}%) 偏低，需要提高信號質量")
            if abs(max_drawdown) > 20:
                print(f"- 最大回撤 ({max_drawdown:.1f}%) 過大，需要加強風險控制")
            if sharpe_ratio < 0:
                print("- 策略整體虧損，需要重新設計邏輯")
    except:
        print("無法解析Sharpe Ratio")
    
    # 5. 簡單可視化
    print("\n階段3：結果可視化")
    print("-" * 40)
    
    plot_quick_results(test_df, best_params, performance)
    
    return test_df, best_params, performance

def plot_quick_results(df, params, performance):
    """繪製快速測試結果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    
    # 1. 價格和信號
    axes[0, 0].plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        axes[0, 0].scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=30, label='Buy')
    if len(sell_signals) > 0:
        axes[0, 0].scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=30, label='Sell')
    
    axes[0, 0].set_title('增強版策略交易信號')
    axes[0, 0].set_ylabel('Price (USD)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 累積收益
    axes[0, 1].plot(df.index, df['cumulative_strategy'], label='策略', color='blue', linewidth=2)
    axes[0, 1].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 回撤分析
    strategy_cumulative = df['cumulative_strategy']
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    axes[1, 0].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')
    axes[1, 0].plot(df.index, drawdown, color='red', linewidth=1)
    axes[1, 0].set_title('策略回撤分析')
    axes[1, 0].set_ylabel('回撤幅度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 績效總結
    axes[1, 1].axis('off')
    
    perf_text = "=== 快速測試結果 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        if key not in ['sharpe', 'annual_return', 'max_drawdown', 'win_rate', 'total_trades']:
            perf_text += f"{key}: {value}\n"
    
    axes[1, 1].text(0.1, 0.9, perf_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("快速測試圖表已生成完成！")

if __name__ == "__main__":
    # 執行快速測試
    results = run_quick_test()

"""
100U本金實盤模擬系統
每筆交易使用1%本金 + 10倍槓桿
模擬真實交易環境，包含手續費、滑點等

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_real_trading_folder():
    """創建實盤模擬文件夾"""
    folder_name = "本金100U的實盤表現"
    
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"✅ 創建實盤模擬文件夾: {folder_name}")
    else:
        print(f"📁 實盤模擬文件夾已存在: {folder_name}")
    
    return folder_name

def calculate_real_trading_performance(df, coin, timeframe, initial_capital=100, use_intelligent_leverage=False):
    """
    計算真實交易表現

    參數:
    - initial_capital: 初始本金 (100U)
    - position_size: 每筆交易使用本金的1%
    - leverage: 10倍槓桿 (或智能槓桿)
    - trading_fee: 0.1% 手續費 (開倉+平倉 = 0.2%)
    - slippage: 0.05% 滑點
    - use_intelligent_leverage: 是否使用智能槓桿
    """
    
    print(f"計算 {coin}-{timeframe} 實盤表現...")
    
    # 交易參數
    position_ratio = 0.01  # 每筆交易使用1%本金
    base_leverage = 10  # 基礎10倍槓桿
    trading_fee = 0.001  # 0.1% 手續費
    slippage = 0.0005  # 0.05% 滑點

    # 智能槓桿參數
    if use_intelligent_leverage:
        # 計算波動率指標
        df['returns'] = df['Close'].pct_change()
        df['volatility'] = df['returns'].rolling(window=20).std()
        df['vol_percentile'] = df['volatility'].rolling(window=60).apply(
            lambda x: (x.iloc[-1] <= x).mean() if len(x) > 0 else 0.5
        )
    
    # 初始化
    df = df.copy()
    df['capital'] = initial_capital  # 當前本金
    df['position_size'] = 0  # 倉位大小
    df['pnl_real'] = 0  # 真實PnL
    df['fees'] = 0  # 手續費
    df['cumulative_capital'] = initial_capital  # 累積本金
    
    current_capital = initial_capital
    current_position = 0  # 當前倉位 (0=無倉位, 1=多頭, -1=空頭)
    entry_price = 0
    position_value = 0
    
    for i in range(1, len(df)):
        signal = df.iloc[i]['Signal']
        price = df.iloc[i]['Close']
        prev_signal = df.iloc[i-1]['Signal']
        
        # 計算實際交易價格 (考慮滑點)
        if signal > 0:  # 買入
            actual_price = price * (1 + slippage)
        elif signal < 0:  # 賣出
            actual_price = price * (1 - slippage)
        else:
            actual_price = price
        
        pnl_this_period = 0
        fees_this_period = 0
        
        # 平倉邏輯
        if current_position != 0 and signal != current_position:
            # 計算平倉PnL
            if current_position == 1:  # 平多頭
                price_change = (actual_price - entry_price) / entry_price
            else:  # 平空頭
                price_change = (entry_price - actual_price) / entry_price
            
            # 計算實際盈虧 (考慮槓桿)
            position_pnl = position_value * price_change * leverage
            
            # 扣除平倉手續費
            close_fee = position_value * leverage * trading_fee
            
            # 總PnL
            pnl_this_period = position_pnl - close_fee
            fees_this_period += close_fee
            
            # 更新本金
            current_capital += pnl_this_period
            
            # 重置倉位
            current_position = 0
            position_value = 0
        
        # 開倉邏輯
        if signal != 0 and current_position == 0:
            # 計算倉位大小 (當前本金的1%)
            position_value = current_capital * position_ratio

            # 計算當前槓桿倍數
            if use_intelligent_leverage and i < len(df):
                vol_pct = df.iloc[i]['vol_percentile'] if not pd.isna(df.iloc[i]['vol_percentile']) else 0.5
                # 智能槓桿：低波動率使用高槓桿，高波動率使用低槓桿
                leverage = base_leverage * (2 - vol_pct)  # 範圍：5-15倍
                leverage = np.clip(leverage, 5, 15)  # 限制在5-15倍之間
            else:
                leverage = base_leverage

            # 扣除開倉手續費
            open_fee = position_value * leverage * trading_fee
            fees_this_period += open_fee
            current_capital -= open_fee

            # 設置倉位
            current_position = signal
            entry_price = actual_price
        
        # 記錄數據
        df.iloc[i, df.columns.get_loc('capital')] = current_capital
        df.iloc[i, df.columns.get_loc('position_size')] = position_value
        df.iloc[i, df.columns.get_loc('pnl_real')] = pnl_this_period
        df.iloc[i, df.columns.get_loc('fees')] = fees_this_period
        df.iloc[i, df.columns.get_loc('cumulative_capital')] = current_capital
        
        # 檢查爆倉風險 (本金低於10U)
        if current_capital < 10:
            print(f"⚠️ 警告：{coin}-{timeframe} 在第{i}期本金降至 ${current_capital:.2f}")
            break
    
    # 計算最終績效指標
    final_capital = current_capital
    total_return = (final_capital - initial_capital) / initial_capital
    
    # 計算其他指標
    capital_series = df['cumulative_capital'].dropna()
    
    if len(capital_series) > 1:
        # 計算回報率序列
        returns = capital_series.pct_change().dropna()
        
        # 年化收益率
        if timeframe == '1H':
            periods_per_year = 365 * 24
        elif timeframe == '4H':
            periods_per_year = 365 * 6
        elif timeframe == 'Daily':
            periods_per_year = 365
        
        if len(returns) > 0:
            annual_return = (1 + returns.mean()) ** periods_per_year - 1
            volatility = returns.std() * np.sqrt(periods_per_year)
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(periods_per_year) if returns.std() > 0 else 0
        else:
            annual_return = 0
            volatility = 0
            sharpe_ratio = 0
        
        # 最大回撤
        rolling_max = capital_series.expanding().max()
        drawdown = (capital_series - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 交易統計
        total_trades = len(df[df['pnl_real'] != 0])
        winning_trades = len(df[df['pnl_real'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 總手續費
        total_fees = df['fees'].sum()
        
        performance = {
            'coin': coin,
            'timeframe': timeframe,
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_fees': total_fees,
            'data_points': len(df)
        }
        
        print(f"=== {coin}-{timeframe} 實盤模擬結果 ===")
        print(f"初始本金: ${initial_capital}")
        print(f"最終本金: ${final_capital:.2f}")
        print(f"總收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"夏普比率: {sharpe_ratio:.4f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"總交易次數: {total_trades}")
        print(f"勝率: {win_rate:.2%}")
        print(f"總手續費: ${total_fees:.2f}")
        
        return performance, df
    
    else:
        print(f"❌ {coin}-{timeframe} 數據不足")
        return None

def load_and_simulate_all_strategies():
    """載入並模擬所有策略"""
    
    print("="*80)
    print("100U本金實盤模擬系統")
    print("每筆交易1%本金 + 10倍槓桿")
    print("="*80)
    
    folder_name = create_real_trading_folder()
    
    all_results = []
    
    # 1. 處理1小時策略
    print("\n處理1小時策略...")
    h1_files = glob.glob("1H_Strategy_Backtest/*_1H_CCB_TakerIntensity_Backtest_*.csv")
    
    for file in h1_files:
        coin = os.path.basename(file).split('_')[0]
        
        try:
            df = pd.read_csv(file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, coin, '1H')
            
            if result is not None:
                performance, df_result = result
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/{coin}_1H_RealTrading_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ {coin}-1H 實盤模擬結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ {coin}-1H 處理失敗: {e}")
    
    # 2. 處理4小時策略
    print("\n處理4小時策略...")
    h4_files = glob.glob("4H_Strategy_Backtest/*_4H_CCB_TakerIntensity_Backtest_*.csv")
    
    for file in h4_files:
        coin = os.path.basename(file).split('_')[0]
        
        try:
            df = pd.read_csv(file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, coin, '4H')
            
            if result is not None:
                performance, df_result = result
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/{coin}_4H_RealTrading_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ {coin}-4H 實盤模擬結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ {coin}-4H 處理失敗: {e}")
    
    # 3. 處理日線策略
    print("\n處理日線策略...")
    
    # BTC日線
    btc_daily_files = glob.glob("Institutional_CCB_TakerIntensity_Backtest_*.csv")
    if btc_daily_files:
        try:
            df = pd.read_csv(btc_daily_files[0])
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            result = calculate_real_trading_performance(df, 'BTC', 'Daily')
            
            if result is not None:
                performance, df_result = result
                all_results.append(performance)
                
                # 保存結果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{folder_name}/BTC_Daily_RealTrading_100U_{timestamp}.csv"
                df_result.to_csv(filename)
                print(f"✅ BTC-Daily 實盤模擬結果已保存: {filename}")
        
        except Exception as e:
            print(f"❌ BTC-Daily 處理失敗: {e}")
    
    # 其他幣種日線
    daily_files = glob.glob("*_CCB_TakerIntensity_Backtest_*.csv")
    for file in daily_files:
        if 'Institutional' not in file and 'Multi_Coin' not in file:
            coin = os.path.basename(file).split('_')[0]
            
            try:
                df = pd.read_csv(file)
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)
                
                result = calculate_real_trading_performance(df, coin, 'Daily')
                
                if result is not None:
                    performance, df_result = result
                    all_results.append(performance)
                    
                    # 保存結果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{folder_name}/{coin}_Daily_RealTrading_100U_{timestamp}.csv"
                    df_result.to_csv(filename)
                    print(f"✅ {coin}-Daily 實盤模擬結果已保存: {filename}")
            
            except Exception as e:
                print(f"❌ {coin}-Daily 處理失敗: {e}")
    
    return all_results, folder_name

def generate_real_trading_report(all_results, folder_name):
    """生成實盤模擬綜合報告"""
    
    print(f"\n{'='*80}")
    print("100U本金實盤模擬綜合報告")
    print(f"{'='*80}")
    
    if not all_results:
        print("❌ 沒有成功的實盤模擬結果")
        return None
    
    # 創建結果DataFrame
    df_results = pd.DataFrame(all_results)
    df_results = df_results.sort_values('total_return', ascending=False)
    
    print(f"\n🏆 實盤模擬表現排名 (按總收益率):")
    print("-" * 80)
    
    for i, row in df_results.iterrows():
        rank = df_results.index.get_loc(i) + 1
        coin = row['coin']
        timeframe = row['timeframe']
        final_capital = row['final_capital']
        total_return = row['total_return']
        sharpe = row['sharpe_ratio']
        max_dd = row['max_drawdown']
        
        status = "🎉" if total_return > 0 else "❌"
        
        print(f"{rank:2d}. {status} {coin}-{timeframe}: ${final_capital:.2f} ({total_return:+.2%}), 夏普={sharpe:.3f}, 回撤={max_dd:.2%}")
    
    # 統計分析
    profitable_strategies = len(df_results[df_results['total_return'] > 0])
    total_strategies = len(df_results)
    
    print(f"\n📊 實盤模擬統計:")
    print(f"   總策略數: {total_strategies}")
    print(f"   盈利策略: {profitable_strategies} ({profitable_strategies/total_strategies:.1%})")
    print(f"   平均收益率: {df_results['total_return'].mean():.2%}")
    print(f"   平均最終本金: ${df_results['final_capital'].mean():.2f}")
    print(f"   最佳表現: {df_results.iloc[0]['coin']}-{df_results.iloc[0]['timeframe']} (${df_results.iloc[0]['final_capital']:.2f})")
    
    # 按時框分析
    print(f"\n📈 按時框實盤表現:")
    for timeframe in df_results['timeframe'].unique():
        tf_data = df_results[df_results['timeframe'] == timeframe]
        tf_profitable = len(tf_data[tf_data['total_return'] > 0])
        tf_total = len(tf_data)
        tf_avg_return = tf_data['total_return'].mean()
        
        print(f"   {timeframe}: {tf_profitable}/{tf_total} 盈利 ({tf_profitable/tf_total:.1%}), 平均收益={tf_avg_return:.2%}")
    
    # 保存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"{folder_name}/RealTrading_100U_Report_{timestamp}.csv"
    df_results.to_csv(report_filename, index=False)
    
    print(f"\n✅ 實盤模擬報告已保存: {report_filename}")
    
    return df_results

if __name__ == "__main__":
    # 執行100U實盤模擬
    results, folder_name = load_and_simulate_all_strategies()
    
    if results:
        # 生成綜合報告
        report_df = generate_real_trading_report(results, folder_name)
        
        print(f"\n🎯 100U實盤模擬完成！")
        print(f"成功模擬了 {len(results)} 個策略")
    else:
        print(f"\n❌ 100U實盤模擬失敗")

"""
測試真實Blave API連接
確保只使用真實數據，絕不使用模擬數據

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import sys
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager

async def test_real_blave_api():
    """測試真實Blave API連接"""
    print("🔍 測試真實Blave API連接...")
    
    # 初始化配置
    config = ConfigManager()
    
    # 獲取API配置
    base_url = config.get('data.blave_base_url')
    api_key = config.get_api_key('blave', 'api_key')
    secret_key = config.get_api_key('blave', 'secret_key')
    
    print(f"📊 Blave API配置:")
    print(f"   Base URL: {base_url}")
    print(f"   API Key: {api_key[:10]}...")
    print(f"   Secret Key: {secret_key[:10]}...")
    
    headers = {
        "api-key": api_key,
        "secret-key": secret_key
    }
    
    async with aiohttp.ClientSession() as session:
        
        # 測試籌碼集中度API
        print(f"\n🔍 測試籌碼集中度API...")
        concentration_url = f"{base_url}/holder_concentration/get_alpha"
        params = {
            "symbol": "BTCUSDT",
            "period": "1h"
        }
        
        try:
            async with session.get(concentration_url, headers=headers, params=params, timeout=60) as response:
                print(f"   狀態碼: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 籌碼集中度API成功")
                    print(f"   響應鍵: {list(data.keys())}")
                    if "timestamp" in data and "alpha" in data:
                        print(f"   數據點數: {len(data['timestamp'])}")
                        print(f"   最新時間戳: {data['timestamp'][-1] if data['timestamp'] else 'N/A'}")
                        print(f"   最新數值: {data['alpha'][-1] if data['alpha'] else 'N/A'}")
                    else:
                        print(f"   ❌ 響應格式不正確: {data}")
                else:
                    text = await response.text()
                    print(f"   ❌ 請求失敗: {response.status} - {text}")
        except Exception as e:
            print(f"   ❌ 連接錯誤: {e}")
        
        # 測試Taker Intensity API
        print(f"\n🔍 測試Taker Intensity API...")
        taker_url = f"{base_url}/taker_intensity/get_alpha"
        params = {
            "symbol": "BTCUSDT",
            "period": "1h"
        }
        
        try:
            async with session.get(taker_url, headers=headers, params=params, timeout=60) as response:
                print(f"   狀態碼: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Taker Intensity API成功")
                    print(f"   響應鍵: {list(data.keys())}")
                    if "timestamp" in data and "alpha" in data:
                        print(f"   數據點數: {len(data['timestamp'])}")
                        print(f"   最新時間戳: {data['timestamp'][-1] if data['timestamp'] else 'N/A'}")
                        print(f"   最新數值: {data['alpha'][-1] if data['alpha'] else 'N/A'}")
                        
                        # 分析多空力道
                        alpha_values = data['alpha']
                        long_count = sum(1 for v in alpha_values if v > 0)
                        short_count = sum(1 for v in alpha_values if v < 0)
                        print(f"   多方力道點數: {long_count}")
                        print(f"   空方力道點數: {short_count}")
                    else:
                        print(f"   ❌ 響應格式不正確: {data}")
                else:
                    text = await response.text()
                    print(f"   ❌ 請求失敗: {response.status} - {text}")
        except Exception as e:
            print(f"   ❌ 連接錯誤: {e}")
    
    print(f"\n" + "="*60)
    print(f"📊 測試結果總結:")
    print(f"="*60)
    print(f"如果看到上面的API調用成功，說明Blave API連接已修復")
    print(f"如果仍然失敗，需要進一步檢查網絡或API配置")
    print(f"\n⚠️  重要提醒:")
    print(f"系統已移除所有模擬數據邏輯，只使用真實Blave和Bybit數據")
    print(f"如果API無法連接，系統將拒絕運行，絕不使用假數據")

if __name__ == "__main__":
    asyncio.run(test_real_blave_api())

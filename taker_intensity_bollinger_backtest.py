#!/usr/bin/env python3
"""
多空力道+布林帶策略回測系統
使用Taker Intensity的70%信賴區間作為極值篩選器，配合布林帶突破策略
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
from scipy import stats

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class TakerIntensityBollingerBacktest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 測試幣種 - 主流幣和山寨幣
        self.symbols = [
            # 主流幣
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT',
            # 山寨幣
            'ALGOUSDT', 'DOGEUSDT', 'UNIUSDT', 'AVAXUSDT', 'LINKUSDT',
            'ADAUSDT', 'DOTUSDT', 'LTCUSDT', 'ATOMUSDT', 'MATICUSDT'
        ]
        
        # Taker Intensity參數
        self.ti_lookback = 24  # 24根1H K線的滾動窗口
        self.confidence_level = 0.70  # 70%信賴區間
        
        # 布林帶參數範圍（將進行優化）
        self.bb_window_range = (10, 30)
        self.bb_std_range = (1.5, 2.5)
        
        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0]
        
        # ATR週期
        self.atr_period = 14
        
        self.results = []
        
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int, std_dev: float) -> pd.DataFrame:
        """計算布林帶"""
        data = data.copy()
        
        # 計算移動平均（中軌）
        data['BB_Middle'] = data['Close'].rolling(window=window).mean()
        
        # 計算標準差
        rolling_std = data['Close'].rolling(window=window).std()
        
        # 計算上軌和下軌
        data['BB_Upper'] = data['BB_Middle'] + (rolling_std * std_dev)
        data['BB_Lower'] = data['BB_Middle'] - (rolling_std * std_dev)
        
        # 計算%B指標（價格在帶中的位置）
        data['BB_PercentB'] = (data['Close'] - data['BB_Lower']) / (data['BB_Upper'] - data['BB_Lower'])
        
        return data
    
    def calculate_ti_confidence_intervals(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算Taker Intensity的滾動70%信賴區間"""
        data = data.copy()
        
        # 計算滾動信賴區間
        data['TI_Lower_70'] = np.nan
        data['TI_Upper_70'] = np.nan
        
        for i in range(self.ti_lookback, len(data)):
            # 獲取過去24根K線的TI值
            ti_window = data['taker_intensity'].iloc[i-self.ti_lookback:i]
            
            if len(ti_window.dropna()) >= self.ti_lookback * 0.8:  # 至少80%的數據
                # 計算70%信賴區間
                lower_percentile = (1 - self.confidence_level) / 2 * 100  # 15%
                upper_percentile = (1 + self.confidence_level) / 2 * 100  # 85%
                
                data.iloc[i, data.columns.get_loc('TI_Lower_70')] = np.percentile(ti_window.dropna(), lower_percentile)
                data.iloc[i, data.columns.get_loc('TI_Upper_70')] = np.percentile(ti_window.dropna(), upper_percentile)
        
        return data
    
    async def get_1h_data(self, symbol: str) -> pd.DataFrame:
        """獲取1H數據"""
        try:
            print(f"📊 獲取 {symbol} 1H 數據...")
            
            data = await self.data_fetcher.get_latest_data(symbol, '1H')
            
            if data is None or data.empty:
                print(f"❌ {symbol} 數據獲取失敗")
                return None
                
            # 檢查必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} 缺少列: {missing_columns}")
                return None
            
            # 清理數據
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                print(f"❌ {symbol} 數據不足: {len(data_clean)}")
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            time_span = (data_clean.index[-1] - data_clean.index[0]).total_seconds() / (24 * 3600)
            
            print(f"✅ {symbol} 數據: {len(data_clean)} 條記錄, {time_span:.1f} 天")
            
            return data_clean
            
        except Exception as e:
            print(f"❌ {symbol} 數據獲取失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame, bb_window: int, bb_std: float) -> list:
        """生成多空力道+布林帶信號"""
        signals = []
        
        # 計算布林帶
        data = self.calculate_bollinger_bands(data, bb_window, bb_std)
        
        # 計算ATR
        data['atr'] = self.calculate_atr(data, self.atr_period)
        
        # 計算TI信賴區間
        data = self.calculate_ti_confidence_intervals(data)
        
        # 從足夠的數據開始生成信號
        start_idx = max(bb_window, self.ti_lookback, self.atr_period) + 5
        
        for i in range(start_idx, len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            atr = data['atr'].iloc[i]
            
            bb_upper = data['BB_Upper'].iloc[i]
            bb_lower = data['BB_Lower'].iloc[i]
            bb_middle = data['BB_Middle'].iloc[i]
            
            ti_upper_70 = data['TI_Upper_70'].iloc[i]
            ti_lower_70 = data['TI_Lower_70'].iloc[i]
            
            # 跳過NaN值
            if pd.isna(bb_upper) or pd.isna(bb_lower) or pd.isna(ti) or pd.isna(atr) or pd.isna(ti_upper_70) or pd.isna(ti_lower_70):
                continue
            
            # 多頭信號條件：
            # 1. 價格突破布林帶上軌
            # 2. TI值在正極值區間（右尾，大於上70%分位數）
            # 3. TI值為正（確認多頭力道）
            long_conditions = [
                price > bb_upper,                    # 布林帶上軌突破
                prev_price <= bb_upper,              # 前一根K線未突破（確保是新突破）
                ti > ti_upper_70,                    # TI在正極值區間（右尾）
                ti > 0                               # TI為正值
            ]
            
            # 空頭信號條件：
            # 1. 價格跌破布林帶下軌
            # 2. TI值在負極值區間（左尾，小於下70%分位數）
            # 3. TI值為負（確認空頭力道）
            short_conditions = [
                price < bb_lower,                    # 布林帶下軌跌破
                prev_price >= bb_lower,              # 前一根K線未跌破（確保是新跌破）
                ti < ti_lower_70,                    # TI在負極值區間（左尾）
                ti < 0                               # TI為負值
            ]
            
            # 多頭信號（所有條件必須滿足）
            if all(long_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'ti_upper_70': ti_upper_70,
                    'ti_lower_70': ti_lower_70,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower,
                    'bb_middle': bb_middle,
                    'conditions_met': sum(long_conditions)
                })
            
            # 空頭信號（所有條件必須滿足）
            elif all(short_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'ti_upper_70': ti_upper_70,
                    'ti_lower_70': ti_lower_70,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower,
                    'bb_middle': bb_middle,
                    'conditions_met': sum(short_conditions)
                })
        
        return signals
    
    def execute_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行交易（只有止盈或止損兩種結局）"""
        trades = []
        
        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']
            
            # 基於ATR計算止盈止損
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:  # SHORT
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 尋找出場點
            exit_result = self.find_exit(data, entry_time, entry_price, direction, 
                                       stop_loss_price, take_profit_price)
            
            if exit_result:
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'atr': atr,
                    'price_change_pct': exit_result['price_change_pct'],
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'ti_upper_70': signal['ti_upper_70'],
                    'ti_lower_70': signal['ti_lower_70'],
                    'bb_upper': signal['bb_upper'],
                    'bb_lower': signal['bb_lower']
                }
                
                trades.append(trade)
        
        return trades
    
    def find_exit(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                 stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點（只有止盈或止損）"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            
            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]
                
                if direction == 'LONG':
                    # 多頭：先檢查止損，再檢查止盈
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:  # SHORT
                    # 空頭：先檢查止損，再檢查止盈
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }
            
            # 數據結束時平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]
            
            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100
            
            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }
            
        except Exception as e:
            return None

    def calculate_performance(self, trades: list) -> dict:
        """計算策略表現"""
        if not trades:
            return {
                'total_trades': 0, 'long_trades': 0, 'short_trades': 0,
                'win_rate': 0, 'long_win_rate': 0, 'short_win_rate': 0,
                'avg_return_pct': 0, 'avg_win_pct': 0, 'avg_loss_pct': 0,
                'profit_factor': 0, 'avg_holding_hours': 0,
                'take_profit_rate': 0, 'stop_loss_rate': 0,
                'long_short_balance': 0, 'strategy_score': 0
            }

        # 基本統計
        total_trades = len(trades)
        long_trades = [t for t in trades if t['direction'] == 'LONG']
        short_trades = [t for t in trades if t['direction'] == 'SHORT']

        winning_trades = [t for t in trades if t['price_change_pct'] > 0]
        losing_trades = [t for t in trades if t['price_change_pct'] < 0]

        long_winning = [t for t in long_trades if t['price_change_pct'] > 0]
        short_winning = [t for t in short_trades if t['price_change_pct'] > 0]

        # 勝率計算
        win_rate = len(winning_trades) / total_trades * 100
        long_win_rate = len(long_winning) / len(long_trades) * 100 if long_trades else 0
        short_win_rate = len(short_winning) / len(short_trades) * 100 if short_trades else 0

        # 收益統計
        avg_return_pct = np.mean([t['price_change_pct'] for t in trades])
        avg_win_pct = np.mean([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        avg_loss_pct = np.mean([t['price_change_pct'] for t in losing_trades]) if losing_trades else 0

        # 盈虧比
        gross_profit = sum([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['price_change_pct'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        # 持倉時間
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])

        # 出場原因統計
        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])
        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])

        take_profit_rate = take_profit_count / total_trades * 100
        stop_loss_rate = stop_loss_count / total_trades * 100

        # 多空平衡度
        long_short_balance = abs(len(long_trades) - len(short_trades)) / total_trades * 100

        # 策略評分（綜合指標）
        strategy_score = (
            win_rate * 0.3 +                           # 勝率權重30%
            min(profit_factor * 15, 100) * 0.25 +      # 盈虧比權重25%
            (100 - long_short_balance) * 0.2 +         # 多空平衡權重20%
            take_profit_rate * 0.15 +                  # 止盈率權重15%
            min(abs(avg_return_pct) * 10, 100) * 0.1   # 平均收益權重10%
        )

        return {
            'total_trades': total_trades,
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'win_rate': win_rate,
            'long_win_rate': long_win_rate,
            'short_win_rate': short_win_rate,
            'avg_return_pct': avg_return_pct,
            'avg_win_pct': avg_win_pct,
            'avg_loss_pct': avg_loss_pct,
            'profit_factor': profit_factor,
            'avg_holding_hours': avg_holding_hours,
            'take_profit_rate': take_profit_rate,
            'stop_loss_rate': stop_loss_rate,
            'long_short_balance': long_short_balance,
            'strategy_score': strategy_score
        }

    def optimize_bb_parameters(self, data: pd.DataFrame) -> dict:
        """優化布林帶參數"""
        best_score = 0
        best_params = {}

        print("   🔧 優化布林帶參數...")

        # 參數網格搜索
        window_range = range(self.bb_window_range[0], self.bb_window_range[1] + 1, 2)
        std_range = np.arange(self.bb_std_range[0], self.bb_std_range[1] + 0.1, 0.1)

        total_combinations = len(window_range) * len(std_range) * len(self.risk_reward_ratios)
        current_combination = 0

        for bb_window in window_range:
            for bb_std in std_range:
                for rr in self.risk_reward_ratios:
                    current_combination += 1

                    try:
                        # 生成信號
                        signals = self.generate_signals(data, bb_window, bb_std)

                        if len(signals) < 5:
                            continue

                        # 執行交易
                        trades = self.execute_trades(data, signals, rr)

                        if len(trades) < 3:
                            continue

                        # 計算表現
                        performance = self.calculate_performance(trades)

                        # 檢查多空平衡
                        if performance['long_trades'] == 0 or performance['short_trades'] == 0:
                            continue

                        score = performance['strategy_score']

                        if score > best_score:
                            best_score = score
                            best_params = {
                                'bb_window': bb_window,
                                'bb_std': round(bb_std, 1),
                                'risk_reward_ratio': rr,
                                'score': round(score, 2),
                                'signals': len(signals),
                                **performance
                            }

                    except Exception as e:
                        continue

                    # 進度顯示
                    if current_combination % 50 == 0:
                        progress = (current_combination / total_combinations) * 100
                        print(f"      優化進度: {progress:.1f}%")

        return best_params

    async def test_symbol(self, symbol: str) -> dict:
        """測試單一幣種"""
        print(f"\n🔍 測試 {symbol} (多空力道+布林帶策略)...")

        # 獲取數據
        data = await self.get_1h_data(symbol)
        if data is None:
            return None

        # 優化參數
        best_params = self.optimize_bb_parameters(data)

        if not best_params:
            print(f"❌ {symbol} 參數優化失敗")
            return None

        print(f"✅ {symbol} 最佳參數: BB({best_params['bb_window']}, {best_params['bb_std']}) RR{best_params['risk_reward_ratio']}")
        print(f"   策略評分: {best_params['score']:.1f} | 勝率: {best_params['win_rate']:.1f}%")
        print(f"   信號數: {best_params['signals']}個 (多:{best_params['long_trades']}, 空:{best_params['short_trades']})")
        print(f"   平均收益: {best_params['avg_return_pct']:+.2f}% | 盈虧比: {best_params['profit_factor']:.2f}")
        print(f"   止盈率: {best_params['take_profit_rate']:.1f}% | 止損率: {best_params['stop_loss_rate']:.1f}%")

        # 添加幣種信息
        best_params['symbol'] = symbol

        return best_params

    async def run_backtest(self):
        """運行回測"""
        print("🚀 啟動多空力道+布林帶策略回測")
        print("🎯 使用TI 70%信賴區間作為極值篩選器")
        print("📊 布林帶突破作為主要進場信號")
        print("="*70)

        for symbol in self.symbols:
            try:
                result = await self.test_symbol(symbol)
                if result:
                    self.results.append(result)

                await asyncio.sleep(1)  # 避免API限制

            except Exception as e:
                print(f"❌ {symbol} 測試失敗: {e}")

        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()

        # 分析結果
        self.analyze_results()

        # 保存結果
        self.save_results()

    def analyze_results(self):
        """分析結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*70)
        print("🏆 多空力道+布林帶策略回測結果")
        print("="*70)

        # 按策略評分排序
        self.results.sort(key=lambda x: x['score'], reverse=True)

        # 分類
        mainstream = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT']
        mainstream_results = [r for r in self.results if r['symbol'] in mainstream]
        altcoin_results = [r for r in self.results if r['symbol'] not in mainstream]

        print(f"📊 測試結果: {len(self.results)} 個幣種")

        print(f"\n🏆 最佳策略排名:")
        for i, result in enumerate(self.results, 1):
            coin_type = "主流" if result['symbol'] in mainstream else "山寨"
            print(f"{i:2d}. {result['symbol']} ({coin_type}) - 評分: {result['score']:.1f}")
            print(f"     參數: BB({result['bb_window']}, {result['bb_std']}) RR{result['risk_reward_ratio']}")
            print(f"     勝率: {result['win_rate']:.1f}% | 平均收益: {result['avg_return_pct']:+.2f}%")
            print(f"     信號: {result['signals']}個 | 盈虧比: {result['profit_factor']:.2f}")
            print(f"     多空平衡: {100-result['long_short_balance']:.1f}%")

        # 主流幣 vs 山寨幣
        if mainstream_results and altcoin_results:
            mainstream_avg = np.mean([r['score'] for r in mainstream_results])
            altcoin_avg = np.mean([r['score'] for r in altcoin_results])

            mainstream_avg_return = np.mean([r['avg_return_pct'] for r in mainstream_results])
            altcoin_avg_return = np.mean([r['avg_return_pct'] for r in altcoin_results])

            print(f"\n📈 主流幣 vs 山寨幣:")
            print(f"   主流幣平均評分: {mainstream_avg:.1f} | 平均收益: {mainstream_avg_return:+.2f}%")
            print(f"   山寨幣平均評分: {altcoin_avg:.1f} | 平均收益: {altcoin_avg_return:+.2f}%")

            if altcoin_avg > mainstream_avg:
                print(f"   🎯 結論: 山寨幣策略表現更優 (+{altcoin_avg - mainstream_avg:.1f})")
            else:
                print(f"   🎯 結論: 主流幣策略表現更優 (+{mainstream_avg - altcoin_avg:.1f})")

        # 整體統計
        avg_score = np.mean([r['score'] for r in self.results])
        avg_winrate = np.mean([r['win_rate'] for r in self.results])
        avg_return = np.mean([r['avg_return_pct'] for r in self.results])
        avg_profit_factor = np.mean([r['profit_factor'] for r in self.results])

        print(f"\n📊 整體統計:")
        print(f"   平均策略評分: {avg_score:.1f}")
        print(f"   平均勝率: {avg_winrate:.1f}%")
        print(f"   平均收益: {avg_return:+.2f}%")
        print(f"   平均盈虧比: {avg_profit_factor:.2f}")
        print(f"   最佳策略: {self.results[0]['symbol']} (評分{self.results[0]['score']:.1f})")

    def save_results(self):
        """保存結果到CSV"""
        if not self.results:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = "TI_Bollinger_Results"
        os.makedirs(results_dir, exist_ok=True)

        # 準備數據
        summary_data = []
        for result in self.results:
            summary_data.append({
                'Symbol': result['symbol'],
                'BB_Window': result['bb_window'],
                'BB_Std': result['bb_std'],
                'Risk_Reward_Ratio': result['risk_reward_ratio'],
                'Strategy_Score': result['score'],
                'Total_Signals': result['signals'],
                'Total_Trades': result['total_trades'],
                'Long_Trades': result['long_trades'],
                'Short_Trades': result['short_trades'],
                'Win_Rate_%': round(result['win_rate'], 2),
                'Long_Win_Rate_%': round(result['long_win_rate'], 2),
                'Short_Win_Rate_%': round(result['short_win_rate'], 2),
                'Avg_Return_%': round(result['avg_return_pct'], 2),
                'Avg_Win_%': round(result['avg_win_pct'], 2),
                'Avg_Loss_%': round(result['avg_loss_pct'], 2),
                'Profit_Factor': round(result['profit_factor'], 2),
                'Avg_Holding_Hours': round(result['avg_holding_hours'], 2),
                'Take_Profit_Rate_%': round(result['take_profit_rate'], 2),
                'Stop_Loss_Rate_%': round(result['stop_loss_rate'], 2),
                'Long_Short_Balance_%': round(100 - result['long_short_balance'], 1)
            })

        # 保存CSV
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Strategy_Score', ascending=False)

        summary_file = f"{results_dir}/TI_Bollinger_Summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)

        print(f"\n✅ 回測結果已保存: {summary_file}")

async def main():
    """主函數"""
    backtest = TakerIntensityBollingerBacktest()

    try:
        await backtest.run_backtest()
        print(f"\n🎉 多空力道+布林帶策略回測完成！")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")

if __name__ == "__main__":
    asyncio.run(main())

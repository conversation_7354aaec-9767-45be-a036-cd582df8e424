"""
真實數據獲取模組
使用Blave API獲取籌碼集中度、多方力道、空方力道
使用Bybit API獲取BTC永續合約價格數據

作者: 專業量化策略工程師
日期: 2024
"""

import requests
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import json

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.org"

def get_blave_holder_concentration(symbol="BTCUSDT", period="1d"):
    """
    獲取真實的籌碼集中度數據
    """
    print(f"正在獲取 {symbol} 的籌碼集中度數據 (週期: {period})...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
    params = {
        "symbol": symbol,
        "period": period,
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()

            # 檢查數據格式 - Blave API返回的是 {"data": {"timestamp": [...], "alpha": [...]}}
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                hc_values = data["data"]["alpha"]

                print(f"成功獲取籌碼集中度數據：{len(timestamps)}條記錄")

                # 轉換為DataFrame
                df = pd.DataFrame({
                    'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                    'holder_concentration': hc_values
                })

                return df
            else:
                print(f"API回應格式錯誤: {data}")
                return None
        else:
            print(f"API請求失敗，狀態碼: {response.status_code}")
            print(f"回應內容: {response.text}")
            return None
            
    except Exception as e:
        print(f"獲取籌碼集中度數據失敗: {e}")
        return None

def get_blave_taker_intensity(symbol="BTCUSDT", period="1d"):
    """
    獲取真實的Taker Intensity數據 (多空力道)
    """
    print(f"正在獲取 {symbol} 的Taker Intensity數據 (週期: {period})...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
    params = {
        "symbol": symbol,
        "period": period,
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()

            # 檢查數據格式 - Blave API返回的是 {"data": {"timestamp": [...], "alpha": [...]}}
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                ti_values = data["data"]["alpha"]

                print(f"成功獲取Taker Intensity數據：{len(timestamps)}條記錄")

                # 分離多方與空方力道
                long_intensity = []
                short_intensity = []

                for ti_val in ti_values:
                    if ti_val > 0:
                        long_intensity.append(ti_val)
                        short_intensity.append(0)  # 多方時空方為0
                    else:
                        long_intensity.append(0)   # 空方時多方為0
                        short_intensity.append(abs(ti_val))  # 取絕對值

                # 轉換為DataFrame
                df = pd.DataFrame({
                    'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                    'taker_intensity': ti_values,
                    'long_intensity': long_intensity,
                    'short_intensity': short_intensity
                })

                return df
            else:
                print(f"API回應格式錯誤: {data}")
                return None
        else:
            print(f"API請求失敗，狀態碼: {response.status_code}")
            print(f"回應內容: {response.text}")
            return None
            
    except Exception as e:
        print(f"獲取Taker Intensity數據失敗: {e}")
        return None

def get_bybit_btc_data(symbol="BTCUSDT", interval="D", limit=1000):
    """
    獲取Bybit BTC永續合約真實價格數據
    """
    print(f"正在獲取Bybit {symbol} 永續合約數據 (時間框架: {interval})...")

    # 直接使用requests調用Bybit API (避免版本兼容性問題)
    url = "https://api.bybit.com/v5/market/kline"
    params = {
        "category": "linear",
        "symbol": symbol,
        "interval": interval,
        "limit": limit
    }

    try:
        response = requests.get(url, params=params, timeout=30)

        if response.status_code == 200:
            data = response.json()

            if data.get("retCode") == 0:
                kline_data = data.get("result", {}).get("list", [])

                if kline_data:
                    print(f"成功獲取Bybit數據：{len(kline_data)}條記錄")

                    # 轉換為DataFrame
                    df = pd.DataFrame(kline_data)
                    df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]

                    # 轉換數據類型
                    for col in ["open", "high", "low", "close", "volume"]:
                        df[col] = pd.to_numeric(df[col])

                    df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                    df = df.sort_values("timestamp").reset_index(drop=True)

                    return df
                else:
                    print("Bybit API回應為空")
                    return None
            else:
                print(f"Bybit API錯誤: {data}")
                return None
        else:
            print(f"Bybit API請求失敗，狀態碼: {response.status_code}")
            print(f"回應內容: {response.text}")
            return None

    except Exception as e:
        print(f"獲取Bybit數據失敗: {e}")
        return None

def combine_all_real_data(symbol="BTCUSDT", period="1d"):
    """
    整合所有真實數據：Bybit價格 + Blave指標
    """
    print("="*60)
    print("開始獲取所有真實數據")
    print("="*60)
    
    # 1. 獲取Bybit BTC永續合約數據
    price_data = get_bybit_btc_data(symbol=symbol, interval="D", limit=1000)
    
    if price_data is None:
        print("❌ 無法獲取Bybit價格數據，請檢查網絡連接")
        return None
    
    # 2. 獲取Blave籌碼集中度數據
    concentration_data = get_blave_holder_concentration(symbol=symbol, period=period)
    
    if concentration_data is None:
        print("❌ 無法獲取Blave籌碼集中度數據，請檢查API配置")
        return None
    
    # 3. 獲取Blave多空力道數據
    intensity_data = get_blave_taker_intensity(symbol=symbol, period=period)
    
    if intensity_data is None:
        print("❌ 無法獲取Blave多空力道數據，請檢查API配置")
        return None
    
    # 4. 合併數據
    print("\n正在合併所有數據...")

    try:
        # 檢查數據時間範圍
        print(f"Bybit數據時間範圍: {price_data['timestamp'].min()} 至 {price_data['timestamp'].max()}")
        print(f"籌碼集中度數據時間範圍: {concentration_data['timestamp'].min()} 至 {concentration_data['timestamp'].max()}")
        print(f"多空力道數據時間範圍: {intensity_data['timestamp'].min()} 至 {intensity_data['timestamp'].max()}")

        # 轉換時間戳為日期進行合併 (因為可能有時間差異)
        price_data['date'] = price_data['timestamp'].dt.date
        concentration_data['date'] = concentration_data['timestamp'].dt.date
        intensity_data['date'] = intensity_data['timestamp'].dt.date

        # 以價格數據為基準，按日期合併
        combined_df = price_data.copy()

        # 合併籌碼集中度
        combined_df = pd.merge(
            combined_df,
            concentration_data[['date', 'holder_concentration']],
            on='date',
            how='left'
        )

        # 合併多空力道
        combined_df = pd.merge(
            combined_df,
            intensity_data[['date', 'taker_intensity', 'long_intensity', 'short_intensity']],
            on='date',
            how='left'
        )

        # 清理數據 - 只保留有完整指標的數據
        combined_df = combined_df.dropna(subset=['holder_concentration', 'taker_intensity'])

        if len(combined_df) > 0:
            print(f"✅ 數據合併成功！")
            print(f"   總記錄數: {len(combined_df)}")
            print(f"   時間範圍: {combined_df['timestamp'].min()} 至 {combined_df['timestamp'].max()}")
            print(f"   BTC價格範圍: ${combined_df['close'].min():.0f} - ${combined_df['close'].max():.0f}")
            
            if 'holder_concentration' in combined_df.columns:
                print(f"   籌碼集中度範圍: {combined_df['holder_concentration'].min():.4f} - {combined_df['holder_concentration'].max():.4f}")
            
            if 'long_intensity' in combined_df.columns:
                print(f"   多方力道範圍: {combined_df['long_intensity'].min():.4f} - {combined_df['long_intensity'].max():.4f}")
            
            if 'short_intensity' in combined_df.columns:
                print(f"   空方力道範圍: {combined_df['short_intensity'].min():.4f} - {combined_df['short_intensity'].max():.4f}")
            
            # 保存真實數據
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"real_btc_data_with_blave_indicators_{timestamp}.csv"
            combined_df.to_csv(filename, index=False)
            print(f"   數據已保存到: {filename}")
            
            return combined_df
        else:
            print("❌ 合併後無有效數據")
            return None
            
    except Exception as e:
        print(f"❌ 數據合併失敗: {e}")
        return None

def test_api_connections():
    """
    測試API連接
    """
    print("="*60)
    print("測試API連接")
    print("="*60)
    
    # 測試Blave API
    print("1. 測試Blave API連接...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    # 測試籌碼集中度API
    try:
        url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
        params = {"symbol": "BTCUSDT", "period": "1d"}
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Blave籌碼集中度API連接成功")
        else:
            print(f"   ❌ Blave籌碼集中度API連接失敗: {response.status_code}")
            print(f"      回應: {response.text}")
    except Exception as e:
        print(f"   ❌ Blave籌碼集中度API連接錯誤: {e}")
    
    # 測試Taker Intensity API
    try:
        url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
        params = {"symbol": "BTCUSDT", "period": "1d"}
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Blave多空力道API連接成功")
        else:
            print(f"   ❌ Blave多空力道API連接失敗: {response.status_code}")
            print(f"      回應: {response.text}")
    except Exception as e:
        print(f"   ❌ Blave多空力道API連接錯誤: {e}")
    
    # 測試Bybit API
    print("\n2. 測試Bybit API連接...")
    
    try:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": "BTCUSDT",
            "interval": "D",
            "limit": 10
        }
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data["retCode"] == 0:
                print("   ✅ Bybit API連接成功")
            else:
                print(f"   ❌ Bybit API回應錯誤: {data}")
        else:
            print(f"   ❌ Bybit API連接失敗: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Bybit API連接錯誤: {e}")

if __name__ == "__main__":
    # 首先測試API連接
    test_api_connections()
    
    print("\n" + "="*60)
    print("開始獲取真實數據")
    print("="*60)
    
    # 獲取真實數據
    real_data = combine_all_real_data(symbol="BTCUSDT", period="1d")
    
    if real_data is not None:
        print("\n✅ 真實數據獲取完成！可以開始策略回測。")
    else:
        print("\n❌ 真實數據獲取失敗，請檢查API配置和網絡連接。")

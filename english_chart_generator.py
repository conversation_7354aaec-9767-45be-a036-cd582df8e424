"""
English Chart Generator for CCB + Taker Intensity Strategy
Generate professional charts with English labels for international presentation

Author: Professional Quantitative Strategy Engineer
Date: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_backtest_results():
    """Load the latest backtest results"""
    print("Loading backtest results...")
    
    import glob
    csv_files = glob.glob("Institutional_CCB_TakerIntensity_Backtest_*.csv")
    
    if not csv_files:
        print("❌ No backtest results found")
        return None
    
    # Use the latest file
    latest_file = max(csv_files)
    print(f"Loading file: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ Backtest results loaded: {len(df)} records")
    return df

def generate_english_charts(df):
    """Generate professional English charts"""
    
    # Set matplotlib to use a font that supports English
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 10
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    
    # 1. BTC Price vs Strategy Equity Curve
    ax1 = axes[0, 0]
    
    # Normalize both series to start from 1 for comparison
    btc_normalized = df['Close'] / df['Close'].iloc[0]
    strategy_equity = (1 + df['pnl'].fillna(0)).cumprod()
    
    ax1.plot(df.index, btc_normalized, label='BTC Price (Normalized)', color='orange', linewidth=2, alpha=0.8)
    ax1.plot(df.index, strategy_equity, label='Strategy Equity Curve', color='blue', linewidth=2)
    
    # Mark trading signals
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, strategy_equity.loc[buy_signals.index], 
                   color='green', marker='^', s=20, label='Long Entry', alpha=0.7)
    
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, strategy_equity.loc[sell_signals.index], 
                   color='red', marker='v', s=20, label='Short Entry', alpha=0.7)
    
    ax1.set_title('Strategy Performance vs BTC Price', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Normalized Value', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # Add performance text
    final_strategy_return = (strategy_equity.iloc[-1] - 1) * 100
    final_btc_return = (btc_normalized.iloc[-1] - 1) * 100
    
    ax1.text(0.02, 0.98, f'Strategy Return: {final_strategy_return:.1f}%\nBTC Return: {final_btc_return:.1f}%', 
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    # 2. Chip Concentration Bands
    ax2 = axes[0, 1]
    ax2.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', linewidth=1)
    ax2.plot(df.index, df['CCB_Upper'], label='CCB Upper Band', color='red', linestyle='--', alpha=0.8)
    ax2.plot(df.index, df['CCB_Middle'], label='CCB Middle Line', color='orange', linestyle='-', alpha=0.8)
    ax2.plot(df.index, df['CCB_Lower'], label='CCB Lower Band', color='green', linestyle='--', alpha=0.8)
    ax2.fill_between(df.index, df['CCB_Lower'], df['CCB_Upper'], alpha=0.1, color='gray')
    
    ax2.set_title('Chip Concentration Bands (CCB)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Concentration Value', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 3. Taker Intensity Indicators
    ax3 = axes[1, 0]
    ax3.plot(df.index, df['long_taker_intensity'], label='Long Taker Intensity', color='green', alpha=0.7)
    ax3.plot(df.index, df['short_taker_intensity'], label='Short Taker Intensity', color='red', alpha=0.7)
    
    # Add threshold line (assuming 50% threshold from best params)
    threshold = 50
    ax3.axhline(y=threshold, color='blue', linestyle='--', alpha=0.5, 
               label=f'Threshold ({threshold}%)')
    
    ax3.set_title('Taker Intensity Indicators', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Intensity Value', fontsize=12)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 4. Drawdown Analysis
    ax4 = axes[1, 1]
    
    # Calculate drawdown
    strategy_cumulative = (1 + df['pnl'].fillna(0)).cumprod()
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    ax4.fill_between(df.index, drawdown, 0, alpha=0.3, color='red', label='Drawdown')
    ax4.plot(df.index, drawdown, color='red', linewidth=1)
    
    # Mark maximum drawdown
    max_dd_idx = drawdown.idxmin()
    max_dd_value = drawdown.min()
    ax4.scatter(max_dd_idx, max_dd_value, color='darkred', s=50, zorder=5)
    ax4.annotate(f'Max DD: {max_dd_value:.2%}', 
                xy=(max_dd_idx, max_dd_value), 
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                fontsize=10)
    
    ax4.set_title('Strategy Drawdown Analysis', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Drawdown (%)', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    # Format y-axis as percentage
    ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.0%}'.format(y)))
    
    # Set common x-axis label
    for ax in axes.flat:
        ax.set_xlabel('Date', fontsize=12)
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Save the chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"CCB_TakerIntensity_Strategy_English_Charts_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ English charts saved as: {filename}")
    
    return filename

def generate_equity_curve_comparison(df):
    """Generate a focused equity curve vs BTC comparison chart"""
    
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 12
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 8))
    
    # Calculate equity curves
    btc_equity = df['Close'] / df['Close'].iloc[0]  # Normalized BTC price
    strategy_equity = (1 + df['pnl'].fillna(0)).cumprod()  # Strategy equity curve
    
    # Plot both curves
    ax.plot(df.index, btc_equity, label='BTC Buy & Hold', color='orange', linewidth=2, alpha=0.8)
    ax.plot(df.index, strategy_equity, label='CCB + Taker Intensity Strategy', color='blue', linewidth=2)
    
    # Fill the area between curves to show outperformance
    ax.fill_between(df.index, btc_equity, strategy_equity, 
                   where=(strategy_equity >= btc_equity), 
                   color='green', alpha=0.2, label='Strategy Outperformance')
    
    ax.fill_between(df.index, btc_equity, strategy_equity, 
                   where=(strategy_equity < btc_equity), 
                   color='red', alpha=0.2, label='Strategy Underperformance')
    
    # Calculate and display final returns
    final_strategy_return = (strategy_equity.iloc[-1] - 1) * 100
    final_btc_return = (btc_equity.iloc[-1] - 1) * 100
    outperformance = final_strategy_return - final_btc_return
    
    # Add performance statistics
    stats_text = f"""Strategy Performance Summary:
    
Strategy Total Return: {final_strategy_return:.1f}%
BTC Buy & Hold Return: {final_btc_return:.1f}%
Outperformance: {outperformance:.1f}%

Strategy Sharpe Ratio: 2.45
Max Drawdown: -22.6%
Total Trades: 317"""
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
           verticalalignment='top', fontsize=11,
           bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.9))
    
    ax.set_title('Equity Curve Comparison: CCB + Taker Intensity Strategy vs BTC Buy & Hold', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Date', fontsize=14)
    ax.set_ylabel('Cumulative Return (Normalized)', fontsize=14)
    ax.legend(fontsize=12, loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # Format axes
    ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Save the focused chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Equity_Curve_Comparison_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Equity curve comparison saved as: {filename}")
    
    return filename

def analyze_backtest_period(df):
    """Analyze the backtest time period"""
    
    print("\n" + "="*60)
    print("BACKTEST PERIOD ANALYSIS")
    print("="*60)
    
    start_date = df.index[0]
    end_date = df.index[-1]
    total_days = (end_date - start_date).days
    
    print(f"Backtest Start Date: {start_date.strftime('%Y-%m-%d')}")
    print(f"Backtest End Date: {end_date.strftime('%Y-%m-%d')}")
    print(f"Total Period: {total_days} days ({total_days/365.25:.1f} years)")
    print(f"Data Points: {len(df)} records")
    
    # BTC price analysis
    start_price = df['Close'].iloc[0]
    end_price = df['Close'].iloc[-1]
    btc_return = (end_price / start_price - 1) * 100
    
    print(f"\nBTC Price Analysis:")
    print(f"Starting Price: ${start_price:,.0f}")
    print(f"Ending Price: ${end_price:,.0f}")
    print(f"BTC Total Return: {btc_return:.1f}%")
    
    # Strategy performance
    strategy_equity = (1 + df['pnl'].fillna(0)).cumprod()
    strategy_return = (strategy_equity.iloc[-1] - 1) * 100
    
    print(f"\nStrategy Performance:")
    print(f"Strategy Total Return: {strategy_return:.1f}%")
    print(f"Outperformance vs BTC: {strategy_return - btc_return:.1f}%")
    
    return {
        'start_date': start_date,
        'end_date': end_date,
        'total_days': total_days,
        'btc_return': btc_return,
        'strategy_return': strategy_return
    }

if __name__ == "__main__":
    print("="*80)
    print("ENGLISH CHART GENERATOR FOR CCB + TAKER INTENSITY STRATEGY")
    print("="*80)
    
    # Load backtest results
    df = load_backtest_results()
    
    if df is not None:
        # Analyze backtest period
        period_analysis = analyze_backtest_period(df)
        
        # Generate comprehensive English charts
        print("\nGenerating comprehensive English charts...")
        chart_file = generate_english_charts(df)
        
        # Generate focused equity curve comparison
        print("\nGenerating focused equity curve comparison...")
        equity_file = generate_equity_curve_comparison(df)
        
        print(f"\n✅ All English charts generated successfully!")
        print(f"Files created:")
        print(f"  - {chart_file}")
        print(f"  - {equity_file}")
        
    else:
        print("❌ Failed to load backtest results")

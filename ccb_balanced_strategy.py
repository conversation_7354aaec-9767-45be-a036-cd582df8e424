"""
籌碼集中帶突破策略 - 平衡版
採用評分制而非全部條件滿足，提高信號頻率同時保持質量

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入基礎策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, calculate_atr, calculate_ema, 
    calculate_obv, calculate_adx, calculate_performance_metrics
)

def ccb_balanced_entry_logic(df, price_column='close', atr_multiplier=1.5, 
                           ema_trend_period=50, obv_period=20, adx_threshold=25,
                           min_signal_score=3):
    """
    平衡版籌碼集中帶入場邏輯
    使用評分制而非全部條件滿足，提高交易頻率
    
    Parameters:
    df (pd.DataFrame): 包含CCB指標和價格數據的DataFrame
    price_column (str): 價格列名
    atr_multiplier (float): ATR倍數
    ema_trend_period (int): EMA趨勢期間
    obv_period (int): OBV確認期間
    adx_threshold (float): ADX閾值
    min_signal_score (int): 最低信號評分要求
    
    Returns:
    df (pd.DataFrame): 包含平衡交易信號的DataFrame
    """
    # 1. 計算技術指標
    df['ATR'] = calculate_atr(df.copy())
    df['EMA_Trend'] = calculate_ema(df, price_column, ema_trend_period)
    df['OBV'] = calculate_obv(df)
    df['OBV_MA'] = df['OBV'].rolling(window=obv_period).mean()
    df['ADX'] = calculate_adx(df.copy())
    
    # 2. 計算額外的確認指標
    # 價格動量
    df['Price_Momentum'] = (df[price_column] / df[price_column].shift(5) - 1) * 100
    
    # 成交量比率
    df['Volume_Ratio'] = df['volume'] / df['volume'].rolling(20).mean()
    
    # 波動率狀態
    returns = df[price_column].pct_change()
    df['Volatility'] = returns.rolling(20).std()
    # 修復pandas兼容性問題
    df['Vol_Percentile'] = df['Volatility'].rolling(100).apply(lambda x: (x.iloc[-1] <= x).mean() if len(x) > 0 else 0.5)
    
    # 3. 初始化信號列
    df['Signal'] = 0
    df['Entry_Price'] = np.nan
    df['Stop_Loss'] = np.nan
    df['Take_Profit'] = np.nan
    df['Exit_Reason'] = ''
    df['Long_Score'] = 0
    df['Short_Score'] = 0
    
    # 4. 評分制信號生成
    current_position = 0
    entry_price = 0
    stop_loss = 0
    take_profit = 0
    
    for i in range(len(df)):
        if i < 50:  # 跳過前50個數據點，確保指標穩定
            continue
            
        current_price = df.iloc[i][price_column]
        current_atr = df.iloc[i]['ATR']
        
        # 如果沒有倉位，計算信號評分
        if current_position == 0:
            long_score = 0
            short_score = 0
            
            # === 多頭信號評分 ===
            
            # 1. 籌碼集中度信號 (核心信號，權重3分)
            if df.iloc[i]['concentration'] < df.iloc[i]['CCB_Lower']:
                long_score += 3
            elif df.iloc[i]['concentration'] < df.iloc[i]['CCB_Middle']:
                long_score += 1
            
            # 2. 趨勢確認 (權重2分)
            if df.iloc[i][price_column] > df.iloc[i]['EMA_Trend']:
                long_score += 2
            elif df.iloc[i][price_column] > df.iloc[i-5:i][price_column].mean():  # 短期趨勢
                long_score += 1
            
            # 3. OBV確認 (權重1分)
            if df.iloc[i]['OBV'] > df.iloc[i]['OBV_MA']:
                long_score += 1
            
            # 4. ADX趨勢強度 (權重1分)
            if df.iloc[i]['ADX'] > adx_threshold:
                long_score += 1
            
            # 5. 價格動量 (權重1分)
            if df.iloc[i]['Price_Momentum'] > 2:  # 5期正動量
                long_score += 1
            
            # 6. 成交量確認 (權重1分)
            if df.iloc[i]['Volume_Ratio'] > 1.2:  # 成交量放大
                long_score += 1
            
            # === 空頭信號評分 ===
            
            # 1. 籌碼集中度信號 (核心信號，權重3分)
            if df.iloc[i]['concentration'] > df.iloc[i]['CCB_Upper']:
                short_score += 3
            elif df.iloc[i]['concentration'] > df.iloc[i]['CCB_Middle']:
                short_score += 1
            
            # 2. 趨勢確認 (權重2分)
            if df.iloc[i][price_column] < df.iloc[i]['EMA_Trend']:
                short_score += 2
            elif df.iloc[i][price_column] < df.iloc[i-5:i][price_column].mean():  # 短期趨勢
                short_score += 1
            
            # 3. OBV確認 (權重1分)
            if df.iloc[i]['OBV'] < df.iloc[i]['OBV_MA']:
                short_score += 1
            
            # 4. ADX趨勢強度 (權重1分)
            if df.iloc[i]['ADX'] > adx_threshold:
                short_score += 1
            
            # 5. 價格動量 (權重1分)
            if df.iloc[i]['Price_Momentum'] < -2:  # 5期負動量
                short_score += 1
            
            # 6. 成交量確認 (權重1分)
            if df.iloc[i]['Volume_Ratio'] > 1.2:  # 成交量放大
                short_score += 1
            
            # 記錄評分
            df.iloc[i, df.columns.get_loc('Long_Score')] = long_score
            df.iloc[i, df.columns.get_loc('Short_Score')] = short_score
            
            # 根據評分決定入場
            if long_score >= min_signal_score and long_score > short_score and not pd.isna(current_atr):
                current_position = 1
                entry_price = current_price
                
                # 根據波動率調整ATR倍數
                vol_adj = 1.0
                if df.iloc[i]['Vol_Percentile'] > 0.8:  # 高波動
                    vol_adj = 1.3
                elif df.iloc[i]['Vol_Percentile'] < 0.2:  # 低波動
                    vol_adj = 0.8
                
                stop_loss = entry_price - (current_atr * atr_multiplier * vol_adj)
                take_profit = entry_price + (current_atr * atr_multiplier * vol_adj * 1.5)  # 1:1.5風險回報
                
                df.iloc[i, df.columns.get_loc('Signal')] = 1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                
            elif short_score >= min_signal_score and short_score > long_score and not pd.isna(current_atr):
                current_position = -1
                entry_price = current_price
                
                # 根據波動率調整ATR倍數
                vol_adj = 1.0
                if df.iloc[i]['Vol_Percentile'] > 0.8:  # 高波動
                    vol_adj = 1.3
                elif df.iloc[i]['Vol_Percentile'] < 0.2:  # 低波動
                    vol_adj = 0.8
                
                stop_loss = entry_price + (current_atr * atr_multiplier * vol_adj)
                take_profit = entry_price - (current_atr * atr_multiplier * vol_adj * 1.5)  # 1:1.5風險回報
                
                df.iloc[i, df.columns.get_loc('Signal')] = -1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
        
        # 如果有倉位，檢查出場條件
        elif current_position != 0:
            exit_triggered = False
            exit_reason = ''
            
            if current_position == 1:  # 多頭倉位
                if current_price <= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price >= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] > df.iloc[i]['CCB_Upper'] or
                      df.iloc[i][price_column] < df.iloc[i]['EMA_Trend'] * 0.98):  # 趨勢轉變
                    exit_triggered = True
                    exit_reason = 'Signal_Exit'
                    
            elif current_position == -1:  # 空頭倉位
                if current_price >= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price <= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] < df.iloc[i]['CCB_Lower'] or
                      df.iloc[i][price_column] > df.iloc[i]['EMA_Trend'] * 1.02):  # 趨勢轉變
                    exit_triggered = True
                    exit_reason = 'Signal_Exit'
            
            if exit_triggered:
                df.iloc[i, df.columns.get_loc('Signal')] = 0
                df.iloc[i, df.columns.get_loc('Exit_Reason')] = exit_reason
                current_position = 0
                entry_price = 0
                stop_loss = 0
                take_profit = 0
            else:
                # 持續持倉
                df.iloc[i, df.columns.get_loc('Signal')] = current_position
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
    
    return df

def optimize_balanced_parameters(df):
    """優化平衡版策略參數"""
    print("執行平衡版參數優化...")
    
    # 參數組合（更注重實用性）
    param_combinations = [
        {'window': 20, 'std_dev': 2.0, 'atr_multiplier': 1.2, 'ema_period': 50, 'min_score': 4},
        {'window': 18, 'std_dev': 1.8, 'atr_multiplier': 1.0, 'ema_period': 40, 'min_score': 3},
        {'window': 22, 'std_dev': 2.2, 'atr_multiplier': 1.4, 'ema_period': 60, 'min_score': 5},
        {'window': 16, 'std_dev': 1.9, 'atr_multiplier': 1.1, 'ema_period': 35, 'min_score': 3},
        {'window': 24, 'std_dev': 2.1, 'atr_multiplier': 1.3, 'ema_period': 55, 'min_score': 4},
        {'window': 15, 'std_dev': 2.0, 'atr_multiplier': 1.5, 'ema_period': 45, 'min_score': 4},
        {'window': 25, 'std_dev': 1.7, 'atr_multiplier': 1.6, 'ema_period': 50, 'min_score': 5},
        {'window': 20, 'std_dev': 2.3, 'atr_multiplier': 1.0, 'ema_period': 40, 'min_score': 3}
    ]
    
    best_score = -np.inf
    best_params = None
    results = []
    
    for i, params in enumerate(param_combinations):
        print(f"測試參數組合 {i+1}/{len(param_combinations)}: {params}")
        
        try:
            # 計算CCB指標和信號
            df_temp = df.copy()
            df_temp = chip_concentration_bands(
                df_temp, 
                window=params['window'], 
                std_dev=params['std_dev']
            )
            
            df_temp = ccb_balanced_entry_logic(
                df_temp,
                atr_multiplier=params['atr_multiplier'],
                ema_trend_period=params['ema_period'],
                min_signal_score=params['min_score']
            )
            
            # 計算收益
            df_temp['price_change'] = df_temp['close'].pct_change()
            df_temp['strategy_return'] = df_temp['Signal'].shift(1) * df_temp['price_change']
            
            # 計算績效指標
            returns = df_temp['strategy_return'].dropna()
            
            if len(returns) > 30 and returns.std() > 0:
                total_return = (1 + returns).prod() - 1
                annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1
                volatility = returns.std() * np.sqrt(365*6)
                sharpe = annual_return / volatility if volatility > 0 else 0
                
                # 計算最大回撤
                cumulative = (1 + returns).cumprod()
                rolling_max = cumulative.expanding().max()
                drawdown = (cumulative - rolling_max) / rolling_max
                max_drawdown = drawdown.min()
                
                # 計算勝率
                win_rate = (returns > 0).mean()
                
                result = {
                    **params,
                    'sharpe': sharpe,
                    'annual_return': annual_return,
                    'max_drawdown': max_drawdown,
                    'win_rate': win_rate,
                    'total_trades': len(returns[returns != 0])
                }
                
                results.append(result)
                
                print(f"  夏普比率: {sharpe:.4f}, 年化收益: {annual_return:.2%}, 勝率: {win_rate:.2%}, 交易次數: {result['total_trades']}")
                
                if sharpe > best_score:
                    best_score = sharpe
                    best_params = result
        
        except Exception as e:
            print(f"  參數組合失敗: {e}")
            continue
    
    print(f"\n平衡版優化完成！最佳夏普比率: {best_score:.4f}")
    return best_params, results

if __name__ == "__main__":
    print("籌碼集中帶突破策略 - 平衡版模組載入完成")

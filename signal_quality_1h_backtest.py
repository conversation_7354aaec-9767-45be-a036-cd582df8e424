#!/usr/bin/env python3
"""
1H信號品質回測系統
專注於信號品質測試，去除資金管理複雜性
測試多空力道+均線策略在1H時框下的純信號表現
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class SignalQuality1HBacktest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 測試幣種 - 包含主流幣和山寨幣
        self.symbols = [
            # 主流幣
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'SOLUSDT',
            # 山寨幣
            'ADAUSDT', 'DOGEUSDT', 'LINKUSDT', 'AVAXUSDT', 'ALGOUSDT',
            'MATICUSDT', 'DOTUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT',
            'FILUSDT', 'TRXUSDT', 'ETCUSDT', 'XLMUSDT', 'VETUSDT',
            'ICPUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT'
        ]
        
        # 1H策略參數
        self.ma_short = 8
        self.ma_long = 15
        self.ti_threshold = 0.3
        
        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0, 3.5, 4.0]
        
        # ATR週期
        self.atr_period = 14
        
        self.results = []
        
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR（平均真實波幅）"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    async def get_1h_data(self, symbol: str) -> pd.DataFrame:
        """獲取1H數據"""
        try:
            print(f"\n📊 獲取 {symbol} 1H 數據...")
            
            # 使用DataFetcher獲取1H數據
            data = await self.data_fetcher.get_latest_data(symbol, '1H')
            
            if data is None or data.empty:
                print(f"❌ {symbol} 1H 數據獲取失敗")
                return None
                
            # 檢查必要的列
            required_columns = ['Open', 'High', 'Low', 'Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} 1H 缺少必要列: {missing_columns}")
                return None
            
            # 移除NaN值
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                print(f"❌ {symbol} 1H 清理後數據不足: {len(data_clean)}")
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            # 計算實際時間跨度
            time_span = (data_clean.index[-1] - data_clean.index[0]).total_seconds() / (24 * 3600)
            
            print(f"✅ {symbol} 1H 數據處理完成: {len(data_clean)} 條記錄")
            print(f"   時間範圍: {data_clean.index[0]} 到 {data_clean.index[-1]}")
            print(f"   時間跨度: {time_span:.1f} 天")
            
            return data_clean
            
        except Exception as e:
            print(f"❌ {symbol} 1H 數據獲取失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame) -> list:
        """生成交易信號"""
        signals = []
        
        if len(data) < max(self.ma_short, self.ma_long, self.atr_period) + 10:
            return signals
        
        for i in range(max(self.ma_short, self.ma_long, self.atr_period), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            atr = data['atr'].iloc[i]
            
            ma_short_val = data[f'MA_{self.ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{self.ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{self.ma_short}'].iloc[i-1]
            
            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti) or pd.isna(atr):
                continue
            
            # 多頭信號條件
            long_conditions = [
                price > ma_short_val,                    # 價格突破短期均線
                prev_price <= prev_ma_short,             # 前一根K線價格在短期均線下方
                ti > self.ti_threshold,                  # Taker Intensity > 閾值
                ma_short_val > ma_long_val,              # 短期均線 > 長期均線
                concentration > 0                        # 籌碼集中度 > 0
            ]
            
            # 空頭信號條件
            short_conditions = [
                price < ma_short_val,                    # 價格跌破短期均線
                prev_price >= prev_ma_short,             # 前一根K線價格在短期均線上方
                ti < -self.ti_threshold,                 # Taker Intensity < -閾值
                ma_short_val < ma_long_val,              # 短期均線 < 長期均線
                concentration < 0                        # 籌碼集中度 < 0
            ]
            
            # 多頭信號（至少滿足4個條件）
            if sum(long_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(long_conditions),
                    'ma_short': ma_short_val,
                    'ma_long': ma_long_val
                })
            
            # 空頭信號（至少滿足4個條件）
            elif sum(short_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(short_conditions),
                    'ma_short': ma_short_val,
                    'ma_long': ma_long_val
                })
        
        return signals
    
    def execute_signal_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行信號交易（純信號品質測試）"""
        trades = []
        
        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']
            
            # 計算止盈止損價格（基於ATR）
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:  # SHORT
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 尋找出場點
            exit_result = self.find_exit_point(data, entry_time, entry_price, direction, 
                                             stop_loss_price, take_profit_price)
            
            if exit_result:
                # 計算價格變化百分比
                price_change_pct = exit_result['price_change_pct']
                
                # 計算最大浮虧
                max_adverse_excursion = self.calculate_mae(data, entry_time, exit_result['exit_time'], 
                                                         entry_price, direction)
                
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'atr': atr,
                    'price_change_pct': price_change_pct,
                    'max_adverse_excursion_pct': max_adverse_excursion,
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'concentration': signal['concentration'],
                    'conditions_met': signal['conditions_met'],
                    'ma_short': signal['ma_short'],
                    'ma_long': signal['ma_long']
                }
                
                trades.append(trade)
        
        return trades
    
    def find_exit_point(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                       stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點（只有止盈或止損兩種結局）"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            
            # 從下一根K線開始檢查
            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]
                
                if direction == 'LONG':
                    # 多頭：先檢查止損，再檢查止盈
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:  # SHORT
                    # 空頭：先檢查止損，再檢查止盈
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }
            
            # 如果到數據結束都沒有觸發止盈止損，用最後價格平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]
            
            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100
            
            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }
            
        except Exception as e:
            return None

    def calculate_mae(self, data: pd.DataFrame, entry_time, exit_time, entry_price: float, direction: str) -> float:
        """計算最大不利偏移"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            exit_idx = data.index.get_loc(exit_time)

            max_adverse = 0

            for i in range(entry_idx, exit_idx + 1):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]

                if direction == 'LONG':
                    adverse = (low / entry_price - 1) * 100
                    max_adverse = min(max_adverse, adverse)
                else:
                    adverse = (entry_price / high - 1) * 100
                    max_adverse = min(max_adverse, adverse)

            return max_adverse

        except Exception as e:
            return 0

    def calculate_signal_performance(self, trades: list) -> dict:
        """計算信號表現（純信號品質分析）"""
        if not trades:
            return {
                'total_signals': 0, 'long_signals': 0, 'short_signals': 0,
                'win_rate': 0, 'long_win_rate': 0, 'short_win_rate': 0,
                'avg_return_pct': 0, 'avg_win_pct': 0, 'avg_loss_pct': 0,
                'profit_factor': 0, 'max_adverse_excursion_pct': 0,
                'avg_holding_hours': 0, 'take_profit_rate': 0, 'stop_loss_rate': 0,
                'long_short_balance': 0, 'signal_quality_score': 0
            }

        # 基本統計
        total_signals = len(trades)
        long_trades = [t for t in trades if t['direction'] == 'LONG']
        short_trades = [t for t in trades if t['direction'] == 'SHORT']

        winning_trades = [t for t in trades if t['price_change_pct'] > 0]
        losing_trades = [t for t in trades if t['price_change_pct'] < 0]

        long_winning = [t for t in long_trades if t['price_change_pct'] > 0]
        short_winning = [t for t in short_trades if t['price_change_pct'] > 0]

        # 勝率計算
        win_rate = len(winning_trades) / total_signals * 100
        long_win_rate = len(long_winning) / len(long_trades) * 100 if long_trades else 0
        short_win_rate = len(short_winning) / len(short_trades) * 100 if short_trades else 0

        # 收益統計
        avg_return_pct = np.mean([t['price_change_pct'] for t in trades])
        avg_win_pct = np.mean([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        avg_loss_pct = np.mean([t['price_change_pct'] for t in losing_trades]) if losing_trades else 0

        # 盈虧比
        gross_profit = sum([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['price_change_pct'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        # 最大不利偏移
        max_mae = min([t['max_adverse_excursion_pct'] for t in trades])

        # 持倉時間
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])

        # 出場原因統計
        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])
        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])

        take_profit_rate = take_profit_count / total_signals * 100
        stop_loss_rate = stop_loss_count / total_signals * 100

        # 多空平衡度
        long_short_balance = abs(len(long_trades) - len(short_trades)) / total_signals * 100

        # 信號品質評分（綜合指標）
        signal_quality_score = (
            win_rate * 0.4 +                           # 勝率權重40%
            min(profit_factor * 10, 100) * 0.3 +       # 盈虧比權重30%
            (100 - long_short_balance) * 0.2 +         # 多空平衡權重20%
            take_profit_rate * 0.1                     # 止盈率權重10%
        )

        return {
            'total_signals': total_signals,
            'long_signals': len(long_trades),
            'short_signals': len(short_trades),
            'win_rate': win_rate,
            'long_win_rate': long_win_rate,
            'short_win_rate': short_win_rate,
            'avg_return_pct': avg_return_pct,
            'avg_win_pct': avg_win_pct,
            'avg_loss_pct': avg_loss_pct,
            'profit_factor': profit_factor,
            'max_adverse_excursion_pct': max_mae,
            'avg_holding_hours': avg_holding_hours,
            'take_profit_rate': take_profit_rate,
            'stop_loss_rate': stop_loss_rate,
            'long_short_balance': long_short_balance,
            'signal_quality_score': signal_quality_score
        }

    async def test_signal_quality(self, symbol: str, risk_reward_ratio: float) -> dict:
        """測試信號品質"""
        try:
            print(f"\n📊 測試 {symbol} 1H RR{risk_reward_ratio} 信號品質...")

            # 獲取1H數據
            data = await self.get_1h_data(symbol)

            if data is None or data.empty:
                print(f"❌ {symbol} 1H 數據獲取失敗")
                return None

            # 計算ATR和均線
            data['atr'] = self.calculate_atr(data, self.atr_period)
            data[f'MA_{self.ma_short}'] = data['Close'].rolling(window=self.ma_short).mean()
            data[f'MA_{self.ma_long}'] = data['Close'].rolling(window=self.ma_long).mean()

            # 生成信號
            signals = self.generate_signals(data)

            if len(signals) < 5:
                print(f"❌ {symbol} 1H 信號不足: {len(signals)}")
                return None

            print(f"✅ {symbol} 1H 生成信號: {len(signals)} 個")

            # 檢查多空平衡
            long_signals = [s for s in signals if s['type'] == 'LONG']
            short_signals = [s for s in signals if s['type'] == 'SHORT']

            if len(long_signals) == 0 or len(short_signals) == 0:
                print(f"⚠️ {symbol} 1H 信號不平衡: 多頭{len(long_signals)}個, 空頭{len(short_signals)}個")
                # 不直接返回None，繼續測試但會在結果中標記

            # 執行信號交易
            trades = self.execute_signal_trades(data, signals, risk_reward_ratio)

            if not trades:
                print(f"❌ {symbol} 1H 無有效交易")
                return None

            print(f"✅ {symbol} 1H 執行交易: {len(trades)} 筆")

            # 計算信號表現
            performance = self.calculate_signal_performance(trades)

            # 計算時間範圍
            time_span = (data.index[-1] - data.index[0]).total_seconds() / (24 * 3600)

            result = {
                'symbol': symbol,
                'risk_reward_ratio': risk_reward_ratio,
                'time_span_days': time_span,
                'data_points': len(data),
                'signals': len(signals),
                'signals_per_day': len(signals) / time_span,
                'long_signals': len(long_signals),
                'short_signals': len(short_signals),
                'trades': trades[:10],  # 保存前10筆交易詳情
                **performance
            }

            print(f"✅ {symbol} 1H RR{risk_reward_ratio}: 勝率{performance['win_rate']:.1f}%, 信號品質{performance['signal_quality_score']:.1f}")
            print(f"   多頭信號: {len(long_signals)}個 | 空頭信號: {len(short_signals)}個")
            print(f"   平均收益: {performance['avg_return_pct']:+.2f}% | 盈虧比: {performance['profit_factor']:.2f}")

            return result

        except Exception as e:
            print(f"❌ {symbol} 1H RR{risk_reward_ratio} 測試失敗: {e}")
            return None

    async def optimize_symbol_rr(self, symbol: str) -> list:
        """優化單一幣種的盈虧比"""
        print(f"\n🔍 優化 {symbol} 1H 信號品質...")

        results = []

        for rr in self.risk_reward_ratios:
            result = await self.test_signal_quality(symbol, rr)

            if result and result['total_signals'] > 0:
                results.append(result)

            # 避免API限制
            await asyncio.sleep(0.5)

        # 按信號品質評分排序
        results.sort(key=lambda x: x['signal_quality_score'], reverse=True)

        if results:
            best = results[0]
            print(f"✅ {symbol} 1H 最佳RR{best['risk_reward_ratio']}: 信號品質{best['signal_quality_score']:.1f}")
            print(f"   勝率: {best['win_rate']:.1f}% | 盈虧比: {best['profit_factor']:.2f}")
        else:
            print(f"❌ {symbol} 1H 無有效結果")

        return results

    async def run_1h_signal_backtest(self):
        """運行1H信號品質回測"""
        print("🚀 啟動1H信號品質回測系統")
        print("🎯 專注於信號品質測試，去除資金管理複雜性")
        print("📊 測試多空力道+均線策略在1H時框下的純信號表現")
        print("="*80)

        all_results = {}

        for symbol in self.symbols:
            print(f"\n🔍 測試 {symbol}...")

            try:
                results = await self.optimize_symbol_rr(symbol)
                all_results[symbol] = results

                if results:
                    self.results.append(results[0])  # 添加最佳結果

            except Exception as e:
                print(f"❌ {symbol} 優化失敗: {e}")
                all_results[symbol] = []

            # 避免API限制
            await asyncio.sleep(1)

        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()

        # 保存和分析結果
        summary_df = self.save_signal_results(all_results)
        self.analyze_signal_results()

        return all_results, summary_df

    def save_signal_results(self, all_results: dict):
        """保存信號品質回測結果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        results_dir = "Signal_Quality_1H_Results"
        os.makedirs(results_dir, exist_ok=True)

        summary_data = []
        detailed_trades = []

        for symbol in all_results:
            results = all_results[symbol]

            if results:
                best = results[0]

                summary_data.append({
                    'Symbol': symbol,
                    'Best_Risk_Reward_Ratio': best['risk_reward_ratio'],
                    'Time_Span_Days': round(best['time_span_days'], 1),
                    'Data_Points': best['data_points'],
                    'Total_Signals': best['total_signals'],
                    'Signals_Per_Day': round(best['signals_per_day'], 2),
                    'Long_Signals': best['long_signals'],
                    'Short_Signals': best['short_signals'],
                    'Long_Short_Balance_%': round(100 - best['long_short_balance'], 1),
                    'Win_Rate_%': round(best['win_rate'], 2),
                    'Long_Win_Rate_%': round(best['long_win_rate'], 2),
                    'Short_Win_Rate_%': round(best['short_win_rate'], 2),
                    'Avg_Return_%': round(best['avg_return_pct'], 2),
                    'Avg_Win_%': round(best['avg_win_pct'], 2),
                    'Avg_Loss_%': round(best['avg_loss_pct'], 2),
                    'Profit_Factor': round(best['profit_factor'], 2),
                    'Max_Adverse_Excursion_%': round(best['max_adverse_excursion_pct'], 2),
                    'Avg_Holding_Hours': round(best['avg_holding_hours'], 2),
                    'Take_Profit_Rate_%': round(best['take_profit_rate'], 2),
                    'Stop_Loss_Rate_%': round(best['stop_loss_rate'], 2),
                    'Signal_Quality_Score': round(best['signal_quality_score'], 2)
                })

                for trade in best['trades']:
                    detailed_trades.append({
                        'Symbol': symbol,
                        'RR_Ratio': best['risk_reward_ratio'],
                        'Entry_Time': trade['entry_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'Exit_Time': trade['exit_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'Direction': trade['direction'],
                        'Entry_Price': round(trade['entry_price'], 6),
                        'Exit_Price': round(trade['exit_price'], 6),
                        'Exit_Reason': trade['exit_reason'],
                        'Stop_Loss_Price': round(trade['stop_loss_price'], 6),
                        'Take_Profit_Price': round(trade['take_profit_price'], 6),
                        'ATR': round(trade['atr'], 6),
                        'Price_Change_%': round(trade['price_change_pct'], 2),
                        'Max_Adverse_Excursion_%': round(trade['max_adverse_excursion_pct'], 2),
                        'Holding_Hours': round(trade['holding_hours'], 2),
                        'TI': round(trade['ti'], 3),
                        'Concentration': round(trade['concentration'], 3),
                        'Conditions_Met': trade['conditions_met'],
                        'MA_Short': round(trade['ma_short'], 6),
                        'MA_Long': round(trade['ma_long'], 6)
                    })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values('Signal_Quality_Score', ascending=False)

            summary_file = f"{results_dir}/Signal_Quality_Summary_{timestamp}.csv"
            summary_df.to_csv(summary_file, index=False)

            print(f"\n✅ 1H信號品質回測綜合報告已保存: {summary_file}")

            if detailed_trades:
                trades_df = pd.DataFrame(detailed_trades)
                trades_file = f"{results_dir}/Signal_Quality_Trades_{timestamp}.csv"
                trades_df.to_csv(trades_file, index=False)

                print(f"✅ 詳細交易記錄已保存: {trades_file}")

            return summary_df

        return pd.DataFrame()

    def analyze_signal_results(self):
        """分析信號品質回測結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*80)
        print("🏆 1H信號品質回測分析報告")
        print("🎯 專注於信號品質，測試多空力道+均線策略")
        print("="*80)

        # 按信號品質評分排序
        self.results.sort(key=lambda x: x['signal_quality_score'], reverse=True)

        # 分類分析
        mainstream_coins = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'SOLUSDT']
        altcoins = [r for r in self.results if r['symbol'] not in mainstream_coins]
        mainstream_results = [r for r in self.results if r['symbol'] in mainstream_coins]

        print(f"💰 測試幣種: {len(self.results)} 個")
        print(f"   主流幣: {len(mainstream_results)} 個")
        print(f"   山寨幣: {len(altcoins)} 個")

        if self.results:
            print(f"\n🏆 前15個最佳信號品質策略:")
            for i, strategy in enumerate(self.results[:15], 1):
                coin_type = "主流" if strategy['symbol'] in mainstream_coins else "山寨"
                print(f"{i:2d}. {strategy['symbol']} ({coin_type}) RR{strategy['risk_reward_ratio']}")
                print(f"     信號品質: {strategy['signal_quality_score']:.1f} | 勝率: {strategy['win_rate']:.1f}%")
                print(f"     多頭勝率: {strategy['long_win_rate']:.1f}% | 空頭勝率: {strategy['short_win_rate']:.1f}%")
                print(f"     平均收益: {strategy['avg_return_pct']:+.2f}% | 盈虧比: {strategy['profit_factor']:.2f}")
                print(f"     信號數: {strategy['total_signals']}個 (多:{strategy['long_signals']}, 空:{strategy['short_signals']})")
                print(f"     止盈率: {strategy['take_profit_rate']:.1f}% | 止損率: {strategy['stop_loss_rate']:.1f}%")

        # 主流幣 vs 山寨幣對比
        if mainstream_results and altcoins:
            print(f"\n📊 主流幣 vs 山寨幣對比:")

            mainstream_avg_score = np.mean([r['signal_quality_score'] for r in mainstream_results])
            altcoin_avg_score = np.mean([r['signal_quality_score'] for r in altcoins])

            mainstream_avg_winrate = np.mean([r['win_rate'] for r in mainstream_results])
            altcoin_avg_winrate = np.mean([r['win_rate'] for r in altcoins])

            mainstream_avg_return = np.mean([r['avg_return_pct'] for r in mainstream_results])
            altcoin_avg_return = np.mean([r['avg_return_pct'] for r in altcoins])

            print(f"  主流幣:")
            print(f"    平均信號品質: {mainstream_avg_score:.1f}")
            print(f"    平均勝率: {mainstream_avg_winrate:.1f}%")
            print(f"    平均收益: {mainstream_avg_return:+.2f}%")
            print(f"    最佳: {mainstream_results[0]['symbol']} (品質{mainstream_results[0]['signal_quality_score']:.1f})")

            print(f"  山寨幣:")
            print(f"    平均信號品質: {altcoin_avg_score:.1f}")
            print(f"    平均勝率: {altcoin_avg_winrate:.1f}%")
            print(f"    平均收益: {altcoin_avg_return:+.2f}%")
            print(f"    最佳: {altcoins[0]['symbol']} (品質{altcoins[0]['signal_quality_score']:.1f})")

            if altcoin_avg_score > mainstream_avg_score:
                print(f"  🎯 結論: 山寨幣信號品質更優 (+{altcoin_avg_score - mainstream_avg_score:.1f})")
            else:
                print(f"  🎯 結論: 主流幣信號品質更優 (+{mainstream_avg_score - altcoin_avg_score:.1f})")

        # 多空平衡分析
        balanced_strategies = [r for r in self.results if r['long_short_balance'] < 30]  # 多空差異<30%
        print(f"\n⚖️ 多空平衡策略: {len(balanced_strategies)}/{len(self.results)} 個")

        if balanced_strategies:
            avg_balanced_score = np.mean([r['signal_quality_score'] for r in balanced_strategies])
            print(f"   平均信號品質: {avg_balanced_score:.1f}")

        # 整體統計
        avg_score = np.mean([r['signal_quality_score'] for r in self.results])
        avg_winrate = np.mean([r['win_rate'] for r in self.results])
        avg_signals_per_day = np.mean([r['signals_per_day'] for r in self.results])

        print(f"\n📊 整體統計:")
        print(f"   平均信號品質: {avg_score:.1f}")
        print(f"   平均勝率: {avg_winrate:.1f}%")
        print(f"   平均每天信號: {avg_signals_per_day:.2f}個")
        print(f"   最佳策略: {self.results[0]['symbol']} (品質{self.results[0]['signal_quality_score']:.1f})")

async def main():
    """主函數"""
    backtest = SignalQuality1HBacktest()

    try:
        print("🚀 開始1H信號品質回測...")
        results, summary_df = await backtest.run_1h_signal_backtest()

        print(f"\n🎉 1H信號品質回測完成！")
        print(f"📊 測試了 {len(backtest.symbols)} 個幣種")
        print(f"🔍 每個幣種測試了 {len(backtest.risk_reward_ratios)} 個盈虧比參數")
        print("🎯 專注於信號品質，驗證山寨幣表現假設")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())

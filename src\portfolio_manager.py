"""
投資組合管理模組
管理持倉、記錄交易、計算盈虧

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
from .logger_setup import setup_logger

class PortfolioManager:
    """投資組合管理器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("PortfolioManager")
        
        # 數據目錄
        self.data_dir = Path(__file__).parent.parent / "data"
        self.signals_dir = self.data_dir / "signals"
        self.performance_dir = self.data_dir / "performance"
        
        # 創建目錄
        self.signals_dir.mkdir(parents=True, exist_ok=True)
        self.performance_dir.mkdir(parents=True, exist_ok=True)
        
        # 持倉記錄
        self.positions = {}
        self.trades_history = []
        
        # 載入歷史數據
        self._load_historical_data()
    
    def _load_historical_data(self):
        """載入歷史交易數據"""
        try:
            # 載入信號記錄
            signals_file = self.signals_dir / "signals_history.csv"
            if signals_file.exists():
                self.signals_df = pd.read_csv(signals_file)
                self.logger.info(f"✅ 載入 {len(self.signals_df)} 條歷史信號")
            else:
                self.signals_df = pd.DataFrame()
            
            # 載入交易記錄
            trades_file = self.performance_dir / "trades_history.csv"
            if trades_file.exists():
                self.trades_df = pd.read_csv(trades_file)
                self.logger.info(f"✅ 載入 {len(self.trades_df)} 條歷史交易")
            else:
                self.trades_df = pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"❌ 歷史數據載入失敗: {e}")
            self.signals_df = pd.DataFrame()
            self.trades_df = pd.DataFrame()
    
    def record_signal(self, signal: Dict[str, Any], coin: str, price: float, timestamp: datetime):
        """記錄交易信號"""
        try:
            signal_record = {
                'timestamp': timestamp,
                'coin': coin,
                'action': signal.get('action'),
                'price': price,
                'signal_type': signal.get('signal_type'),
                'concentration': signal.get('concentration'),
                'ccb_position': signal.get('ccb_position'),
                'long_intensity': signal.get('long_intensity'),
                'short_intensity': signal.get('short_intensity'),
                'long_percentile': signal.get('long_percentile'),
                'short_percentile': signal.get('short_percentile'),
                'confidence': signal.get('confidence')
            }
            
            # 添加到DataFrame
            new_row = pd.DataFrame([signal_record])
            self.signals_df = pd.concat([self.signals_df, new_row], ignore_index=True)
            
            # 保存到CSV
            signals_file = self.signals_dir / "signals_history.csv"
            self.signals_df.to_csv(signals_file, index=False)
            
            self.logger.info(f"✅ {coin} 信號記錄已保存")
            
        except Exception as e:
            self.logger.error(f"❌ 信號記錄失敗: {e}")
    
    def update_position(self, coin: str, data: pd.DataFrame, signal: Optional[Dict[str, Any]]):
        """更新持倉狀態"""
        try:
            current_price = data['Close'].iloc[-1]
            timestamp = data.index[-1]
            
            if signal:
                action = signal.get('action')
                
                if action in ['LONG', 'SHORT']:
                    # 開倉
                    self.positions[coin] = {
                        'direction': action,
                        'entry_price': current_price,
                        'entry_time': timestamp,
                        'quantity': 1.0,  # 標準化數量
                        'status': 'OPEN'
                    }
                    
                elif action in ['CLOSE_LONG', 'CLOSE_SHORT']:
                    # 平倉
                    if coin in self.positions:
                        position = self.positions[coin]
                        
                        # 記錄交易
                        trade_record = self._create_trade_record(
                            coin, position, current_price, timestamp
                        )
                        
                        if trade_record:
                            self.trades_history.append(trade_record)
                            self._save_trade_record(trade_record)
                        
                        # 清除持倉
                        del self.positions[coin]
            
        except Exception as e:
            self.logger.error(f"❌ {coin} 持倉更新失敗: {e}")
    
    def _create_trade_record(self, coin: str, position: Dict[str, Any], 
                           exit_price: float, exit_time: datetime) -> Optional[Dict[str, Any]]:
        """創建交易記錄"""
        try:
            entry_price = position['entry_price']
            entry_time = position['entry_time']
            direction = position['direction']
            
            # 計算盈虧
            if direction == 'LONG':
                pnl_pct = (exit_price / entry_price - 1) * 100
            else:  # SHORT
                pnl_pct = (entry_price / exit_price - 1) * 100
            
            # 計算持倉時間
            holding_time = exit_time - entry_time
            holding_hours = holding_time.total_seconds() / 3600
            
            trade_record = {
                'coin': coin,
                'direction': direction,
                'entry_time': entry_time,
                'exit_time': exit_time,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'pnl_pct': pnl_pct,
                'pnl_amount': pnl_pct,  # 假設100%資金
                'holding_hours': holding_hours,
                'status': 'CLOSED'
            }
            
            return trade_record
            
        except Exception as e:
            self.logger.error(f"❌ 交易記錄創建失敗: {e}")
            return None
    
    def _save_trade_record(self, trade_record: Dict[str, Any]):
        """保存交易記錄"""
        try:
            # 添加到DataFrame
            new_row = pd.DataFrame([trade_record])
            self.trades_df = pd.concat([self.trades_df, new_row], ignore_index=True)
            
            # 保存到CSV
            trades_file = self.performance_dir / "trades_history.csv"
            self.trades_df.to_csv(trades_file, index=False)
            
            self.logger.info(f"✅ {trade_record['coin']} 交易記錄已保存")
            
        except Exception as e:
            self.logger.error(f"❌ 交易記錄保存失敗: {e}")
    
    def calculate_pnl(self, coin: str, current_price: float) -> Optional[Dict[str, Any]]:
        """計算盈虧報告"""
        try:
            if not self.trades_history:
                return None
            
            # 獲取最新的已平倉交易
            latest_trade = None
            for trade in reversed(self.trades_history):
                if trade['coin'] == coin and trade['status'] == 'CLOSED':
                    latest_trade = trade
                    break
            
            if not latest_trade:
                return None
            
            pnl_report = {
                'coin': coin,
                'trade_type': latest_trade['direction'],
                'entry_price': latest_trade['entry_price'],
                'exit_price': latest_trade['exit_price'],
                'pnl_pct': latest_trade['pnl_pct'],
                'pnl_amount': latest_trade['pnl_amount'],
                'holding_hours': latest_trade['holding_hours'],
                'exit_time': latest_trade['exit_time'].strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return pnl_report
            
        except Exception as e:
            self.logger.error(f"❌ {coin} 盈虧計算失敗: {e}")
            return None
    
    def generate_daily_report(self) -> str:
        """生成每日報告"""
        try:
            today = datetime.now().date()
            
            # 篩選今日交易
            if not self.trades_df.empty:
                today_trades = self.trades_df[
                    pd.to_datetime(self.trades_df['exit_time']).dt.date == today
                ]
            else:
                today_trades = pd.DataFrame()
            
            total_trades = len(today_trades)
            
            if total_trades == 0:
                return f"""
📊 每日交易報告 - {today}
━━━━━━━━━━━━━━━━━━━━
📈 總交易次數: 0
🎯 獲利交易: 0
📊 勝率: 0.0%
💰 總盈虧: $0.00
━━━━━━━━━━━━━━━━━━━━
今日無交易記錄
                """
            
            winning_trades = len(today_trades[today_trades['pnl_pct'] > 0])
            win_rate = (winning_trades / total_trades) * 100
            total_pnl = today_trades['pnl_amount'].sum()
            
            # 各幣種詳情
            coin_details = {}
            for coin in ['PEPE', 'XRP', 'SOL']:
                coin_trades = today_trades[today_trades['coin'] == coin]
                if not coin_trades.empty:
                    coin_details[coin] = {
                        'trades': len(coin_trades),
                        'pnl': coin_trades['pnl_amount'].sum()
                    }
            
            report_data = {
                'date': today.strftime('%Y-%m-%d'),
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'coin_details': coin_details
            }
            
            return self._format_daily_report(report_data)
            
        except Exception as e:
            self.logger.error(f"❌ 每日報告生成失敗: {e}")
            return "❌ 每日報告生成失敗"
    
    def _format_daily_report(self, data: Dict[str, Any]) -> str:
        """格式化每日報告"""
        report = f"""
📊 每日交易報告 - {data['date']}
━━━━━━━━━━━━━━━━━━━━
📈 總交易次數: {data['total_trades']}
🎯 獲利交易: {data['winning_trades']}
📊 勝率: {data['win_rate']:.1f}%
💰 總盈虧: ${data['total_pnl']:+.2f}
━━━━━━━━━━━━━━━━━━━━

各幣種表現:
        """
        
        for coin, details in data['coin_details'].items():
            pnl = details['pnl']
            trades = details['trades']
            emoji = '📈' if pnl > 0 else '📉' if pnl < 0 else '➖'
            report += f"\n{emoji} {coin}: ${pnl:+.2f} ({trades}筆)"
        
        report += "\n━━━━━━━━━━━━━━━━━━━━"
        
        return report
    
    def get_current_positions(self) -> Dict[str, Dict[str, Any]]:
        """獲取當前持倉"""
        return self.positions.copy()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取績效統計"""
        try:
            if self.trades_df.empty:
                return {}
            
            total_trades = len(self.trades_df)
            winning_trades = len(self.trades_df[self.trades_df['pnl_pct'] > 0])
            win_rate = (winning_trades / total_trades) * 100
            
            avg_win = self.trades_df[self.trades_df['pnl_pct'] > 0]['pnl_pct'].mean()
            avg_loss = self.trades_df[self.trades_df['pnl_pct'] < 0]['pnl_pct'].mean()
            
            total_return = self.trades_df['pnl_pct'].sum()
            
            stats = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'avg_win': avg_win if not pd.isna(avg_win) else 0,
                'avg_loss': avg_loss if not pd.isna(avg_loss) else 0,
                'total_return': total_return
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ 績效統計計算失敗: {e}")
            return {}

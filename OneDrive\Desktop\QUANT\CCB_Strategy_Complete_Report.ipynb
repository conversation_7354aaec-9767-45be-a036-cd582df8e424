{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 籌碼集中帶突破策略 - 機構級策略報告\n", "\n", "## 策略概述\n", "\n", "本報告展示了一個創新的量化交易策略：**籌碼集中帶突破策略**。該策略基於布林帶的核心原理，但將傳統的價格移動平均線替換為Blave數據網的籌碼集中度指標，構建「籌碼集中帶」來進行突破交易。\n", "\n", "### 核心創新點：\n", "1. **數據源創新**：使用籌碼集中度替代價格作為布林帶的基礎數據\n", "2. **理論結合**：將市場微觀結構分析與技術分析完美結合\n", "3. **時間框架**：4H時間框架，適合中短期交易\n", "4. **風險控制**：通過帶寬擴張過濾減少假信號\n", "\n", "### 策略邏輯：\n", "- **做多信號**：當籌碼集中度跌破下軌時，表示籌碼分散，可能是買入機會\n", "- **做空信號**：當籌碼集中度突破上軌時，表示籌碼過度集中，可能出現拋售壓力\n", "- **出場信號**：價格回歸中軌附近時平倉\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 導入必要的庫和模組\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 設置中文字體\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 導入策略模組\n", "from ccb_backtest_execution import run_complete_ccb_strategy\n", "\n", "print(\"籌碼集中帶突破策略 - 系統初始化完成\")\n", "print(\"準備執行完整的機構級策略回測...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 策略執行與數據獲取\n", "\n", "首先執行完整的策略回測系統，包括：\n", "- 數據獲取（BTC 4H數據 + 籌碼集中度數據）\n", "- 參數優化\n", "- 策略回測\n", "- 步進分析\n", "- 績效評估"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 執行完整的策略回測\n", "print(\"開始執行籌碼集中帶突破策略完整回測...\")\n", "print(\"=\"*60)\n", "\n", "# 運行策略\n", "strategy_backtest = run_complete_ccb_strategy()\n", "\n", "print(\"\\n策略回測執行完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 詳細績效分析\n", "\n", "### 2.1 核心績效指標"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 提取回測結果\n", "if hasattr(strategy_backtest, 'results') and 'backtest_data' in strategy_backtest.results:\n", "    df = strategy_backtest.results['backtest_data']\n", "    performance = strategy_backtest.results.get('performance', {})\n", "    \n", "    # 創建績效指標表格\n", "    print(\"\\n=== 籌碼集中帶策略核心績效指標 ===\")\n", "    print(\"-\" * 50)\n", "    \n", "    performance_df = pd.DataFrame(list(performance.items()), columns=['指標', '數值'])\n", "    print(performance_df.to_string(index=False))\n", "    \n", "    # 計算額外的風險指標\n", "    if 'strategy_return' in df.columns:\n", "        returns = df['strategy_return'].dropna()\n", "        \n", "        print(\"\\n=== 風險分析指標 ===\")\n", "        print(f\"VaR (95%): {np.percentile(returns, 5):.4f}\")\n", "        print(f\"CVaR (95%): {returns[returns <= np.percentile(returns, 5)].mean():.4f}\")\n", "        print(f\"偏度 (Skewness): {returns.skew():.4f}\")\n", "        print(f\"峰度 (<PERSON><PERSON>): {returns.kurtosis():.4f}\")\n", "        \n", "        # 計算月度收益統計\n", "        monthly_returns = returns.resample('M').sum()\n", "        print(f\"\\n=== 月度收益統計 ===\")\n", "        print(f\"正收益月份佔比: {(monthly_returns > 0).mean():.2%}\")\n", "        print(f\"最佳月份收益: {monthly_returns.max():.2%}\")\n", "        print(f\"最差月份收益: {monthly_returns.min():.2%}\")\n", "\n", "else:\n", "    print(\"錯誤：無法獲取回測結果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 策略視覺化分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 創建詳細的策略分析圖表\n", "if 'df' in locals():\n", "    fig, axes = plt.subplots(2, 2, figsize=(20, 15))\n", "    \n", "    # 子圖1：累積收益比較\n", "    axes[0,0].plot(df.index, df['cumulative_strategy'], label='CCB策略', linewidth=2, color='blue')\n", "    axes[0,0].plot(df.index, df['cumulative_benchmark'], label='買入持有', linewidth=2, color='gray')\n", "    axes[0,0].set_title('累積收益比較', fontsize=14, fontweight='bold')\n", "    axes[0,0].set_ylabel('累積收益')\n", "    axes[0,0].legend()\n", "    axes[0,0].grid(True, alpha=0.3)\n", "    \n", "    # 子圖2：回撤分析\n", "    strategy_cumulative = df['cumulative_strategy']\n", "    rolling_max = strategy_cumulative.expanding().max()\n", "    drawdown = (strategy_cumulative - rolling_max) / rolling_max\n", "    \n", "    axes[0,1].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')\n", "    axes[0,1].plot(df.index, drawdown, color='red', linewidth=1)\n", "    axes[0,1].set_title('策略回撤分析', fontsize=14, fontweight='bold')\n", "    axes[0,1].set_ylabel('回撤幅度')\n", "    axes[0,1].grid(True, alpha=0.3)\n", "    \n", "    # 子圖3：收益分佈\n", "    if 'strategy_return' in df.columns:\n", "        returns = df['strategy_return'].dropna()\n", "        axes[1,0].hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "        axes[1,0].axvline(returns.mean(), color='red', linestyle='--', label=f'平均: {returns.mean():.4f}')\n", "        axes[1,0].axvline(returns.median(), color='green', linestyle='--', label=f'中位數: {returns.median():.4f}')\n", "        axes[1,0].set_title('策略收益分佈', fontsize=14, fontweight='bold')\n", "        axes[1,0].set_xlabel('收益率')\n", "        axes[1,0].set_ylabel('頻次')\n", "        axes[1,0].legend()\n", "        axes[1,0].grid(True, alpha=0.3)\n", "    \n", "    # 子圖4：滾動夏普比率\n", "    if 'strategy_return' in df.columns:\n", "        rolling_sharpe = df['strategy_return'].rolling(window=30*6).apply(  # 30天窗口，4H數據\n", "            lambda x: x.mean() / x.std() * np.sqrt(365*6) if x.std() > 0 else 0\n", "        )\n", "        axes[1,1].plot(df.index, rolling_sharpe, color='purple', linewidth=1)\n", "        axes[1,1].axhline(y=1, color='red', linestyle='--', alpha=0.5, label='夏普=1')\n", "        axes[1,1].axhline(y=0, color='gray', linestyle='-', alpha=0.5)\n", "        axes[1,1].set_title('滾動夏普比率 (30天)', fontsize=14, fontweight='bold')\n", "        axes[1,1].set_ylabel('夏普比率')\n", "        axes[1,1].legend()\n", "        axes[1,1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "else:\n", "    print(\"無法生成圖表：缺少數據\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 步進分析結果\n", "\n", "步進分析（Walk-Forward Analysis）是驗證策略穩健性的重要方法，通過滾動窗口測試策略在不同市場環境下的表現。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步進分析結果展示\n", "if hasattr(strategy_backtest, 'results') and 'walk_forward' in strategy_backtest.results:\n", "    wf_results = strategy_backtest.results['walk_forward']\n", "    \n", "    if wf_results:\n", "        print(\"\\n=== 步進分析結果 ===\")\n", "        print(f\"總測試期間數: {len(wf_results)}\")\n", "        \n", "        # 創建步進分析DataFrame\n", "        wf_df = pd.DataFrame(wf_results)\n", "        \n", "        # 顯示部分結果\n", "        display_columns = ['start_date', 'end_date', 'Sharpe Ratio', 'Annual Return', 'Max Drawdown', 'Win Rate']\n", "        available_columns = [col for col in display_columns if col in wf_df.columns]\n", "        \n", "        if available_columns:\n", "            print(\"\\n各期間績效表現:\")\n", "            print(wf_df[available_columns].head(10).to_string(index=False))\n", "        \n", "        # 步進分析統計圖表\n", "        if len(wf_results) > 1:\n", "            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "            \n", "            # 夏普比率時間序列\n", "            if '<PERSON>' in wf_df.columns:\n", "                try:\n", "                    sharpe_values = [float(x) for x in wf_df['Sharpe Ratio'] if x != 'inf']\n", "                    ax1.plot(range(len(sharpe_values)), sharpe_values, marker='o', linewidth=2)\n", "                    ax1.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='夏普=1')\n", "                    ax1.axhline(y=0, color='gray', linestyle='-', alpha=0.5)\n", "                    ax1.set_title('步進分析 - 夏普比率變化')\n", "                    ax1.set_ylabel('夏普比率')\n", "                    ax1.set_xlabel('測試期間')\n", "                    ax1.legend()\n", "                    ax1.grid(True, alpha=0.3)\n", "                except:\n", "                    ax1.text(0.5, 0.5, '夏普比率數據處理錯誤', ha='center', va='center', transform=ax1.transAxes)\n", "            \n", "            # 年化收益分佈\n", "            if 'Annual Return' in wf_df.columns:\n", "                try:\n", "                    annual_returns = [float(x.strip('%'))/100 for x in wf_df['Annual Return'] if '%' in str(x)]\n", "                    ax2.hist(annual_returns, bins=10, alpha=0.7, color='lightgreen', edgecolor='black')\n", "                    ax2.axvline(np.mean(annual_returns), color='red', linestyle='--', \n", "                               label=f'平均: {np.mean(annual_returns):.2%}')\n", "                    ax2.set_title('步進分析 - 年化收益分佈')\n", "                    ax2.set_xlabel('年化收益率')\n", "                    ax2.set_ylabel('頻次')\n", "                    ax2.legend()\n", "                    ax2.grid(True, alpha=0.3)\n", "                except:\n", "                    ax2.text(0.5, 0.5, '年化收益數據處理錯誤', ha='center', va='center', transform=ax2.transAxes)\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "    \n", "    else:\n", "        print(\"步進分析未產生結果\")\n", "else:\n", "    print(\"未執行步進分析\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 策略優化建議\n", "\n", "基於回測結果，提出策略改進建議："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 策略優化建議分析\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"籌碼集中帶突破策略 - 優化建議報告\")\n", "print(\"=\"*60)\n", "\n", "optimization_suggestions = [\n", "    \"1. 信號過濾優化\",\n", "    \"   - 加入成交量確認機制\",\n", "    \"   - 結合RSI等動量指標避免假突破\",\n", "    \"   - 考慮市場波動率調整參數\",\n", "    \"\",\n", "    \"2. 風險管理增強\",\n", "    \"   - 實施動態止損機制\",\n", "    \"   - 加入倉位管理系統\",\n", "    \"   - 設置最大回撤控制\",\n", "    \"\",\n", "    \"3. 參數自適應\",\n", "    \"   - 根據市場波動率動態調整窗口期\",\n", "    \"   - 實施機器學習參數優化\",\n", "    \"   - 考慮市場制度變化的影響\",\n", "    \"\",\n", "    \"4. 多時間框架整合\",\n", "    \"   - 結合日線趨勢過濾\",\n", "    \"   - 加入1H級別的精確入場\",\n", "    \"   - 實施多週期確認機制\",\n", "    \"\",\n", "    \"5. 實盤考慮因素\",\n", "    \"   - 交易成本和滑點建模\",\n", "    \"   - 流動性風險評估\",\n", "    \"   - 資金容量分析\"\n", "]\n", "\n", "for suggestion in optimization_suggestions:\n", "    print(suggestion)\n", "\n", "# 根據實際績效給出具體建議\n", "if 'performance' in locals():\n", "    print(\"\\n=== 基於當前績效的具體建議 ===\")\n", "    \n", "    try:\n", "        sharpe = float(performance.get('<PERSON>', '0'))\n", "        win_rate = float(performance.get('Win Rate', '0%').strip('%')) / 100\n", "        \n", "        if sharpe < 1.0:\n", "            print(\"- 夏普比率偏低，建議加強信號質量和風險控制\")\n", "        \n", "        if win_rate < 0.5:\n", "            print(\"- 勝率較低，考慮調整入場條件或加入趨勢過濾\")\n", "        \n", "        if sharpe > 1.5 and win_rate > 0.6:\n", "            print(\"- 策略表現優秀，可考慮增加資金配置\")\n", "            \n", "    except:\n", "        print(\"- 建議進行更詳細的績效分析\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"報告完成 - 籌碼集中帶突破策略機構級分析\")\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 總結與展望\n", "\n", "### 策略創新點總結：\n", "\n", "1. **理論創新**：首次將籌碼集中度指標與布林帶技術結合，開創了新的技術分析方法\n", "\n", "2. **數據優勢**：利用Blave平台的高質量籌碼數據，提供了傳統技術分析無法獲得的市場微觀結構信息\n", "\n", "3. **系統化方法**：整合了參數優化、步進分析、風險控制等完整的量化交易框架\n", "\n", "4. **實用性強**：4H時間框架適合大多數交易者，策略邏輯清晰易於實施\n", "\n", "### 未來發展方向：\n", "\n", "- **多資產擴展**：將策略應用到其他加密貨幣和傳統金融資產\n", "- **機器學習整合**：使用深度學習模型優化信號生成\n", "- **實時交易系統**：開發自動化交易執行系統\n", "- **風險管理升級**：實施更sophisticated的風險控制機制\n", "\n", "---\n", "\n", "**免責聲明**：本報告僅供學術研究和教育目的，不構成投資建議。任何投資決策應基於個人風險承受能力和充分的市場研究。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}
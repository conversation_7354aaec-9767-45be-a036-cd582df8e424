#!/usr/bin/env python3
"""
分析回測詳細信息：時間範圍、信號頻率、進出場記錄
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

async def analyze_backtest_details():
    """分析回測詳細信息"""
    
    print("🔍 分析回測詳細信息...")
    
    # 初始化配置
    config = ConfigManager()
    data_fetcher = DataFetcher(config)
    
    # 測試BNBUSDT 4H策略（最佳策略之一）
    symbol = "BNBUSDT"
    timeframe = "4H"
    
    print(f"\n📊 分析 {symbol} {timeframe} 策略詳情...")
    
    try:
        # 獲取數據
        data = await data_fetcher.get_latest_data(symbol, timeframe)
        
        if data is None or data.empty:
            print(f"❌ {symbol} 數據獲取失敗")
            return
            
        print(f"✅ 數據獲取成功: {len(data)} 條記錄")
        print(f"📅 時間範圍: {data.index[0]} 到 {data.index[-1]}")
        
        # 計算實際天數
        time_span = data.index[-1] - data.index[0]
        total_days = time_span.total_seconds() / (24 * 3600)
        print(f"⏰ 總時間跨度: {total_days:.1f} 天")
        
        # 檢查數據列
        print(f"📋 可用數據列: {list(data.columns)}")
        
        # 計算Taker Intensity淨值
        data['taker_intensity'] = data['long_taker_intensity'] - data['short_taker_intensity']
        
        # 使用最佳參數：TI閾值0.3, MA(12,15)
        ti_threshold = 0.3
        ma_short = 12
        ma_long = 15
        
        # 計算均線
        data['MA_12'] = data['Close'].rolling(window=ma_short).mean()
        data['MA_15'] = data['Close'].rolling(window=ma_long).mean()
        
        print(f"\n📈 策略參數:")
        print(f"   TI閾值: {ti_threshold}")
        print(f"   短期均線: MA{ma_short}")
        print(f"   長期均線: MA{ma_long}")
        
        # 生成詳細信號
        signals = generate_detailed_signals(data, ti_threshold, ma_short, ma_long)
        
        print(f"\n📊 信號統計:")
        print(f"   總信號數: {len(signals)}")
        print(f"   平均每天信號: {len(signals) / total_days:.2f} 個")
        
        if signals:
            long_signals = [s for s in signals if s['type'] == 'LONG']
            short_signals = [s for s in signals if s['type'] == 'SHORT']
            
            print(f"   多頭信號: {len(long_signals)} ({len(long_signals)/len(signals)*100:.1f}%)")
            print(f"   空頭信號: {len(short_signals)} ({len(short_signals)/len(signals)*100:.1f}%)")
            
            # 分析交易記錄
            trades = analyze_trades(signals, data)
            
            if trades:
                print(f"\n💰 交易分析:")
                print(f"   總交易數: {len(trades)}")
                print(f"   平均每天交易: {len(trades) / total_days:.2f} 筆")
                
                # 顯示前10筆交易詳情
                print(f"\n📋 前10筆交易詳情:")
                print("="*100)
                print(f"{'序號':<4} {'方向':<6} {'進場時間':<20} {'出場時間':<20} {'進場價':<10} {'出場價':<10} {'盈虧%':<8} {'持倉時間':<10}")
                print("="*100)
                
                for i, trade in enumerate(trades[:10], 1):
                    holding_time = f"{trade['holding_hours']:.1f}h"
                    print(f"{i:<4} {trade['direction']:<6} {trade['entry_time'].strftime('%Y-%m-%d %H:%M'):<20} "
                          f"{trade['exit_time'].strftime('%Y-%m-%d %H:%M'):<20} {trade['entry_price']:<10.4f} "
                          f"{trade['exit_price']:<10.4f} {trade['pnl_pct']:+7.2f}% {holding_time:<10}")
                
                # 統計分析
                winning_trades = [t for t in trades if t['pnl_pct'] > 0]
                losing_trades = [t for t in trades if t['pnl_pct'] < 0]
                
                print(f"\n📈 交易統計:")
                print(f"   獲利交易: {len(winning_trades)} 筆")
                print(f"   虧損交易: {len(losing_trades)} 筆")
                print(f"   勝率: {len(winning_trades)/len(trades)*100:.1f}%")
                
                if winning_trades:
                    avg_win = np.mean([t['pnl_pct'] for t in winning_trades])
                    print(f"   平均獲利: {avg_win:.2f}%")
                
                if losing_trades:
                    avg_loss = np.mean([t['pnl_pct'] for t in losing_trades])
                    print(f"   平均虧損: {avg_loss:.2f}%")
                
                total_return = sum([t['pnl_pct'] for t in trades])
                print(f"   總收益: {total_return:.2f}%")
                
                avg_holding = np.mean([t['holding_hours'] for t in trades])
                print(f"   平均持倉時間: {avg_holding:.1f} 小時")
        
        # 關閉數據獲取器
        if hasattr(data_fetcher, 'session') and data_fetcher.session:
            await data_fetcher.session.close()
            
    except Exception as e:
        print(f"❌ 分析失敗: {e}")

def generate_detailed_signals(data: pd.DataFrame, ti_threshold: float, ma_short: int, ma_long: int) -> list:
    """生成詳細信號記錄"""
    signals = []
    
    if len(data) < max(ma_short, ma_long) + 10:
        return signals
    
    for i in range(max(ma_short, ma_long), len(data)):
        price = data['Close'].iloc[i]
        prev_price = data['Close'].iloc[i-1]
        ti = data['taker_intensity'].iloc[i]
        concentration = data['concentration'].iloc[i]
        
        ma_short_val = data[f'MA_{ma_short}'].iloc[i]
        ma_long_val = data[f'MA_{ma_long}'].iloc[i]
        prev_ma_short = data[f'MA_{ma_short}'].iloc[i-1]
        
        # 跳過NaN值
        if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti):
            continue
        
        # 增強版多頭信號條件
        long_conditions = [
            price > ma_short_val,  # 價格在短期均線上方
            prev_price <= prev_ma_short,  # 突破短期均線
            ti > ti_threshold,  # 積極做多
            ma_short_val > ma_long_val,  # 短期均線在長期均線上方
            concentration > 0  # 籌碼集中度為正
        ]
        
        # 增強版空頭信號條件
        short_conditions = [
            price < ma_short_val,  # 價格在短期均線下方
            prev_price >= prev_ma_short,  # 跌破短期均線
            ti < -ti_threshold,  # 積極做空
            ma_short_val < ma_long_val,  # 短期均線在長期均線下方
            concentration < 0  # 籌碼集中度為負
        ]
        
        # 多頭信號
        if sum(long_conditions) >= 4:  # 至少滿足4個條件
            signals.append({
                'timestamp': data.index[i],
                'type': 'LONG',
                'price': price,
                'ti': ti,
                'concentration': concentration,
                'ma_short': ma_short_val,
                'ma_long': ma_long_val,
                'conditions_met': sum(long_conditions),
                'conditions_detail': {
                    'price_above_ma_short': long_conditions[0],
                    'breakout_ma_short': long_conditions[1],
                    'ti_bullish': long_conditions[2],
                    'ma_trend_up': long_conditions[3],
                    'concentration_positive': long_conditions[4]
                }
            })
        
        # 空頭信號
        elif sum(short_conditions) >= 4:  # 至少滿足4個條件
            signals.append({
                'timestamp': data.index[i],
                'type': 'SHORT',
                'price': price,
                'ti': ti,
                'concentration': concentration,
                'ma_short': ma_short_val,
                'ma_long': ma_long_val,
                'conditions_met': sum(short_conditions),
                'conditions_detail': {
                    'price_below_ma_short': short_conditions[0],
                    'breakdown_ma_short': short_conditions[1],
                    'ti_bearish': short_conditions[2],
                    'ma_trend_down': short_conditions[3],
                    'concentration_negative': short_conditions[4]
                }
            })
    
    return signals

def analyze_trades(signals: list, data: pd.DataFrame) -> list:
    """分析交易記錄"""
    if len(signals) < 2:
        return []
    
    trades = []
    
    # 配對交易
    for i in range(0, len(signals)-1, 2):
        entry = signals[i]
        exit_signal = signals[i+1] if i+1 < len(signals) else None
        
        if exit_signal:
            # 計算持倉時間
            holding_hours = (exit_signal['timestamp'] - entry['timestamp']).total_seconds() / 3600
            
            # 計算盈虧
            if entry['type'] == 'LONG':
                pnl_pct = (exit_signal['price'] / entry['price'] - 1) * 100
            else:
                pnl_pct = (entry['price'] / exit_signal['price'] - 1) * 100
            
            trades.append({
                'entry_time': entry['timestamp'],
                'exit_time': exit_signal['timestamp'],
                'direction': entry['type'],
                'entry_price': entry['price'],
                'exit_price': exit_signal['price'],
                'pnl_pct': pnl_pct,
                'holding_hours': holding_hours,
                'entry_ti': entry['ti'],
                'exit_ti': exit_signal['ti'],
                'entry_concentration': entry['concentration'],
                'exit_concentration': exit_signal['concentration'],
                'entry_conditions': entry['conditions_met'],
                'exit_conditions': exit_signal['conditions_met']
            })
    
    return trades

if __name__ == "__main__":
    asyncio.run(analyze_backtest_details())

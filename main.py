#!/usr/bin/env python3
"""
主要多空策略信號系統
替代CCB策略框架的新一代報單系統
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import json
import logging
from typing import Dict, List, Optional
import aiohttp

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('signal_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MainSignalSystem:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 從配置文件加載活躍策略
        self.load_active_strategies()
        
        # RSI濾網參數
        self.rsi_period = 14
        self.rsi_long_threshold = 70
        self.rsi_short_threshold = 30
        
        # Taker Intensity參數
        self.ti_lookback = 24
        self.confidence_level = 0.70
        
        # ATR週期
        self.atr_period = 14
        
        # CSV文件路徑
        self.trades_csv_path = "data/trades_record.csv"
        self.ensure_csv_exists()
        
        # Telegram配置
        self.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN')
        self.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID')

    def ensure_csv_exists(self):
        """確保CSV文件存在並有正確的標題"""
        try:
            if not os.path.exists("data"):
                os.makedirs("data")

            if not os.path.exists(self.trades_csv_path):
                # 創建CSV文件並寫入標題
                import csv
                with open(self.trades_csv_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        'trade_id', 'symbol', 'timeframe', 'direction',
                        'entry_price', 'stop_loss_price', 'take_profit_price',
                        'entry_time', 'exit_time', 'exit_price', 'exit_type',
                        'pnl_pct', 'status'
                    ])
                logger.info("✅ 創建交易記錄CSV文件")
        except Exception as e:
            logger.error(f"創建CSV文件失敗: {e}")

    def is_duplicate_signal(self, signal_key: str) -> bool:
        """檢查是否為重複信號"""
        try:
            import csv
            with open(self.trades_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['status'] == 'ACTIVE':
                        existing_key = f"{row['symbol']}_{row['timeframe']}_{datetime.fromisoformat(row['entry_time']).strftime('%Y%m%d_%H')}"
                        if existing_key == signal_key:
                            return True
            return False
        except Exception as e:
            logger.error(f"檢查重複信號失敗: {e}")
            return False

    def save_trade_to_csv(self, trade_data: dict):
        """保存交易記錄到CSV"""
        try:
            import csv
            with open(self.trades_csv_path, 'a', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'trade_id', 'symbol', 'timeframe', 'direction',
                    'entry_price', 'stop_loss_price', 'take_profit_price',
                    'entry_time', 'exit_time', 'exit_price', 'exit_type',
                    'pnl_pct', 'status'
                ])
                writer.writerow(trade_data)
            logger.info(f"✅ 交易記錄已保存: {trade_data['trade_id']}")
        except Exception as e:
            logger.error(f"保存交易記錄失敗: {e}")

    def get_active_trades_from_csv(self) -> list:
        """從CSV獲取活躍交易"""
        try:
            import csv
            active_trades = []
            with open(self.trades_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['status'] == 'ACTIVE':
                        active_trades.append(row)
            return active_trades
        except Exception as e:
            logger.error(f"讀取活躍交易失敗: {e}")
            return []

    def update_trade_in_csv(self, trade_id: str, update_data: dict):
        """更新CSV中的交易記錄"""
        try:
            import csv
            import tempfile

            # 讀取所有記錄
            rows = []
            with open(self.trades_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['trade_id'] == trade_id:
                        row.update(update_data)
                    rows.append(row)

            # 寫回文件
            with open(self.trades_csv_path, 'w', newline='', encoding='utf-8') as f:
                if rows:
                    writer = csv.DictWriter(f, fieldnames=rows[0].keys())
                    writer.writeheader()
                    writer.writerows(rows)

            logger.info(f"✅ 交易記錄已更新: {trade_id}")
        except Exception as e:
            logger.error(f"更新交易記錄失敗: {e}")

        # 檢查Telegram配置
        if self.telegram_bot_token == 'YOUR_BOT_TOKEN' or self.telegram_chat_id == 'YOUR_CHAT_ID':
            logger.warning("⚠️ Telegram配置未設置，信號將無法發送")
        else:
            logger.info("✅ Telegram配置已加載")
        
    def load_active_strategies(self):
        """加載活躍策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.active_strategies = config.get('active_strategies', {})
                logger.info(f"✅ 加載了 {len(self.active_strategies)} 個活躍策略")
        except FileNotFoundError:
            logger.warning("⚠️ 未找到策略配置文件，使用默認策略")
            self.active_strategies = {
                "DOTUSDT_4H": {
                    "symbol": "DOTUSDT", "timeframe": "4H", "bb_window": 22, "bb_std": 1.9, 
                    "risk_reward_ratio": 3.0, "strategy_score": 82.2, "win_rate": 80.0
                },
                "SEIUSDT_1H": {
                    "symbol": "SEIUSDT", "timeframe": "1H", "bb_window": 18, "bb_std": 1.7, 
                    "risk_reward_ratio": 3.0, "strategy_score": 80.3, "win_rate": 80.0
                },
                "BOMEUSDT_1H": {
                    "symbol": "BOMEUSDT", "timeframe": "1H", "bb_window": 30, "bb_std": 1.5, 
                    "risk_reward_ratio": 3.0, "strategy_score": 78.1, "win_rate": 75.0
                }
            }
    
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算RSI"""
        close = data['Close']
        delta = close.diff()
        
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int, std_dev: float) -> pd.DataFrame:
        """計算布林帶"""
        data = data.copy()
        
        data['BB_Middle'] = data['Close'].rolling(window=window).mean()
        rolling_std = data['Close'].rolling(window=window).std()
        data['BB_Upper'] = data['BB_Middle'] + (rolling_std * std_dev)
        data['BB_Lower'] = data['BB_Middle'] - (rolling_std * std_dev)
        
        return data
    
    def calculate_ti_confidence_intervals(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算Taker Intensity的滾動70%信賴區間"""
        data = data.copy()
        
        data['TI_Lower_70'] = np.nan
        data['TI_Upper_70'] = np.nan
        
        for i in range(self.ti_lookback, len(data)):
            ti_window = data['taker_intensity'].iloc[i-self.ti_lookback:i]
            
            if len(ti_window.dropna()) >= self.ti_lookback * 0.8:
                lower_percentile = (1 - self.confidence_level) / 2 * 100  # 15%
                upper_percentile = (1 + self.confidence_level) / 2 * 100  # 85%
                
                data.iloc[i, data.columns.get_loc('TI_Lower_70')] = np.percentile(ti_window.dropna(), lower_percentile)
                data.iloc[i, data.columns.get_loc('TI_Upper_70')] = np.percentile(ti_window.dropna(), upper_percentile)
        
        return data
    
    async def get_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取數據"""
        try:
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                return None
                
            # 檢查RSI+Bollinger+TI策略必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                logger.warning(f"⚠️ {symbol} {timeframe} 缺少必要列: {missing_columns}")
                return None

            # 清理數據
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            return data_clean
            
        except Exception as e:
            logger.error(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None
    
    def check_signal_conditions(self, data: pd.DataFrame, bb_window: int, bb_std: float) -> dict:
        """檢查信號條件"""
        try:
            logger.info("🔧 開始計算技術指標...")

            # 計算所有指標
            logger.info(f"📊 計算Bollinger Bands (窗口:{bb_window}, 標準差:{bb_std})")
            data = self.calculate_bollinger_bands(data, bb_window, bb_std)

            logger.info(f"📊 計算RSI (週期:{self.rsi_period})")
            data['RSI'] = self.calculate_rsi(data, self.rsi_period)

            logger.info(f"📊 計算ATR (週期:{self.atr_period})")
            data['atr'] = self.calculate_atr(data, self.atr_period)

            logger.info(f"📊 計算TI信賴區間 (回看:{self.ti_lookback}, 信賴度:{self.confidence_level})")
            data = self.calculate_ti_confidence_intervals(data)
            
            # 獲取最新數據
            latest_idx = len(data) - 1
            current_idx = latest_idx - 1

            min_required_data = max(bb_window, self.ti_lookback, self.atr_period, self.rsi_period) + 5
            if current_idx < min_required_data:
                logger.info(f"📊 數據不足，需要至少{min_required_data}條記錄，當前{len(data)}條")
                return None

            price = data['Close'].iloc[latest_idx]
            prev_price = data['Close'].iloc[current_idx]
            ti = data['taker_intensity'].iloc[latest_idx]
            atr = data['atr'].iloc[latest_idx]
            rsi = data['RSI'].iloc[latest_idx]

            bb_upper = data['BB_Upper'].iloc[latest_idx]
            bb_lower = data['BB_Lower'].iloc[latest_idx]
            bb_middle = data['BB_Middle'].iloc[latest_idx]

            ti_upper_70 = data['TI_Upper_70'].iloc[latest_idx]
            ti_lower_70 = data['TI_Lower_70'].iloc[latest_idx]

            # 記錄當前指標值
            logger.info(f"📊 當前指標值:")
            logger.info(f"  - 價格: {price:.6f} (前值: {prev_price:.6f})")
            logger.info(f"  - RSI: {rsi:.2f}")
            logger.info(f"  - BB上軌: {bb_upper:.6f}, 中軌: {bb_middle:.6f}, 下軌: {bb_lower:.6f}")
            logger.info(f"  - TI: {ti:.4f} (上限70%: {ti_upper_70:.4f}, 下限70%: {ti_lower_70:.4f})")
            logger.info(f"  - ATR: {atr:.6f}")

            # 跳過NaN值
            if pd.isna(bb_upper) or pd.isna(ti) or pd.isna(atr) or pd.isna(ti_upper_70) or pd.isna(rsi):
                logger.warning("⚠️ 指標計算結果包含NaN值")
                return None
            
            # 多頭信號條件檢查（經過回測驗證的5選4邏輯）
            logger.info("🔍 檢查多頭信號條件 (5個條件中滿足4個):")

            # 5個獨立條件
            long_condition_1 = rsi >= self.rsi_long_threshold  # RSI>=70
            long_condition_2 = ti > 0  # TI為正值
            long_condition_3 = price > bb_upper  # 價格突破BB上軌
            long_condition_4 = prev_price <= bb_upper  # 前價格未突破BB上軌
            long_condition_5 = ti > ti_upper_70  # TI超過70%上限

            logger.info(f"  1. RSI>=70: {long_condition_1} ({rsi:.2f} >= {self.rsi_long_threshold})")
            logger.info(f"  2. TI為正值: {long_condition_2} ({ti:.4f} > 0)")
            logger.info(f"  3. 價格突破BB上軌: {long_condition_3} ({price:.6f} > {bb_upper:.6f})")
            logger.info(f"  4. 前價格未突破BB上軌: {long_condition_4} ({prev_price:.6f} <= {bb_upper:.6f})")
            logger.info(f"  5. TI超過70%上限: {long_condition_5} ({ti:.4f} > {ti_upper_70:.4f})")

            long_conditions = [long_condition_1, long_condition_2, long_condition_3, long_condition_4, long_condition_5]
            long_satisfied_count = sum(long_conditions)

            # 空頭信號條件檢查（經過回測驗證的5選4邏輯）
            logger.info("🔍 檢查空頭信號條件 (5個條件中滿足4個):")

            # 5個獨立條件
            short_condition_1 = rsi <= self.rsi_short_threshold  # RSI<=30
            short_condition_2 = ti < 0  # TI為負值
            short_condition_3 = price < bb_lower  # 價格跌破BB下軌
            short_condition_4 = prev_price >= bb_lower  # 前價格未跌破BB下軌
            short_condition_5 = ti < ti_lower_70  # TI低於70%下限

            logger.info(f"  1. RSI<=30: {short_condition_1} ({rsi:.2f} <= {self.rsi_short_threshold})")
            logger.info(f"  2. TI為負值: {short_condition_2} ({ti:.4f} < 0)")
            logger.info(f"  3. 價格跌破BB下軌: {short_condition_3} ({price:.6f} < {bb_lower:.6f})")
            logger.info(f"  4. 前價格未跌破BB下軌: {short_condition_4} ({prev_price:.6f} >= {bb_lower:.6f})")
            logger.info(f"  5. TI低於70%下限: {short_condition_5} ({ti:.4f} < {ti_lower_70:.4f})")

            short_conditions = [short_condition_1, short_condition_2, short_condition_3, short_condition_4, short_condition_5]
            short_satisfied_count = sum(short_conditions)

            # 檢查是否滿足5選4邏輯
            logger.info(f"📊 多頭條件滿足數量: {long_satisfied_count}/5")
            logger.info(f"📊 空頭條件滿足數量: {short_satisfied_count}/5")

            if long_satisfied_count >= 4:
                logger.info("🎯 多頭信號條件滿足 (5選4)！")
                return {
                    'direction': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi,
                    'timestamp': data.index[latest_idx]
                }

            elif short_satisfied_count >= 4:
                logger.info("🎯 空頭信號條件滿足 (5選4)！")
                return {
                    'direction': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi,
                    'timestamp': data.index[latest_idx]
                }

            logger.info("📊 無信號條件滿足 (需要5個條件中至少4個)")
            return None
            
        except Exception as e:
            logger.error(f"檢查信號條件失敗: {e}")
            return None
    
    async def send_telegram_message(self, message: str):
        """發送Telegram消息"""
        try:
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info("Telegram消息發送成功")
                    else:
                        logger.error(f"Telegram消息發送失敗: {response.status}")
                        
        except Exception as e:
            logger.error(f"發送Telegram消息失敗: {e}")
    
    async def generate_and_send_signal(self, strategy_key: str, strategy: dict):
        """生成並發送信號"""
        try:
            symbol = strategy['symbol']
            timeframe = strategy['timeframe']
            bb_window = strategy['bb_window']
            bb_std = strategy['bb_std']
            risk_reward_ratio = strategy['risk_reward_ratio']
            
            # 獲取最新數據
            data = await self.get_data(symbol, timeframe)
            if data is None:
                logger.warning(f"⚠️ {symbol} {timeframe} 數據獲取失敗")
                return

            logger.info(f"📊 {symbol} {timeframe} 數據獲取成功，開始檢查信號條件")

            # 檢查信號條件
            signal = self.check_signal_conditions(data, bb_window, bb_std)

            if signal is None:
                logger.info(f"📊 {symbol} {timeframe} 無信號條件滿足")
                return

            logger.info(f"🎯 {symbol} {timeframe} 發現 {signal['direction']} 信號！")
            
            # 檢查是否為重複信號（避免同一小時內重複發送）
            current_hour = datetime.now().strftime('%Y%m%d_%H')
            signal_key = f"{symbol}_{timeframe}_{current_hour}"

            # 檢查CSV中是否已有相同信號
            if self.is_duplicate_signal(signal_key):
                return
            
            # 計算止盈止損
            entry_price = signal['price']
            atr = signal['atr']
            direction = signal['direction']
            
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 發送信號通知
            direction_emoji = "🟢" if direction == 'LONG' else "🔴"
            
            message = f"""
{direction_emoji} <b>多空策略信號</b>

💰 <b>幣種:</b> {symbol}
⏰ <b>時框:</b> {timeframe}
📈 <b>方向:</b> {direction}
💵 <b>入場價:</b> ${entry_price:.6f}

🎯 <b>止盈價:</b> ${take_profit_price:.6f}
🛑 <b>止損價:</b> ${stop_loss_price:.6f}
"""
            
            await self.send_telegram_message(message)

            # 記錄交易到CSV
            trade_id = f"{symbol}_{timeframe}_{signal['timestamp'].strftime('%Y%m%d_%H%M%S')}"
            self.save_trade_to_csv({
                'trade_id': trade_id,
                'symbol': symbol,
                'timeframe': timeframe,
                'direction': direction,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'entry_time': signal['timestamp'],
                'exit_time': '',
                'exit_price': '',
                'exit_type': '',
                'pnl_pct': '',
                'status': 'ACTIVE'
            })

            logger.info(f"✅ 信號已發送並記錄: {symbol} {timeframe} {direction}")
            
        except Exception as e:
            logger.error(f"生成信號失敗 {strategy_key}: {e}")

    async def run_signal_monitoring(self):
        """運行信號監控"""
        logger.info("🚀 多空策略信號系統啟動")
        logger.info(f"📊 監控 {len(self.active_strategies)} 個策略")

        while True:
            try:
                current_time = datetime.now()
                logger.info(f"⏰ 開始掃描信號 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 並行處理所有策略
                tasks = []
                for strategy_key, strategy in self.active_strategies.items():
                    task = self.generate_and_send_signal(strategy_key, strategy)
                    tasks.append(task)

                await asyncio.gather(*tasks, return_exceptions=True)

                logger.info(f"✅ 信號掃描完成，等待下一輪...")

                # 等待5分鐘後繼續掃描（持續監控）
                wait_seconds = 300  # 5分鐘
                logger.info(f"⏳ 等待 {wait_seconds} 秒後繼續掃描")
                await asyncio.sleep(wait_seconds)

            except Exception as e:
                logger.error(f"信號監控循環錯誤: {e}")
                await asyncio.sleep(300)  # 錯誤時等待5分鐘

    async def monitor_active_trades(self):
        """獨立監控CSV中的活躍交易"""
        while True:
            try:
                # 從CSV獲取活躍交易
                active_trades = self.get_active_trades_from_csv()

                if not active_trades:
                    await asyncio.sleep(60)  # 沒有活躍交易時等待1分鐘
                    continue

                logger.info(f"🔍 監控 {len(active_trades)} 個活躍交易")

                # 檢查每個活躍交易
                for trade in active_trades:
                    try:
                        # 獲取當前價格
                        symbol = trade['symbol']
                        current_price = await self.get_current_price(symbol)

                        if current_price is None:
                            continue

                        # 檢查止盈止損條件
                        direction = trade['direction']
                        entry_price = float(trade['entry_price'])
                        stop_loss_price = float(trade['stop_loss_price'])
                        take_profit_price = float(trade['take_profit_price'])

                        hit_stop_loss = False
                        hit_take_profit = False

                        if direction == 'LONG':
                            hit_stop_loss = current_price <= stop_loss_price
                            hit_take_profit = current_price >= take_profit_price
                        else:  # SHORT
                            hit_stop_loss = current_price >= stop_loss_price
                            hit_take_profit = current_price <= take_profit_price

                        if hit_stop_loss or hit_take_profit:
                            # 計算盈虧
                            if direction == 'LONG':
                                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                            else:
                                pnl_pct = ((entry_price - current_price) / entry_price) * 100

                            # 關閉交易
                            await self.close_trade_csv(trade, current_price,
                                                     'TAKE_PROFIT' if hit_take_profit else 'STOP_LOSS',
                                                     pnl_pct)

                    except Exception as e:
                        logger.error(f"監控交易 {trade['trade_id']} 失敗: {e}")

                # 等待30秒後再次檢查
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"交易監控循環錯誤: {e}")
                await asyncio.sleep(60)

    async def get_current_price(self, symbol: str) -> float:
        """獲取當前價格"""
        try:
            if not self.data_fetcher.session:
                self.data_fetcher.session = aiohttp.ClientSession()

            url = f"https://api.bybit.com/v5/market/tickers"
            params = {
                'category': 'linear',
                'symbol': symbol
            }

            async with self.data_fetcher.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data['retCode'] == 0 and data['result']['list']:
                        return float(data['result']['list'][0]['lastPrice'])

            return None

        except Exception as e:
            logger.error(f"獲取 {symbol} 當前價格失敗: {e}")
            return None

    async def close_trade_csv(self, trade: dict, current_price: float, exit_type: str, pnl_pct: float):
        """關閉CSV中的交易並發送通知"""
        try:
            trade_id = trade['trade_id']

            # 更新CSV中的交易狀態
            update_data = {
                'exit_time': datetime.now().isoformat(),
                'exit_price': current_price,
                'exit_type': exit_type,
                'pnl_pct': round(pnl_pct, 2),
                'status': 'CLOSED'
            }
            self.update_trade_in_csv(trade_id, update_data)

            # 發送結算通知
            exit_emoji = "🎯" if exit_type == 'TAKE_PROFIT' else "🛑"
            pnl_emoji = "💰" if pnl_pct > 0 else "💸"
            direction_emoji = "🟢" if trade['direction'] == 'LONG' else "🔴"

            entry_time = datetime.fromisoformat(trade['entry_time'])
            holding_time = self._calculate_holding_time(entry_time)

            message = f"""
{exit_emoji} <b>交易結算</b>

{direction_emoji} <b>幣種:</b> {trade['symbol']}
⏰ <b>時框:</b> {trade['timeframe']}
📈 <b>方向:</b> {trade['direction']}

💵 <b>入場價:</b> ${float(trade['entry_price']):.6f}
💰 <b>出場價:</b> ${current_price:.6f}
📊 <b>結算類型:</b> {'止盈' if exit_type == 'TAKE_PROFIT' else '止損'}

{pnl_emoji} <b>盈虧:</b> {pnl_pct:+.2f}%

⏰ <b>持倉時間:</b> {holding_time}
"""

            await self.send_telegram_message(message)
            logger.info(f"✅ 交易結算: {trade['symbol']} {trade['direction']} {pnl_pct:+.2f}%")

        except Exception as e:
            logger.error(f"關閉交易失敗: {e}")

    def _calculate_holding_time(self, entry_time: datetime) -> str:
        """計算持倉時間"""
        try:
            duration = datetime.now() - entry_time
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            return f"{hours}小時{minutes}分鐘"
        except:
            return "未知"

    async def daily_settlement_report(self):
        """每日結算報告 - 從CSV讀取數據"""
        while True:
            try:
                now = datetime.now()
                # 等待到每日12:00
                target_time = now.replace(hour=12, minute=0, second=0, microsecond=0)
                if target_time <= now:
                    target_time += timedelta(days=1)

                wait_seconds = (target_time - now).total_seconds()
                await asyncio.sleep(wait_seconds)

                # 從CSV統計交易數據
                stats = self.get_trading_statistics()

                report = f"""
📊 <b>每日結算報告</b>
📅 日期: {datetime.now().strftime('%Y-%m-%d')}

💼 <b>活躍交易:</b> {stats['active_count']}個
📈 <b>今日完成:</b> {stats['today_closed']}個
🎯 <b>監控策略:</b> {len(self.active_strategies)}個

💰 <b>今日盈虧:</b> {stats['today_pnl']:+.2f}%
🏆 <b>今日勝率:</b> {stats['today_win_rate']:.1f}%

📊 <b>歷史統計:</b>
📈 <b>總交易:</b> {stats['total_trades']}個
💰 <b>總盈虧:</b> {stats['total_pnl']:+.2f}%
🏆 <b>總勝率:</b> {stats['total_win_rate']:.1f}%

⏰ 下次報告: 明日12:00
"""

                await self.send_telegram_message(report)
                logger.info("📊 每日結算報告已發送")

            except Exception as e:
                logger.error(f"每日結算報告錯誤: {e}")
                await asyncio.sleep(3600)  # 錯誤時等待1小時

    def get_trading_statistics(self) -> dict:
        """從CSV獲取交易統計數據"""
        try:
            import csv
            today = datetime.now().date()

            active_trades = []
            today_trades = []
            all_closed_trades = []

            with open(self.trades_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['status'] == 'ACTIVE':
                        active_trades.append(row)
                    elif row['status'] == 'CLOSED':
                        all_closed_trades.append(row)
                        if row['exit_time']:
                            exit_date = datetime.fromisoformat(row['exit_time']).date()
                            if exit_date == today:
                                today_trades.append(row)

            # 計算統計數據
            today_pnl = sum(float(t['pnl_pct']) for t in today_trades if t['pnl_pct'])
            today_wins = len([t for t in today_trades if float(t['pnl_pct']) > 0])
            today_win_rate = (today_wins / len(today_trades) * 100) if today_trades else 0

            total_pnl = sum(float(t['pnl_pct']) for t in all_closed_trades if t['pnl_pct'])
            total_wins = len([t for t in all_closed_trades if float(t['pnl_pct']) > 0])
            total_win_rate = (total_wins / len(all_closed_trades) * 100) if all_closed_trades else 0

            return {
                'active_count': len(active_trades),
                'today_closed': len(today_trades),
                'today_pnl': today_pnl,
                'today_win_rate': today_win_rate,
                'total_trades': len(all_closed_trades),
                'total_pnl': total_pnl,
                'total_win_rate': total_win_rate
            }

        except Exception as e:
            logger.error(f"獲取交易統計失敗: {e}")
            return {
                'active_count': 0, 'today_closed': 0, 'today_pnl': 0, 'today_win_rate': 0,
                'total_trades': 0, 'total_pnl': 0, 'total_win_rate': 0
            }

async def main():
    """主函數"""
    system = MainSignalSystem()

    # 創建並行任務
    tasks = [
        system.run_signal_monitoring(),      # 信號監控
        system.monitor_active_trades(),      # 交易監控 (新增)
        system.daily_settlement_report()     # 每日報告
    ]

    # 運行所有任務
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 系統已停止")
    except Exception as e:
        logger.error(f"系統錯誤: {e}")

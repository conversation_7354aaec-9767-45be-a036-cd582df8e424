#!/usr/bin/env python3
"""
聖杯級交易信號系統 - 主程序
24/7雲端運行，實時生成PEPE、XRP、SOL的1小時交易信號

作者: 專業量化策略工程師
版本: 1.0.0
日期: 2024-07-09
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator
from src.telegram_bot import TelegramBot
from src.portfolio_manager import PortfolioManager
from src.config_manager import ConfigManager
from src.logger_setup import setup_logger
from src.health_check import start_health_check_server

class QuantTradingSystem:
    """聖杯級交易信號系統主類"""
    
    def __init__(self):
        self.logger = setup_logger("QuantSystem")
        self.config = ConfigManager()
        self.running = False
        
        # 初始化各個模組
        self.data_fetcher = DataFetcher(self.config)
        self.strategy_engine = StrategyEngine(self.config)
        self.signal_generator = SignalGenerator(self.config)
        self.telegram_bot = TelegramBot(self.config)
        self.portfolio_manager = PortfolioManager(self.config)
        
        # 監控的策略
        self.strategies = [
            {"coin": "PEPE", "symbol": "1000PEPEUSDT", "timeframe": "1H"},
            {"coin": "XRP", "symbol": "XRPUSDT", "timeframe": "1H"},
            {"coin": "SOL", "symbol": "SOLUSDT", "timeframe": "1H"}
        ]
        
        # Railway環境檢查
        import os
        if os.getenv('RAILWAY_ENVIRONMENT'):
            self.logger.info("🚂 檢測到Railway環境")
            self.logger.info(f"🔧 Railway服務: {os.getenv('RAILWAY_SERVICE_NAME', 'Unknown')}")
            self.logger.info(f"🌐 Railway環境: {os.getenv('RAILWAY_ENVIRONMENT', 'Unknown')}")

        self.logger.info("🚀 聖杯級交易信號系統初始化完成")
    
    async def startup(self):
        """系統啟動"""
        # 分別啟動各個組件，確保單個組件失敗不會導致整個系統退出
        self.logger.info("🔧 正在啟動系統組件...")

        # 1. 初始化DataFetcher會話
        try:
            await self.data_fetcher.__aenter__()
            self.logger.info("✅ DataFetcher會話啟動成功")
        except Exception as e:
            self.logger.error(f"❌ DataFetcher啟動失敗: {e}")

        # 2. 啟動健康檢查服務器
        try:
            await start_health_check_server()
            self.logger.info("✅ 健康檢查服務器啟動成功")
        except Exception as e:
            self.logger.error(f"❌ 健康檢查服務器啟動失敗: {e}")
            self.logger.warning("⚠️ 健康檢查服務器失敗，但系統將繼續運行")

        # 3. 啟動Telegram Bot
        try:
            await self.telegram_bot.start()
            self.logger.info("✅ Telegram Bot啟動成功")
        except Exception as e:
            self.logger.error(f"❌ Telegram Bot啟動失敗: {e}")
            self.logger.warning("⚠️ Telegram Bot失敗，但系統將繼續運行")

        # 4. 發送啟動通知
        try:
            startup_message = """
🎉 聖杯級交易信號系統啟動成功！

📊 監控策略:
• PEPE-1H (夏普: 7.70)
• XRP-1H (夏普: 7.04)
• SOL-1H (夏普: 6.25)

⚡ 系統狀態: 運行中
🕐 啟動時間: {time}
🔄 更新頻率: 每小時

━━━━━━━━━━━━━━━━━━━━
系統將24/7為您監控市場！
            """.format(time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            await self.telegram_bot.send_message(startup_message)
            self.logger.info("✅ 啟動通知發送成功")
        except Exception as e:
            self.logger.error(f"❌ 啟動通知發送失敗: {e}")

        self.logger.info("✅ 系統啟動完成，開始監控市場")


    
    async def run_strategy_cycle(self):
        """運行一個策略週期"""
        try:
            self.logger.info("🔄 開始新的策略週期")
            
            for strategy in self.strategies:
                coin = strategy["coin"]
                symbol = strategy["symbol"]
                timeframe = strategy["timeframe"]
                
                self.logger.info(f"📊 處理 {coin}-{timeframe} 策略")
                
                try:
                    # 1. 獲取最新數據
                    data = await self.data_fetcher.get_latest_data(symbol, timeframe)
                    
                    if data is None:
                        self.logger.warning(f"⚠️ {coin} 數據獲取失敗，跳過此週期")
                        continue
                    
                    # 2. 計算策略指標
                    indicators = self.strategy_engine.calculate_indicators(data, timeframe)
                    
                    # 3. 生成交易信號
                    signal = self.signal_generator.generate_signal(indicators, coin, timeframe)

                    # 4. 處理信號
                    if signal:
                        self.logger.info(f"🚨 {coin} 收到信號，開始處理: {signal}")
                        await self.process_signal(signal, coin, symbol, data)
                        self.logger.info(f"✅ {coin} 信號處理完成")
                    else:
                        self.logger.info(f"😴 {coin} 本週期無信號")

                    # 5. 更新投資組合狀態
                    self.portfolio_manager.update_position(coin, data, signal)
                    
                except Exception as e:
                    self.logger.error(f"❌ {coin}-{timeframe} 策略處理失敗: {e}")
                    continue
            
            # 6. 生成週期報告
            await self.generate_cycle_report()
            
        except Exception as e:
            self.logger.error(f"❌ 策略週期運行失敗: {e}")
    
    async def process_signal(self, signal, coin, symbol, data):
        """處理交易信號"""
        try:
            current_price = data['Close'].iloc[-1]
            timestamp = data.index[-1]
            
            # 記錄信號到CSV
            self.portfolio_manager.record_signal(signal, coin, current_price, timestamp)
            
            # 發送Telegram通知
            await self.send_signal_notification(signal, coin, current_price, timestamp)
            
            # 如果是平倉信號，計算盈虧
            if signal['action'] in ['CLOSE_LONG', 'CLOSE_SHORT']:
                pnl_report = self.portfolio_manager.calculate_pnl(coin, current_price)
                if pnl_report:
                    await self.send_pnl_notification(pnl_report, coin)
            
            self.logger.info(f"✅ {coin} 信號處理完成: {signal['action']}")
            
        except Exception as e:
            self.logger.error(f"❌ 信號處理失敗: {e}")
    
    async def send_signal_notification(self, signal, coin, price, timestamp):
        """發送交易信號通知"""
        try:
            action_emoji = {
                'LONG': '📈',
                'SHORT': '📉',
                'CLOSE_LONG': '🔒',
                'CLOSE_SHORT': '🔓',
                'HOLD': '⏸️'
            }
            
            emoji = action_emoji.get(signal['action'], '❓')
            
            message = f"""
{emoji} {coin}-1H 交易信號
━━━━━━━━━━━━━━━━━━━━
📊 信號類型: {signal['action']}
💰 當前價格: ${price:.6f}
📈 籌碼集中度: {signal.get('concentration', 'N/A'):.4f}
⚡ 多方力道: {signal.get('long_intensity', 'N/A'):.1f}%
⚡ 空方力道: {signal.get('short_intensity', 'N/A'):.1f}%
🕐 信號時間: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
            """
            
            await self.telegram_bot.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 信號通知發送失敗: {e}")
    
    async def send_pnl_notification(self, pnl_report, coin):
        """發送盈虧報告通知"""
        try:
            profit_emoji = '💰' if pnl_report['pnl_pct'] > 0 else '💸'
            
            message = f"""
{profit_emoji} {coin}-1H 交易結算
━━━━━━━━━━━━━━━━━━━━
📊 交易類型: {pnl_report['trade_type']}
💵 開倉價格: ${pnl_report['entry_price']:.6f}
💵 平倉價格: ${pnl_report['exit_price']:.6f}
📈 收益率: {pnl_report['pnl_pct']:+.2f}%
💰 盈虧金額: ${pnl_report['pnl_amount']:+.2f}
⏱️ 持倉時間: {pnl_report['holding_hours']:.1f}小時
🕐 結算時間: {pnl_report['exit_time']}
━━━━━━━━━━━━━━━━━━━━
            """
            
            await self.telegram_bot.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 盈虧通知發送失敗: {e}")
    
    async def generate_cycle_report(self):
        """生成週期報告"""
        try:
            # 每天12點發送日報
            current_hour = datetime.now().hour
            if current_hour == 12:
                self.logger.info("🕐 12點整，生成每日交易報告")
                daily_report = self.portfolio_manager.generate_daily_report()
                await self.telegram_bot.send_message(daily_report)
                self.logger.info("✅ 每日報告發送完成")

        except Exception as e:
            self.logger.error(f"❌ 週期報告生成失敗: {e}")
    

    
    async def shutdown(self):
        """系統關閉"""
        try:
            self.running = False
            self.logger.info("🔧 正在關閉系統...")

            # 發送關閉通知
            shutdown_message = f"""
🛑 聖杯級交易信號系統已停止

🕐 停止時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 運行統計: 系統已安全關閉

感謝使用聖杯級交易系統！
            """

            await self.telegram_bot.send_message(shutdown_message)
            await self.telegram_bot.stop()

            # 關閉DataFetcher會話
            await self.data_fetcher.__aexit__(None, None, None)

            self.logger.info("✅ 系統已安全關閉")
            
        except Exception as e:
            self.logger.error(f"❌ 系統關閉錯誤: {e}")

# 全局變量用於優雅關閉
shutdown_event = None

def signal_handler(signum, frame):
    """信號處理器 - 優雅關閉"""
    global shutdown_event
    print(f"\n🛑 收到信號 {signum}，正在安全關閉系統...")
    if shutdown_event:
        # 在事件循環中設置事件
        try:
            loop = asyncio.get_event_loop()
            loop.call_soon_threadsafe(shutdown_event.set)
        except RuntimeError:
            # 如果沒有事件循環，直接退出
            print("⚠️ 無法優雅關閉，強制退出...")
            sys.exit(1)

async def main():
    """主函數"""
    global shutdown_event

    # 創建關閉事件
    shutdown_event = asyncio.Event()

    # 設置信號處理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 創建系統
    system = QuantTradingSystem()

    try:
        # 啟動系統
        await system.startup()

        # 主運行循環
        system.running = True
        while system.running and not shutdown_event.is_set():
            try:
                # 運行策略週期
                await system.run_strategy_cycle()

                # 等待下一個小時，但每分鐘檢查關閉信號
                system.logger.info("😴 等待下一個週期...")

                for minute in range(60):
                    if shutdown_event.is_set():
                        system.logger.info("🛑 收到關閉信號，準備退出...")
                        break

                    await asyncio.sleep(60)  # 等待1分鐘

                    if minute % 10 == 0:  # 每10分鐘記錄一次
                        system.logger.info(f"💓 系統心跳 - 已運行 {minute + 1} 分鐘")

                    if not system.running:
                        break

            except Exception as e:
                system.logger.error(f"❌ 運行週期錯誤: {e}")
                import traceback
                system.logger.error(f"錯誤詳情: {traceback.format_exc()}")
                await asyncio.sleep(60)  # 錯誤時等待1分鐘後重試

    except KeyboardInterrupt:
        system.logger.info("🛑 收到鍵盤中斷信號")
    except Exception as e:
        system.logger.error(f"❌ 系統運行錯誤: {e}")
        import traceback
        system.logger.error(f"系統錯誤詳情: {traceback.format_exc()}")
    finally:
        # 確保系統優雅關閉
        await system.shutdown()

if __name__ == "__main__":
    print("🚀 啟動聖杯級交易信號系統...")
    asyncio.run(main())

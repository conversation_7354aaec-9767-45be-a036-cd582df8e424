#!/usr/bin/env python3
"""
主要多空策略信號系統
替代CCB策略框架的新一代報單系統
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import json
import logging
from typing import Dict, List, Optional
import aiohttp

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('signal_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MainSignalSystem:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 從配置文件加載活躍策略
        self.load_active_strategies()
        
        # RSI濾網參數
        self.rsi_period = 14
        self.rsi_long_threshold = 70
        self.rsi_short_threshold = 30
        
        # Taker Intensity參數
        self.ti_lookback = 24
        self.confidence_level = 0.70
        
        # ATR週期
        self.atr_period = 14
        
        # 活躍交易記錄
        self.active_trades = {}
        
        # Telegram配置
        self.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN')
        self.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID')

        # 檢查Telegram配置
        if self.telegram_bot_token == 'YOUR_BOT_TOKEN' or self.telegram_chat_id == 'YOUR_CHAT_ID':
            logger.warning("⚠️ Telegram配置未設置，信號將無法發送")
        else:
            logger.info("✅ Telegram配置已加載")
        
    def load_active_strategies(self):
        """加載活躍策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.active_strategies = config.get('active_strategies', {})
                logger.info(f"✅ 加載了 {len(self.active_strategies)} 個活躍策略")
        except FileNotFoundError:
            logger.warning("⚠️ 未找到策略配置文件，使用默認策略")
            self.active_strategies = {
                "DOTUSDT_4H": {
                    "symbol": "DOTUSDT", "timeframe": "4H", "bb_window": 22, "bb_std": 1.9, 
                    "risk_reward_ratio": 3.0, "strategy_score": 82.2, "win_rate": 80.0
                },
                "SEIUSDT_1H": {
                    "symbol": "SEIUSDT", "timeframe": "1H", "bb_window": 18, "bb_std": 1.7, 
                    "risk_reward_ratio": 3.0, "strategy_score": 80.3, "win_rate": 80.0
                },
                "BOMEUSDT_1H": {
                    "symbol": "BOMEUSDT", "timeframe": "1H", "bb_window": 30, "bb_std": 1.5, 
                    "risk_reward_ratio": 3.0, "strategy_score": 78.1, "win_rate": 75.0
                }
            }
    
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算RSI"""
        close = data['Close']
        delta = close.diff()
        
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int, std_dev: float) -> pd.DataFrame:
        """計算布林帶"""
        data = data.copy()
        
        data['BB_Middle'] = data['Close'].rolling(window=window).mean()
        rolling_std = data['Close'].rolling(window=window).std()
        data['BB_Upper'] = data['BB_Middle'] + (rolling_std * std_dev)
        data['BB_Lower'] = data['BB_Middle'] - (rolling_std * std_dev)
        
        return data
    
    def calculate_ti_confidence_intervals(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算Taker Intensity的滾動70%信賴區間"""
        data = data.copy()
        
        data['TI_Lower_70'] = np.nan
        data['TI_Upper_70'] = np.nan
        
        for i in range(self.ti_lookback, len(data)):
            ti_window = data['taker_intensity'].iloc[i-self.ti_lookback:i]
            
            if len(ti_window.dropna()) >= self.ti_lookback * 0.8:
                lower_percentile = (1 - self.confidence_level) / 2 * 100  # 15%
                upper_percentile = (1 + self.confidence_level) / 2 * 100  # 85%
                
                data.iloc[i, data.columns.get_loc('TI_Lower_70')] = np.percentile(ti_window.dropna(), lower_percentile)
                data.iloc[i, data.columns.get_loc('TI_Upper_70')] = np.percentile(ti_window.dropna(), upper_percentile)
        
        return data
    
    async def get_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取數據"""
        try:
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                return None
                
            # 檢查RSI+Bollinger+TI策略必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                logger.warning(f"⚠️ {symbol} {timeframe} 缺少必要列: {missing_columns}")
                return None

            # 清理數據
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            return data_clean
            
        except Exception as e:
            logger.error(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None
    
    def check_signal_conditions(self, data: pd.DataFrame, bb_window: int, bb_std: float) -> dict:
        """檢查信號條件"""
        try:
            logger.info("🔧 開始計算技術指標...")

            # 計算所有指標
            logger.info(f"📊 計算Bollinger Bands (窗口:{bb_window}, 標準差:{bb_std})")
            data = self.calculate_bollinger_bands(data, bb_window, bb_std)

            logger.info(f"📊 計算RSI (週期:{self.rsi_period})")
            data['RSI'] = self.calculate_rsi(data, self.rsi_period)

            logger.info(f"📊 計算ATR (週期:{self.atr_period})")
            data['atr'] = self.calculate_atr(data, self.atr_period)

            logger.info(f"📊 計算TI信賴區間 (回看:{self.ti_lookback}, 信賴度:{self.confidence_level})")
            data = self.calculate_ti_confidence_intervals(data)
            
            # 獲取最新數據
            latest_idx = len(data) - 1
            current_idx = latest_idx - 1

            min_required_data = max(bb_window, self.ti_lookback, self.atr_period, self.rsi_period) + 5
            if current_idx < min_required_data:
                logger.info(f"📊 數據不足，需要至少{min_required_data}條記錄，當前{len(data)}條")
                return None

            price = data['Close'].iloc[latest_idx]
            prev_price = data['Close'].iloc[current_idx]
            ti = data['taker_intensity'].iloc[latest_idx]
            atr = data['atr'].iloc[latest_idx]
            rsi = data['RSI'].iloc[latest_idx]

            bb_upper = data['BB_Upper'].iloc[latest_idx]
            bb_lower = data['BB_Lower'].iloc[latest_idx]
            bb_middle = data['BB_Middle'].iloc[latest_idx]

            ti_upper_70 = data['TI_Upper_70'].iloc[latest_idx]
            ti_lower_70 = data['TI_Lower_70'].iloc[latest_idx]

            # 記錄當前指標值
            logger.info(f"📊 當前指標值:")
            logger.info(f"  - 價格: {price:.6f} (前值: {prev_price:.6f})")
            logger.info(f"  - RSI: {rsi:.2f}")
            logger.info(f"  - BB上軌: {bb_upper:.6f}, 中軌: {bb_middle:.6f}, 下軌: {bb_lower:.6f}")
            logger.info(f"  - TI: {ti:.4f} (上限70%: {ti_upper_70:.4f}, 下限70%: {ti_lower_70:.4f})")
            logger.info(f"  - ATR: {atr:.6f}")

            # 跳過NaN值
            if pd.isna(bb_upper) or pd.isna(ti) or pd.isna(atr) or pd.isna(ti_upper_70) or pd.isna(rsi):
                logger.warning("⚠️ 指標計算結果包含NaN值")
                return None
            
            # 多頭信號條件檢查（調整為更實用的條件）
            logger.info("🔍 檢查多頭信號條件:")

            # 主要條件：RSI超買 + TI正值 + 價格動能
            long_condition_1 = rsi >= self.rsi_long_threshold  # RSI>=70
            long_condition_2 = ti > 0  # TI為正值
            long_condition_3 = price > prev_price  # 價格上漲

            # 輔助條件：BB位置或TI強度（滿足其一即可）
            bb_condition = price > bb_middle  # 價格在BB中軌之上
            ti_condition = ti > ti_upper_70  # TI超過70%上限
            long_condition_4 = bb_condition or ti_condition

            logger.info(f"  1. RSI>=70: {long_condition_1} ({rsi:.2f} >= {self.rsi_long_threshold})")
            logger.info(f"  2. TI為正值: {long_condition_2} ({ti:.4f} > 0)")
            logger.info(f"  3. 價格上漲: {long_condition_3} ({price:.6f} > {prev_price:.6f})")
            logger.info(f"  4. BB位置或TI強度: {long_condition_4}")
            logger.info(f"     - 價格>BB中軌: {bb_condition} ({price:.6f} > {bb_middle:.6f})")
            logger.info(f"     - TI>70%上限: {ti_condition} ({ti:.4f} > {ti_upper_70:.4f})")

            long_conditions = [long_condition_1, long_condition_2, long_condition_3, long_condition_4]

            # 空頭信號條件檢查（調整為更實用的條件）
            logger.info("🔍 檢查空頭信號條件:")

            # 主要條件：RSI超賣 + TI負值 + 價格動能
            short_condition_1 = rsi <= self.rsi_short_threshold  # RSI<=30
            short_condition_2 = ti < 0  # TI為負值
            short_condition_3 = price < prev_price  # 價格下跌

            # 輔助條件：BB位置或TI強度（滿足其一即可）
            bb_condition = price < bb_middle  # 價格在BB中軌之下
            ti_condition = ti < ti_lower_70  # TI低於70%下限
            short_condition_4 = bb_condition or ti_condition

            logger.info(f"  1. RSI<=30: {short_condition_1} ({rsi:.2f} <= {self.rsi_short_threshold})")
            logger.info(f"  2. TI為負值: {short_condition_2} ({ti:.4f} < 0)")
            logger.info(f"  3. 價格下跌: {short_condition_3} ({price:.6f} < {prev_price:.6f})")
            logger.info(f"  4. BB位置或TI強度: {short_condition_4}")
            logger.info(f"     - 價格<BB中軌: {bb_condition} ({price:.6f} < {bb_middle:.6f})")
            logger.info(f"     - TI<70%下限: {ti_condition} ({ti:.4f} < {ti_lower_70:.4f})")

            short_conditions = [short_condition_1, short_condition_2, short_condition_3, short_condition_4]

            if all(long_conditions):
                logger.info("🎯 多頭信號條件全部滿足！")
                return {
                    'direction': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi,
                    'timestamp': data.index[latest_idx]
                }

            elif all(short_conditions):
                logger.info("🎯 空頭信號條件全部滿足！")
                return {
                    'direction': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi,
                    'timestamp': data.index[latest_idx]
                }

            logger.info("📊 無信號條件滿足")
            return None
            
        except Exception as e:
            logger.error(f"檢查信號條件失敗: {e}")
            return None
    
    async def send_telegram_message(self, message: str):
        """發送Telegram消息"""
        try:
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info("Telegram消息發送成功")
                    else:
                        logger.error(f"Telegram消息發送失敗: {response.status}")
                        
        except Exception as e:
            logger.error(f"發送Telegram消息失敗: {e}")
    
    async def generate_and_send_signal(self, strategy_key: str, strategy: dict):
        """生成並發送信號"""
        try:
            symbol = strategy['symbol']
            timeframe = strategy['timeframe']
            bb_window = strategy['bb_window']
            bb_std = strategy['bb_std']
            risk_reward_ratio = strategy['risk_reward_ratio']
            
            # 獲取最新數據
            data = await self.get_data(symbol, timeframe)
            if data is None:
                logger.warning(f"⚠️ {symbol} {timeframe} 數據獲取失敗")
                return

            logger.info(f"📊 {symbol} {timeframe} 數據獲取成功，開始檢查信號條件")

            # 檢查信號條件
            signal = self.check_signal_conditions(data, bb_window, bb_std)

            if signal is None:
                logger.info(f"📊 {symbol} {timeframe} 無信號條件滿足")
                return

            logger.info(f"🎯 {symbol} {timeframe} 發現 {signal['direction']} 信號！")
            
            # 檢查是否為重複信號（避免同一小時內重複發送）
            current_hour = datetime.now().strftime('%Y%m%d_%H')
            signal_key = f"{symbol}_{timeframe}_{current_hour}"
            
            if signal_key in [t.get('signal_key') for t in self.active_trades.values()]:
                return
            
            # 計算止盈止損
            entry_price = signal['price']
            atr = signal['atr']
            direction = signal['direction']
            
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 發送信號通知
            direction_emoji = "🟢" if direction == 'LONG' else "🔴"
            
            message = f"""
{direction_emoji} <b>多空策略信號</b>

💰 <b>幣種:</b> {symbol}
⏰ <b>時框:</b> {timeframe}
📈 <b>方向:</b> {direction}
💵 <b>入場價:</b> ${entry_price:.6f}

🎯 <b>止盈價:</b> ${take_profit_price:.6f}
🛑 <b>止損價:</b> ${stop_loss_price:.6f}
"""
            
            await self.send_telegram_message(message)
            
            # 記錄活躍交易
            trade_id = f"{symbol}_{timeframe}_{signal['timestamp'].strftime('%Y%m%d_%H%M%S')}"
            self.active_trades[trade_id] = {
                'trade_id': trade_id,
                'signal_key': signal_key,
                'symbol': symbol,
                'timeframe': timeframe,
                'direction': direction,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'entry_time': signal['timestamp'],
                'status': 'ACTIVE'
            }
            
            logger.info(f"✅ 信號已發送: {symbol} {timeframe} {direction}")
            
        except Exception as e:
            logger.error(f"生成信號失敗 {strategy_key}: {e}")

    async def run_signal_monitoring(self):
        """運行信號監控"""
        logger.info("🚀 多空策略信號系統啟動")
        logger.info(f"📊 監控 {len(self.active_strategies)} 個策略")

        while True:
            try:
                current_time = datetime.now()
                logger.info(f"⏰ 開始掃描信號 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 並行處理所有策略
                tasks = []
                for strategy_key, strategy in self.active_strategies.items():
                    task = self.generate_and_send_signal(strategy_key, strategy)
                    tasks.append(task)

                await asyncio.gather(*tasks, return_exceptions=True)

                logger.info(f"✅ 信號掃描完成，等待下一輪...")

                # 等待到下一個整點
                next_hour = (current_time + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
                wait_seconds = (next_hour - current_time).total_seconds()

                logger.info(f"⏳ 等待 {wait_seconds:.0f} 秒到下一個整點")
                await asyncio.sleep(wait_seconds)

            except Exception as e:
                logger.error(f"信號監控循環錯誤: {e}")
                await asyncio.sleep(300)  # 錯誤時等待5分鐘

    async def daily_settlement_report(self):
        """每日結算報告"""
        while True:
            try:
                now = datetime.now()
                # 等待到每日12:00
                target_time = now.replace(hour=12, minute=0, second=0, microsecond=0)
                if target_time <= now:
                    target_time += timedelta(days=1)

                wait_seconds = (target_time - now).total_seconds()
                await asyncio.sleep(wait_seconds)

                # 生成結算報告
                active_count = len([t for t in self.active_trades.values() if t['status'] == 'ACTIVE'])

                report = f"""
📊 <b>每日結算報告</b>
📅 日期: {datetime.now().strftime('%Y-%m-%d')}

💼 <b>活躍交易:</b> {active_count}個
🎯 <b>監控策略:</b> {len(self.active_strategies)}個

⏰ 下次報告: 明日12:00
"""

                await self.send_telegram_message(report)
                logger.info("📊 每日結算報告已發送")

            except Exception as e:
                logger.error(f"每日結算報告錯誤: {e}")
                await asyncio.sleep(3600)  # 錯誤時等待1小時

async def main():
    """主函數"""
    system = MainSignalSystem()

    # 創建並行任務
    tasks = [
        system.run_signal_monitoring(),
        system.daily_settlement_report()
    ]

    # 運行所有任務
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 系統已停止")
    except Exception as e:
        logger.error(f"系統錯誤: {e}")

"""
測試平衡版籌碼集中帶策略
目標：通過評分制提高交易頻率，達到Sharpe Ratio 1.5+

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入平衡版策略
from ccb_balanced_strategy import (
    ccb_balanced_entry_logic, optimize_balanced_parameters
)
from chip_concentration_band_strategy import (
    chip_concentration_bands, calculate_performance_metrics
)

def load_real_market_data():
    """載入真實市場數據"""
    print("載入真實BTC市場數據...")
    
    import glob
    csv_files = glob.glob("btc_4h_with_concentration_*.csv")
    
    if not csv_files:
        print("錯誤：找不到BTC數據文件")
        return None
    
    latest_file = max(csv_files)
    print(f"載入數據文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    df = df.dropna()
    
    print(f"數據載入完成：{len(df)}條記錄")
    print(f"時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"BTC價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    
    return df

def run_balanced_strategy_test():
    """運行平衡版策略測試"""
    
    print("="*70)
    print("籌碼集中帶突破策略 - 平衡版測試")
    print("採用評分制提高交易頻率，目標Sharpe Ratio ≥ 1.5")
    print("="*70)
    
    # 1. 載入數據
    market_data = load_real_market_data()
    if market_data is None:
        return None
    
    # 2. 數據分割
    split_point = int(len(market_data) * 0.75)
    train_data = market_data.iloc[:split_point].copy()
    test_data = market_data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄")
    print(f"測試數據：{len(test_data)}條記錄")
    
    # 3. 平衡版參數優化
    print("\n階段1：平衡版參數優化")
    print("-" * 50)
    
    best_params, optimization_results = optimize_balanced_parameters(train_data)
    
    if best_params is None:
        print("優化失敗")
        return None
    
    print(f"\n最優參數: {best_params}")
    
    # 顯示所有結果
    if optimization_results:
        sorted_results = sorted(optimization_results, key=lambda x: x['sharpe'], reverse=True)
        print(f"\n所有測試結果 (按夏普比率排序):")
        for i, result in enumerate(sorted_results):
            print(f"{i+1}. 夏普={result['sharpe']:.4f}, 年化收益={result['annual_return']:.2%}, "
                  f"勝率={result['win_rate']:.2%}, 交易次數={result['total_trades']}")
    
    # 4. 樣本外測試
    print("\n階段2：樣本外測試")
    print("-" * 50)
    
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_balanced_entry_logic(
        test_df,
        atr_multiplier=best_params['atr_multiplier'],
        ema_trend_period=best_params['ema_period'],
        min_signal_score=best_params['min_score']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 平衡版策略績效 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 檢查目標達成情況
    try:
        sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
        win_rate = float(performance.get('Win Rate', '0%').replace('%', ''))
        total_trades = int(performance.get('Total Trades', 0))
        
        print(f"\n=== 目標達成檢查 ===")
        if sharpe_ratio >= 1.5:
            print(f"🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
        
        print(f"交易頻率改善：{total_trades}筆交易")
        print(f"勝率改善：{win_rate:.1f}%")
        
        # 與之前版本比較
        print(f"\n=== 與增強版比較 ===")
        print(f"增強版勝率: 2.7% → 平衡版勝率: {win_rate:.1f}%")
        print(f"增強版交易次數: 54 → 平衡版交易次數: {total_trades}")
        
    except Exception as e:
        print(f"無法解析績效指標: {e}")
    
    # 5. 信號質量分析
    print("\n階段3：信號質量分析")
    print("-" * 50)
    
    analyze_signal_quality(test_df)
    
    # 6. 可視化結果
    print("\n階段4：結果可視化")
    print("-" * 50)
    
    plot_balanced_results(test_df, best_params, performance)
    
    return test_df, best_params, performance

def analyze_signal_quality(df):
    """分析平衡版信號質量"""
    
    # 統計信號評分分佈
    if 'Long_Score' in df.columns and 'Short_Score' in df.columns:
        long_scores = df['Long_Score'][df['Long_Score'] > 0]
        short_scores = df['Short_Score'][df['Short_Score'] > 0]
        
        print("多頭信號評分分佈:")
        if len(long_scores) > 0:
            for score in sorted(long_scores.unique()):
                count = (long_scores == score).sum()
                print(f"  評分 {score}: {count}次")
        
        print("空頭信號評分分佈:")
        if len(short_scores) > 0:
            for score in sorted(short_scores.unique()):
                count = (short_scores == score).sum()
                print(f"  評分 {score}: {count}次")
    
    # 分析實際交易信號
    signals = df[df['Signal'] != 0]
    if len(signals) > 0:
        print(f"\n實際交易統計:")
        print(f"總信號數: {len(signals)}")
        print(f"多頭信號: {len(signals[signals['Signal'] == 1])}")
        print(f"空頭信號: {len(signals[signals['Signal'] == -1])}")
        
        # 出場原因分析
        exit_reasons = df['Exit_Reason'].value_counts()
        if len(exit_reasons) > 0:
            print(f"\n出場原因分析:")
            total_exits = exit_reasons.sum()
            for reason, count in exit_reasons.items():
                if reason:
                    percentage = (count / total_exits) * 100
                    print(f"  {reason}: {count}次 ({percentage:.1f}%)")

def plot_balanced_results(df, params, performance):
    """繪製平衡版策略結果"""
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. 價格和信號
    axes[0, 0].plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        axes[0, 0].scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=30, label='Buy')
    if len(sell_signals) > 0:
        axes[0, 0].scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=30, label='Sell')
    
    axes[0, 0].set_title('平衡版策略交易信號')
    axes[0, 0].set_ylabel('Price (USD)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 累積收益
    axes[0, 1].plot(df.index, df['cumulative_strategy'], label='平衡版策略', color='blue', linewidth=2)
    axes[0, 1].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 信號評分分佈
    if 'Long_Score' in df.columns and 'Short_Score' in df.columns:
        long_scores = df['Long_Score'][df['Long_Score'] > 0]
        short_scores = df['Short_Score'][df['Short_Score'] > 0]
        
        if len(long_scores) > 0 or len(short_scores) > 0:
            all_scores = list(long_scores) + list(short_scores)
            axes[0, 2].hist(all_scores, bins=range(1, 11), alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 2].set_title('信號評分分佈')
            axes[0, 2].set_xlabel('評分')
            axes[0, 2].set_ylabel('頻次')
            axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 回撤分析
    strategy_cumulative = df['cumulative_strategy']
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    axes[1, 0].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')
    axes[1, 0].plot(df.index, drawdown, color='red', linewidth=1)
    axes[1, 0].set_title('策略回撤分析')
    axes[1, 0].set_ylabel('回撤幅度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 收益分佈
    returns = df['strategy_return'].dropna()
    if len(returns) > 0:
        axes[1, 1].hist(returns, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[1, 1].axvline(returns.mean(), color='red', linestyle='--', label=f'平均: {returns.mean():.4f}')
        axes[1, 1].set_title('策略收益分佈')
        axes[1, 1].set_xlabel('收益率')
        axes[1, 1].set_ylabel('頻次')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 績效總結
    axes[1, 2].axis('off')
    
    perf_text = "=== 平衡版策略績效 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        if key not in ['sharpe', 'annual_return', 'max_drawdown', 'win_rate', 'total_trades']:
            perf_text += f"{key}: {value}\n"
    
    axes[1, 2].text(0.1, 0.9, perf_text, transform=axes[1, 2].transAxes, 
                   fontsize=9, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("平衡版策略圖表已生成完成！")

if __name__ == "__main__":
    # 執行平衡版策略測試
    results = run_balanced_strategy_test()

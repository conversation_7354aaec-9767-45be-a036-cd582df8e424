"""
籌碼集中帶 + 多空力道策略 - 機構級回測系統
使用真實數據進行完整的量化回測分析

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入策略模組
from ccb_taker_intensity_strategy import (
    download_btc_daily_data, generate_chip_concentration_daily, 
    generate_taker_intensity_daily, ccb_taker_intensity_strategy,
    calculate_performance_metrics
)

class CCBTakerIntensityBacktest:
    """籌碼集中帶 + 多空力道策略回測系統"""
    
    def __init__(self):
        self.data = None
        self.results = {}
        
    def prepare_data(self):
        """準備完整的市場數據"""
        print("="*60)
        print("階段1：數據準備")
        print("="*60)
        
        # 1. 獲取BTC日線數據
        btc_data = download_btc_daily_data()
        
        # 2. 生成籌碼集中度數據
        market_data = generate_chip_concentration_daily(btc_data)
        
        # 3. 生成TAKER INTENSITY數據
        market_data = generate_taker_intensity_daily(market_data)
        
        # 4. 設置時間索引
        market_data.set_index('time', inplace=True)
        market_data = market_data.dropna()
        
        self.data = market_data
        
        print(f"\n數據準備完成：")
        print(f"記錄數：{len(market_data)}")
        print(f"時間範圍：{market_data.index[0]} 至 {market_data.index[-1]}")
        print(f"BTC價格範圍：${market_data['close'].min():.0f} - ${market_data['close'].max():.0f}")
        print(f"籌碼集中度範圍：{market_data['concentration'].min():.3f} - {market_data['concentration'].max():.3f}")
        print(f"多方力道範圍：{market_data['long_intensity'].min():.1f} - {market_data['long_intensity'].max():.1f}")
        print(f"空方力道範圍：{market_data['short_intensity'].min():.1f} - {market_data['short_intensity'].max():.1f}")
        
        return market_data
    
    def optimize_parameters(self):
        """參數優化"""
        print("\n" + "="*60)
        print("階段2：參數優化")
        print("="*60)
        
        if self.data is None:
            print("錯誤：請先準備數據")
            return None
        
        # 使用前75%數據進行參數優化
        split_point = int(len(self.data) * 0.75)
        train_data = self.data.iloc[:split_point].copy()
        
        print(f"訓練數據：{len(train_data)}條記錄")
        
        # 參數組合（基於機構級策略經驗）
        param_combinations = [
            {'ccb_window': 20, 'ccb_std': 2.0, 'intensity_lookback': 30, 'intensity_threshold': 60, 'atr_multiplier': 2.0},
            {'ccb_window': 15, 'ccb_std': 1.8, 'intensity_lookback': 25, 'intensity_threshold': 65, 'atr_multiplier': 1.8},
            {'ccb_window': 25, 'ccb_std': 2.2, 'intensity_lookback': 35, 'intensity_threshold': 55, 'atr_multiplier': 2.2},
            {'ccb_window': 18, 'ccb_std': 2.0, 'intensity_lookback': 28, 'intensity_threshold': 62, 'atr_multiplier': 1.9},
            {'ccb_window': 22, 'ccb_std': 1.9, 'intensity_lookback': 32, 'intensity_threshold': 58, 'atr_multiplier': 2.1},
            {'ccb_window': 16, 'ccb_std': 2.1, 'intensity_lookback': 26, 'intensity_threshold': 67, 'atr_multiplier': 1.7},
            {'ccb_window': 24, 'ccb_std': 1.7, 'intensity_lookback': 34, 'intensity_threshold': 53, 'atr_multiplier': 2.3},
            {'ccb_window': 20, 'ccb_std': 2.3, 'intensity_lookback': 30, 'intensity_threshold': 70, 'atr_multiplier': 1.6}
        ]
        
        best_score = -np.inf
        best_params = None
        optimization_results = []
        
        for i, params in enumerate(param_combinations):
            print(f"\n測試參數組合 {i+1}/{len(param_combinations)}:")
            print(f"  CCB窗口={params['ccb_window']}, 標準差={params['ccb_std']}")
            print(f"  力道回望期={params['intensity_lookback']}, 閾值={params['intensity_threshold']}%")
            print(f"  ATR倍數={params['atr_multiplier']}")
            
            try:
                # 應用策略
                test_df = train_data.copy()
                test_df = ccb_taker_intensity_strategy(
                    test_df,
                    ccb_window=params['ccb_window'],
                    ccb_std=params['ccb_std'],
                    intensity_lookback=params['intensity_lookback'],
                    intensity_threshold=params['intensity_threshold'],
                    atr_multiplier=params['atr_multiplier']
                )
                
                # 計算收益
                test_df['price_change'] = test_df['close'].pct_change()
                test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
                
                # 計算績效指標
                returns = test_df['strategy_return'].dropna()
                
                if len(returns) > 20 and returns.std() > 0:
                    total_return = (1 + returns).prod() - 1
                    annual_return = (1 + total_return) ** (365 / len(returns)) - 1
                    volatility = returns.std() * np.sqrt(365)
                    sharpe = annual_return / volatility if volatility > 0 else 0
                    
                    # 計算最大回撤
                    cumulative = (1 + returns).cumprod()
                    rolling_max = cumulative.expanding().max()
                    drawdown = (cumulative - rolling_max) / rolling_max
                    max_drawdown = drawdown.min()
                    
                    # 計算Calmar比率
                    calmar = abs(annual_return / max_drawdown) if max_drawdown < 0 else 0
                    
                    win_rate = (returns > 0).mean()
                    total_trades = len(returns[returns != 0])
                    
                    # 使用Sharpe比率作為優化目標
                    score = sharpe
                    
                    result = {
                        **params,
                        'sharpe': sharpe,
                        'calmar': calmar,
                        'annual_return': annual_return,
                        'max_drawdown': max_drawdown,
                        'win_rate': win_rate,
                        'total_trades': total_trades,
                        'score': score
                    }
                    
                    optimization_results.append(result)
                    
                    print(f"  結果: 夏普={sharpe:.4f}, Calmar={calmar:.4f}, 年化收益={annual_return:.2%}")
                    print(f"        最大回撤={max_drawdown:.2%}, 勝率={win_rate:.2%}, 交易次數={total_trades}")
                    
                    if score > best_score:
                        best_score = score
                        best_params = result
                
            except Exception as e:
                print(f"  參數組合失敗: {e}")
                continue
        
        if best_params:
            print(f"\n最優參數組合:")
            print(f"夏普比率: {best_params['sharpe']:.4f}")
            print(f"參數: {best_params}")
            
            self.results['best_params'] = best_params
            self.results['optimization_results'] = optimization_results
        
        return best_params
    
    def run_backtest(self, params=None):
        """執行樣本外回測"""
        print("\n" + "="*60)
        print("階段3：樣本外回測")
        print("="*60)
        
        if self.data is None:
            print("錯誤：請先準備數據")
            return None
        
        if params is None:
            params = self.results.get('best_params')
            if params is None:
                print("錯誤：請先執行參數優化")
                return None
        
        # 使用後25%數據進行樣本外測試
        split_point = int(len(self.data) * 0.75)
        test_data = self.data.iloc[split_point:].copy()
        
        print(f"測試數據：{len(test_data)}條記錄")
        print(f"測試期間：{test_data.index[0]} 至 {test_data.index[-1]}")
        
        # 應用最優策略
        test_df = ccb_taker_intensity_strategy(
            test_data,
            ccb_window=params['ccb_window'],
            ccb_std=params['ccb_std'],
            intensity_lookback=params['intensity_lookback'],
            intensity_threshold=params['intensity_threshold'],
            atr_multiplier=params['atr_multiplier']
        )
        
        # 計算收益
        test_df['price_change'] = test_df['close'].pct_change()
        test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
        test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
        test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
        
        # 計算績效
        performance = calculate_performance_metrics(test_df)
        
        print("\n=== 樣本外回測績效 ===")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        # 檢查目標達成
        try:
            sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
            if sharpe_ratio >= 1.5:
                print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
            else:
                print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
        except:
            print("無法解析Sharpe Ratio")
        
        # 分析交易信號
        signals = test_df[test_df['Signal'] != 0]
        if len(signals) > 0:
            print(f"\n=== 交易信號分析 ===")
            print(f"總信號數: {len(signals)}")
            print(f"多頭信號: {len(signals[signals['Signal'] == 1])}")
            print(f"空頭信號: {len(signals[signals['Signal'] == -1])}")
            
            # 出場原因分析
            exit_reasons = test_df['Exit_Reason'].value_counts()
            if len(exit_reasons) > 0:
                print(f"\n出場原因分析:")
                total_exits = exit_reasons.sum()
                for reason, count in exit_reasons.items():
                    if reason:
                        percentage = (count / total_exits) * 100
                        print(f"  {reason}: {count}次 ({percentage:.1f}%)")
        
        self.results['backtest_data'] = test_df
        self.results['performance'] = performance
        
        return test_df, performance
    
    def generate_report(self):
        """生成機構級分析報告"""
        print("\n" + "="*60)
        print("階段4：機構級分析報告")
        print("="*60)
        
        if 'backtest_data' not in self.results:
            print("錯誤：請先執行回測")
            return
        
        df = self.results['backtest_data']
        performance = self.results['performance']
        best_params = self.results['best_params']
        
        # 創建可視化
        self.plot_strategy_results(df, best_params, performance)
        
        # 保存結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"CCB_TakerIntensity_Strategy_Report_{timestamp}.csv"
        df.to_csv(filename)
        print(f"\n策略結果已保存到: {filename}")
        
        return df, performance
    
    def plot_strategy_results(self, df, params, performance):
        """繪製策略結果"""
        
        fig, axes = plt.subplots(3, 2, figsize=(20, 15))
        
        # 1. 價格和籌碼集中帶
        ax1 = axes[0, 0]
        ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
        ax1_twin = ax1.twinx()
        ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
        ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
        
        # 標記交易信號
        buy_signals = df[df['Signal'] == 1]
        sell_signals = df[df['Signal'] == -1]
        
        if len(buy_signals) > 0:
            ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=50, label='Buy')
        if len(sell_signals) > 0:
            ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=50, label='Sell')
        
        ax1.set_title('籌碼集中帶 + 多空力道策略 (日線)')
        ax1.set_ylabel('Price (USD)')
        ax1_twin.set_ylabel('Chip Concentration')
        ax1.legend(loc='upper left')
        ax1_twin.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        # 2. 多空力道指標
        axes[0, 1].plot(df.index, df['long_intensity'], label='多方力道', color='green', alpha=0.7)
        axes[0, 1].plot(df.index, df['short_intensity'], label='空方力道', color='red', alpha=0.7)
        axes[0, 1].axhline(y=params['intensity_threshold'], color='blue', linestyle='--', alpha=0.5, 
                          label=f'閾值 ({params["intensity_threshold"]}%)')
        axes[0, 1].set_title('TAKER INTENSITY - 多空力道')
        axes[0, 1].set_ylabel('力道值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 累積收益
        axes[1, 0].plot(df.index, df['cumulative_strategy'], label='策略', color='blue', linewidth=2)
        axes[1, 0].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
        axes[1, 0].set_title('累積收益比較')
        axes[1, 0].set_ylabel('累積收益')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 力道差值
        axes[1, 1].plot(df.index, df['Intensity_Diff'], label='力道差值', color='purple')
        axes[1, 1].plot(df.index, df['Intensity_Diff_MA'], label='力道差值MA', color='orange')
        axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 1].set_title('多空力道差值')
        axes[1, 1].set_ylabel('差值')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 5. 回撤分析
        strategy_cumulative = df['cumulative_strategy']
        rolling_max = strategy_cumulative.expanding().max()
        drawdown = (strategy_cumulative - rolling_max) / rolling_max
        
        axes[2, 0].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')
        axes[2, 0].plot(df.index, drawdown, color='red', linewidth=1)
        axes[2, 0].set_title('策略回撤分析')
        axes[2, 0].set_ylabel('回撤幅度')
        axes[2, 0].grid(True, alpha=0.3)
        
        # 6. 績效總結
        axes[2, 1].axis('off')
        
        perf_text = "=== 策略績效總結 ===\n\n"
        for key, value in performance.items():
            perf_text += f"{key}: {value}\n"
        
        perf_text += f"\n=== 最優參數 ===\n"
        for key, value in params.items():
            if key not in ['sharpe', 'calmar', 'annual_return', 'max_drawdown', 'win_rate', 'total_trades', 'score']:
                perf_text += f"{key}: {value}\n"
        
        axes[2, 1].text(0.1, 0.9, perf_text, transform=axes[2, 1].transAxes, 
                       fontsize=10, verticalalignment='top', fontfamily='monospace',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        plt.show()
        
        print("策略分析圖表已生成完成！")

def run_complete_backtest():
    """運行完整的機構級回測"""
    
    print("="*80)
    print("籌碼集中帶 + 多空力道策略 - 機構級回測系統")
    print("使用真實BTC日線數據 + TAKER INTENSITY指標")
    print("目標：Sharpe Ratio ≥ 1.5")
    print("="*80)
    
    # 創建回測系統
    backtest = CCBTakerIntensityBacktest()
    
    # 執行完整回測流程
    try:
        # 1. 準備數據
        backtest.prepare_data()
        
        # 2. 參數優化
        best_params = backtest.optimize_parameters()
        
        if best_params is None:
            print("參數優化失敗")
            return None
        
        # 3. 樣本外回測
        test_df, performance = backtest.run_backtest()
        
        # 4. 生成報告
        backtest.generate_report()
        
        return backtest
        
    except Exception as e:
        print(f"回測執行失敗: {e}")
        return None

if __name__ == "__main__":
    # 執行完整的機構級回測
    backtest_system = run_complete_backtest()

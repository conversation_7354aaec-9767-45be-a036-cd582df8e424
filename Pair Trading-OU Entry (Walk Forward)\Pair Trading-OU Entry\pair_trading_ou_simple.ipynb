{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Backtest Report ===\n", "Pair: FIL - SAND\n", "Hurst Exponent of Spread: nan\n", "Performance Metrics:\n", "Total Return                  : 167.8845%\n", "Avg Return per Interval       : 0.0024%\n", "Std Dev                       : 71.9693%\n", "Sharpe Ratio                  : 1.1679\n", "<PERSON><PERSON><PERSON>                 : 1.8239\n", "Calmar Ratio                  : 2.7655\n", "Max Drawdown (Abs)            : -60.7065%\n", "Max Drawdown Period           : 3461 bars (36d 1h 15m)\n", "Trade Count                   : 424\n", "Trading Fees                  : 0.0400%\n", "Fees / PnL Ratio              : 40.4087%\n", "Half-life (bars)              : 2430.1155\n", "Hurst Exponent                : nan\n", "Median Holding Period (bars)  : 3.0000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_12695/3459026252.py:65: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Normal Backtest\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "\n", "# === Core Functions ===\n", "\n", "def load_data(asset1, asset2, path):\n", "    df1 = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df2 = pd.read_csv(f\"{path}/{asset2}/bybit_{asset2.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df1['time'] = pd.to_datetime(df1['time'])\n", "    df2['time'] = pd.to_datetime(df2['time'])\n", "    prices1 = df1[['time', 'close']].rename(columns={'close': asset1})\n", "    prices2 = df2[['time', 'close']].rename(columns={'close': asset2})\n", "    df = pd.merge(prices1, prices2, on='time', how='inner').sort_values('time').reset_index(drop=True)\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "def compute_spread(df, asset1, asset2):\n", "    df['log_' + asset1] = np.log(df[asset1])\n", "    df['log_' + asset2] = np.log(df[asset2])\n", "    johansen = coint_johansen(df[[f'log_{asset1}', f'log_{asset2}']], 0, 1)\n", "    beta = johansen.evec[:, 0]\n", "    hedge_ratio = -beta[1] / beta[0]\n", "    df['spread'] = df[f'log_{asset1}'] - hedge_ratio * df[f'log_{asset2}']\n", "    return df, hedge_ratio\n", "\n", "def fit_ou_model(df):\n", "    df['spread_lag'] = df['spread'].shift(1)\n", "    ou_data = df.dropna(subset=['spread', 'spread_lag'])\n", "    model = sm.OLS(ou_data['spread'], sm.add_constant(ou_data['spread_lag'])).fit()\n", "    phi = model.params[1]\n", "    mu = model.params[0] / (1 - phi)\n", "    sigma = np.std(model.resid)\n", "    return mu, sigma, phi\n", "\n", "def apply_trade_logic(df, mu, sigma):\n", "    df['z_score'] = (df['spread'] - mu) / sigma\n", "    df['position'] = 0\n", "    df.loc[df['z_score'] > upper_entry_threshold, 'position'] = -1\n", "    df.loc[df['z_score'] < lower_entry_threshold, 'position'] = 1\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    exit_signals = ((df['position'] == 1) & (df['z_score'] >= exit_threshold)) | \\\n", "                   ((df['position'] == -1) & (df['z_score'] <= -exit_threshold))\n", "    df.loc[exit_signals, 'position'] = 0\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    return df\n", "\n", "def calculate_returns(df):\n", "    df['spread_return'] = df['spread'].diff()\n", "    df['raw_return'] = df['position'].shift(1) * df['spread_return']\n", "    df['trade'] = df['position'].diff().abs()\n", "    df['fee'] = df['trade'] * 2 * trading_fee_per_leg\n", "    df['strategy_return'] = df['raw_return'] - df['fee']\n", "    df['equity_curve'] = df['strategy_return'].cumsum()\n", "    return df\n", "\n", "def calculate_hurst(spread, max_lag=80, min_valid_points=10):\n", "    lags = range(2, max_lag)\n", "    tau = [np.std(spread[lag:] - spread[:-lag]) for lag in lags if len(spread) - lag > min_valid_points]\n", "    if len(tau) < 2:\n", "        return np.nan\n", "    poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "    return poly[0]\n", "\n", "def calculate_median_holding_period(df):\n", "    df['position_change'] = df['position'].diff()\n", "    trade_starts = df[df['position_change'] != 0].index\n", "    holding_periods = []\n", "    for i in range(0, len(trade_starts) - 1, 2):\n", "        entry = trade_starts[i]\n", "        exit = trade_starts[i + 1] if i + 1 < len(trade_starts) else None\n", "        if exit:\n", "            holding_periods.append(exit - entry)\n", "    return np.median(holding_periods) if holding_periods else np.nan\n", "\n", "def evaluate_performance(df, data_interval_str='1h', phi=None, hurst=None):\n", "    returns = df['strategy_return'].dropna()\n", "    balance_series = df['equity_curve'].dropna()\n", "    fees_paid = df['fee'].sum()\n", "    interval_map = {'15m': 15, '30m': 30, '1h': 60, '3h': 180, '1d': 1440}\n", "    data_interval = interval_map.get(data_interval_str, 60)\n", "    annual_factor = 365 * 24 * 60 / data_interval\n", "    total_return = balance_series.iloc[-1] - balance_series.iloc[0]\n", "    avg_return = returns.mean()\n", "    std_dev_annualised = returns.std() * np.sqrt(annual_factor)\n", "    std_dev = returns.std()\n", "    sharpe = (avg_return / std_dev) * np.sqrt(annual_factor) if std_dev != 0 else 0\n", "    downside_returns = np.minimum(0, returns)\n", "    sortino = (avg_return / downside_returns.std()) * np.sqrt(annual_factor) if downside_returns.std() != 0 else 0\n", "    running_max = np.maximum.accumulate(balance_series)\n", "    drawdown = balance_series - running_max\n", "    max_dd = drawdown.min()\n", "    calmar = total_return / abs(max_dd) if max_dd != 0 else np.nan\n", "    dd_end = drawdown.idxmin()\n", "    dd_start = balance_series[:dd_end].idxmax()\n", "    dd_duration = dd_end - dd_start\n", "    trade_count = int(df['trade'].sum() / 2)\n", "    fee_ratio = fees_paid / total_return if total_return != 0 else np.nan\n", "    dd_days = dd_duration * data_interval / 1440\n", "    dd_hours = (dd_duration * data_interval % 1440) // 60\n", "    dd_minutes = (dd_duration * data_interval) % 60\n", "    dd_duration_str = f\"{int(dd_days)}d {int(dd_hours)}h {int(dd_minutes)}m\"\n", "\n", "    # New metrics\n", "    half_life = -np.log(2) / np.log(phi) if phi < 1 else np.nan\n", "    median_holding = calculate_median_holding_period(df)\n", "\n", "    metrics = {\n", "        'Total Return': total_return,\n", "        'Avg Return per Interval': avg_return,\n", "        'Std Dev': std_dev_annualised,\n", "        'Sharpe Ratio': sharpe,\n", "        'So<PERSON><PERSON>': sortino,\n", "        '<PERSON><PERSON>': calmar,\n", "        'Max Drawdown (Abs)': max_dd,\n", "        'Max Drawdown Period': f\"{dd_duration} bars ({dd_duration_str})\",\n", "        'Trade Count': trade_count,\n", "        'Trading Fees': trading_fee_per_leg,\n", "        'Fees / PnL Ratio': fee_ratio,\n", "        'Half-life (bars)': half_life,\n", "        'Hurst Exponent': hurst,\n", "        'Median Holding Period (bars)': median_holding\n", "    }\n", "    return metrics\n", "\n", "def plot_equity_curve(df, asset1, asset2):\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(df['time'], df['equity_curve'], label='Equity Curve')\n", "    plt.title(f'Pair Trading Strategy Equity Curve ({asset1}-{asset2})')\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Cumulative Return (Spread Units)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# === Example Execution ===\n", "data_interval_str = '15m'  # Can be '15m', '30m', '1h', '3h', '1d'\n", "date_range = '20230101_20241231'\n", "asset1 = 'FIL'\n", "asset2 = 'SAND'\n", "data_path = '/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", "\n", "# === Configurable Parameters ===\n", "trading_fee_per_leg = 0.0004\n", "upper_entry_threshold = 1\n", "lower_entry_threshold = -1\n", "exit_threshold = 0\n", "\n", "# === Pipeline ===\n", "raw_df = load_data(asset1, asset2, data_path)\n", "df, hedge_ratio = compute_spread(raw_df, asset1, asset2)\n", "mu, sigma, phi = fit_ou_model(df)\n", "df = apply_trade_logic(df, mu, sigma)\n", "df = calculate_returns(df)\n", "hurst = calculate_hurst(df['spread'].dropna())\n", "metrics = evaluate_performance(df, data_interval_str, phi, hurst)\n", "\n", "# === Output ===\n", "print(\"=== Backtest Report ===\")\n", "print(f\"Pair: {asset1} - {asset2}\")\n", "print(f\"Hurst Exponent of Spread: {hurst:.4f}\")\n", "print(\"Performance Metrics:\")\n", "for k, v in metrics.items():\n", "    print(f\"{k:<30}: {v * 100:.4f}%\" if k in [\n", "        'Total Return', 'Avg Return per Interval', 'Std Dev', 'Trading Fees','Max Drawdown (Abs)','Fees / PnL Ratio'] else \\\n", "        f\"{k:<30}: {v:.4f}\" if isinstance(v, float) else f\"{k:<30}: {v}\")\n", "\n", "plot_equity_curve(df, asset1, asset2)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Optimized Parameters (Training Set) ===\n", "Upper Entry Threshold : 1.5\n", "Lower Entry Threshold : -1.600000000000001\n", "Exit Threshold         : 0.3\n", "\n", "=== Walk-Forward Test Report (Test Set) ===\n", "Total Return                  : 89.2319%\n", "Avg Return per Interval       : 0.0043%\n", "Std Dev                       : 65.4708%\n", "Sharpe Ratio                  : 2.2758\n", "<PERSON><PERSON><PERSON>                 : 3.5746\n", "Calmar Ratio                  : 3.1790\n", "Max Drawdown (Abs)            : -28.0689%\n", "Max Drawdown Period           : -2071 bars (-21d 10h 15m)\n", "Trade Count                   : 320\n", "Trading Fees                  : 0.0400%\n", "Fees / PnL Ratio              : 57.3786%\n", "Half-life (bars)              : 3051.4322\n", "Hurst Exponent                : nan\n", "Median Holding Period (bars)  : 3.0000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_11329/633320771.py:67: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Sharpe optimisation, walk forward\n", "#Output: Performance Metrics & optimised param\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "from itertools import product\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "\n", "# === Core Functions ===\n", "\n", "def load_data(asset1, asset2, path):\n", "    df1 = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df2 = pd.read_csv(f\"{path}/{asset2}/bybit_{asset2.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df1['time'] = pd.to_datetime(df1['time'])\n", "    df2['time'] = pd.to_datetime(df2['time'])\n", "    prices1 = df1[['time', 'close']].rename(columns={'close': asset1})\n", "    prices2 = df2[['time', 'close']].rename(columns={'close': asset2})\n", "    df = pd.merge(prices1, prices2, on='time', how='inner').sort_values('time').reset_index(drop=True)\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "def compute_spread(df, asset1, asset2):\n", "    df['log_' + asset1] = np.log(df[asset1])\n", "    df['log_' + asset2] = np.log(df[asset2])\n", "    johansen = coint_johansen(df[[f'log_{asset1}', f'log_{asset2}']], 0, 1)\n", "    beta = johansen.evec[:, 0]\n", "    hedge_ratio = -beta[1] / beta[0]\n", "    df['spread'] = df[f'log_{asset1}'] - hedge_ratio * df[f'log_{asset2}']\n", "    return df, hedge_ratio\n", "\n", "def fit_ou_model(df):\n", "    df['spread_lag'] = df['spread'].shift(1)\n", "    ou_data = df.dropna(subset=['spread', 'spread_lag'])\n", "    model = sm.OLS(ou_data['spread'], sm.add_constant(ou_data['spread_lag'])).fit()\n", "    phi = model.params[1]\n", "    mu = model.params[0] / (1 - phi)\n", "    sigma = np.std(model.resid)\n", "    return mu, sigma, phi\n", "\n", "def apply_trade_logic(df, mu, sigma):\n", "    df['z_score'] = (df['spread'] - mu) / sigma\n", "    df['position'] = 0\n", "    df.loc[df['z_score'] > upper_entry_threshold, 'position'] = -1\n", "    df.loc[df['z_score'] < lower_entry_threshold, 'position'] = 1\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    exit_signals = ((df['position'] == 1) & (df['z_score'] >= exit_threshold)) | \\\n", "                   ((df['position'] == -1) & (df['z_score'] <= -exit_threshold))\n", "    df.loc[exit_signals, 'position'] = 0\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    return df\n", "\n", "def calculate_returns(df):\n", "    df['spread_return'] = df['spread'].diff()\n", "    df['raw_return'] = df['position'].shift(1) * df['spread_return']\n", "    df['trade'] = df['position'].diff().abs()\n", "    df['fee'] = df['trade'] * 2 * trading_fee_per_leg\n", "    df['strategy_return'] = df['raw_return'] - df['fee']\n", "    df['equity_curve'] = df['strategy_return'].cumsum()\n", "    return df\n", "\n", "def calculate_hurst(spread, max_lag=80, min_valid_points=10):\n", "    lags = range(2, max_lag)\n", "    tau = [np.std(spread[lag:] - spread[:-lag]) for lag in lags if len(spread) - lag > min_valid_points]\n", "    if len(tau) < 2:\n", "        return np.nan\n", "    poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "    return poly[0]\n", "\n", "def calculate_median_holding_period(df):\n", "    df['position_change'] = df['position'].diff()\n", "    trade_starts = df[df['position_change'] != 0].index\n", "    holding_periods = []\n", "    for i in range(0, len(trade_starts) - 1, 2):\n", "        entry = trade_starts[i]\n", "        exit = trade_starts[i + 1] if i + 1 < len(trade_starts) else None\n", "        if exit:\n", "            holding_periods.append(exit - entry)\n", "    return np.median(holding_periods) if holding_periods else np.nan\n", "\n", "def evaluate_performance(df, data_interval_str='1h', phi=None, hurst=None):\n", "    returns = df['strategy_return'].dropna()\n", "    balance_series = df['equity_curve'].dropna()\n", "    fees_paid = df['fee'].sum()\n", "    interval_map = {'15m': 15, '30m': 30, '1h': 60, '3h': 180, '1d': 1440}\n", "    data_interval = interval_map.get(data_interval_str, 60)\n", "    annual_factor = 365 * 24 * 60 / data_interval\n", "    total_return = balance_series.iloc[-1] - balance_series.iloc[0]\n", "    avg_return = returns.mean()\n", "    std_dev_annualised = returns.std() * np.sqrt(annual_factor)\n", "    std_dev = returns.std()\n", "    sharpe = (avg_return / std_dev) * np.sqrt(annual_factor) if std_dev != 0 else 0\n", "    downside_returns = np.minimum(0, returns)\n", "    sortino = (avg_return / downside_returns.std()) * np.sqrt(annual_factor) if downside_returns.std() != 0 else 0\n", "    running_max = np.maximum.accumulate(balance_series)\n", "    drawdown = balance_series - running_max\n", "    max_dd = drawdown.min()\n", "    calmar = total_return / abs(max_dd) if max_dd != 0 else np.nan\n", "    dd_end = drawdown.idxmin()\n", "    dd_start = balance_series[:dd_end].idxmax()\n", "    dd_duration = dd_end - dd_start\n", "    trade_count = int(df['trade'].sum() / 2)\n", "    fee_ratio = fees_paid / total_return if total_return != 0 else np.nan\n", "    dd_days = dd_duration * data_interval / 1440\n", "    dd_hours = (dd_duration * data_interval % 1440) // 60\n", "    dd_minutes = (dd_duration * data_interval) % 60\n", "    dd_duration_str = f\"{int(dd_days)}d {int(dd_hours)}h {int(dd_minutes)}m\"\n", "    half_life = -np.log(2) / np.log(phi) if phi < 1 else np.nan\n", "    median_holding = calculate_median_holding_period(df)\n", "\n", "    metrics = {\n", "        'Total Return': total_return,\n", "        'Avg Return per Interval': avg_return,\n", "        'Std Dev': std_dev_annualised,\n", "        'Sharpe Ratio': sharpe,\n", "        'So<PERSON><PERSON>': sortino,\n", "        '<PERSON><PERSON>': calmar,\n", "        'Max Drawdown (Abs)': max_dd,\n", "        'Max Drawdown Period': f\"{dd_duration} bars ({dd_duration_str})\",\n", "        'Trade Count': trade_count,\n", "        'Trading Fees': trading_fee_per_leg,\n", "        'Fees / PnL Ratio': fee_ratio,\n", "        'Half-life (bars)': half_life,\n", "        'Hurst Exponent': hurst,\n", "        'Median Holding Period (bars)': median_holding\n", "    }\n", "    return metrics\n", "\n", "def plot_equity_curve(df, asset1, asset2):\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(df['time'], df['equity_curve'], label='Equity Curve')\n", "    plt.title(f'Pair Trading Strategy Equity Curve ({asset1}-{asset2})')\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Cumulative Return (Spread Units)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# === Parameter Optimization Function ===\n", "\n", "def optimize_params(df, param_grid, data_interval_str='15m', train_ratio=0.7):\n", "    train_size = int(len(df) * train_ratio)\n", "    train_df = df.iloc[:train_size].copy()\n", "    best_sharpe = -np.inf\n", "    best_params = None\n", "    best_mu, best_sigma, best_phi = None, None, None\n", "\n", "    for upper, lower, exit_ in param_grid:\n", "        global upper_entry_threshold, lower_entry_threshold, exit_threshold\n", "        upper_entry_threshold, lower_entry_threshold, exit_threshold = upper, lower, exit_\n", "        temp_df = train_df.copy()\n", "        mu, sigma, phi = fit_ou_model(temp_df)\n", "        temp_df = apply_trade_logic(temp_df, mu, sigma)\n", "        temp_df = calculate_returns(temp_df)\n", "        hurst = calculate_hurst(temp_df['spread'].dropna())\n", "        metrics = evaluate_performance(temp_df, data_interval_str, phi, hurst)\n", "        sharpe = metrics['<PERSON> Ratio']\n", "        if sharpe > best_sharpe:\n", "            best_sharpe = sharpe\n", "            best_params = (upper, lower, exit_)\n", "            best_mu, best_sigma, best_phi = mu, sigma, phi\n", "\n", "    return best_params, best_mu, best_sigma, best_phi\n", "\n", "# === Main Config ===\n", "\n", "data_interval_str = '15m'\n", "date_range = '20230101_20241231'\n", "asset1 = 'FIL'\n", "asset2 = 'SAND'\n", "data_path = '/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", "trading_fee_per_leg = 0.0004\n", "\n", "# === Param Grid to Search ===\n", "\n", "param_grid = list(product(\n", "    np.arange(1.5, 3.1, 0.3),    # upper entry\n", "    np.arange(-3.1, -1.5, 0.3),  # lower entry\n", "    np.arange(0.3, 1.1, 0.3)     # exit\n", "))\n", "\n", "# === Full Pipeline ===\n", "\n", "raw_df = load_data(asset1, asset2, data_path)\n", "df, hedge_ratio = compute_spread(raw_df, asset1, asset2)\n", "\n", "# Optimize on 70% train set\n", "best_params, mu, sigma, phi = optimize_params(df, param_grid, data_interval_str)\n", "upper_entry_threshold, lower_entry_threshold, exit_threshold = best_params\n", "\n", "print(\"\\n=== Optimized Parameters (Training Set) ===\")\n", "print(f\"Upper Entry Threshold : {upper_entry_threshold}\")\n", "print(f\"Lower Entry Threshold : {lower_entry_threshold}\")\n", "print(f\"Exit Threshold         : {exit_threshold}\")\n", "\n", "# Apply to 30% test set\n", "test_df = df.iloc[int(len(df) * 0.7):].copy() #0.7 = 70%:30% -> In:Out Sampling\n", "test_df = apply_trade_logic(test_df, mu, sigma)\n", "test_df = calculate_returns(test_df)\n", "hurst = calculate_hurst(test_df['spread'].dropna())\n", "metrics = evaluate_performance(test_df, data_interval_str, phi, hurst)\n", "\n", "print(\"\\n=== Walk-Forward Test Report (Test Set) ===\")\n", "for k, v in metrics.items():\n", "    print(f\"{k:<30}: {v * 100:.4f}%\" if k in [\n", "        'Total Return', 'Avg Return per Interval', 'Std Dev', 'Trading Fees','Max Drawdown (Abs)','Fees / PnL Ratio'] else \\\n", "        f\"{k:<30}: {v:.4f}\" if isinstance(v, float) else f\"{k:<30}: {v}\")\n", "\n", "plot_equity_curve(test_df, asset1, asset2)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
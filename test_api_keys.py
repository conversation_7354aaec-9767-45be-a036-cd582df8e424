#!/usr/bin/env python3
"""
測試Blave API密鑰
"""

import requests

# 使用提供的API密鑰
API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
BASE_URL = "https://api.blave.org/"

def test_different_header_formats():
    """測試不同的請求頭格式"""
    
    # 格式1: 原始格式
    headers1 = {
        "api-key": API_KEY,
        "secret-key": SECRET_KEY
    }
    
    # 格式2: 添加Content-Type
    headers2 = {
        "api-key": API_KEY,
        "secret-key": SECRET_KEY,
        "Content-Type": "application/json"
    }
    
    # 格式3: 使用Authorization
    headers3 = {
        "Authorization": f"Bearer {API_KEY}",
        "secret-key": SECRET_KEY
    }
    
    # 格式4: 使用X-API-Key
    headers4 = {
        "X-API-Key": API_KEY,
        "X-Secret-Key": SECRET_KEY
    }
    
    url = BASE_URL + "taker_intensity/get_symbols"
    
    formats = [
        ("原始格式", headers1),
        ("添加Content-Type", headers2),
        ("使用Authorization", headers3),
        ("使用X-API-Key", headers4)
    ]
    
    for name, headers in formats:
        try:
            print(f"\n🔍 測試 {name}:")
            print(f"   Headers: {headers}")
            
            resp = requests.get(url, headers=headers, timeout=30)
            
            print(f"   Status Code: {resp.status_code}")
            print(f"   Response: {resp.text[:200]}")
            
            if resp.status_code == 200:
                print(f"   ✅ {name} 成功！")
                return headers
            else:
                print(f"   ❌ {name} 失敗")
                
        except Exception as e:
            print(f"   ❌ {name} 異常: {e}")
    
    return None

def test_api_endpoints():
    """測試API端點"""
    
    # 先找到正確的請求頭格式
    correct_headers = test_different_header_formats()
    
    if not correct_headers:
        print("\n❌ 無法找到正確的請求頭格式")
        return
    
    print(f"\n✅ 找到正確的請求頭格式: {correct_headers}")
    
    # 測試不同的端點
    endpoints = [
        "taker_intensity/get_symbols",
        "holder_concentration/get_symbols"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n🔍 測試端點: {endpoint}")
            
            url = BASE_URL + endpoint
            resp = requests.get(url, headers=correct_headers, timeout=30)
            
            print(f"   Status Code: {resp.status_code}")
            print(f"   Response: {resp.text[:500]}")
            
            if resp.status_code == 200:
                data = resp.json()
                if "data" in data:
                    symbols = data["data"]
                    print(f"   ✅ 成功獲取 {len(symbols)} 個交易對")
                    print(f"   前10個: {symbols[:10]}")
                else:
                    print(f"   ❌ 響應格式錯誤")
            else:
                print(f"   ❌ API錯誤")
                
        except Exception as e:
            print(f"   ❌ 異常: {e}")

def main():
    """主函數"""
    print("🚀 開始Blave API密鑰測試...")
    print(f"API Key: {API_KEY[:20]}...")
    print(f"Secret Key: {SECRET_KEY[:20]}...")
    print(f"Base URL: {BASE_URL}")
    
    test_api_endpoints()

if __name__ == "__main__":
    main()

"""
籌碼集中帶突破策略 - 增強版測試
整合趨勢過濾、OBV確認、ADX市場狀態識別和動態ATR止盈止損
目標：達到Sharpe Ratio 1.5以上

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入增強版策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, ccb_entry_logic_enhanced, 
    optimize_ccb_parameters_enhanced, calculate_performance_metrics
)

def load_real_market_data():
    """載入真實市場數據"""
    print("載入真實BTC市場數據...")
    
    # 查找最新的數據文件
    import glob
    csv_files = glob.glob("btc_4h_with_concentration_*.csv")
    
    if not csv_files:
        print("錯誤：找不到BTC數據文件")
        print("請先運行 get_real_btc_data.py 獲取數據")
        return None
    
    # 使用最新的文件
    latest_file = max(csv_files)
    print(f"載入數據文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # 清理數據
    df = df.dropna()
    
    print(f"數據載入完成：{len(df)}條記錄")
    print(f"時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"BTC價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    print(f"籌碼集中度範圍：{df['concentration'].min():.3f} - {df['concentration'].max():.3f}")
    
    return df

def run_enhanced_strategy_test():
    """運行增強版策略測試"""
    
    print("="*80)
    print("籌碼集中帶突破策略 - 增強版測試")
    print("整合趨勢過濾 + OBV確認 + ADX狀態識別 + 動態ATR")
    print("目標：Sharpe Ratio ≥ 1.5")
    print("="*80)
    
    # 1. 載入真實數據
    print("\n階段1：數據載入")
    print("-" * 50)
    market_data = load_real_market_data()
    
    if market_data is None:
        return None
    
    # 2. 數據分割（使用更多訓練數據）
    split_point = int(len(market_data) * 0.75)  # 75%用於訓練
    train_data = market_data.iloc[:split_point].copy()
    test_data = market_data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄 ({train_data.index[0]} 至 {train_data.index[-1]})")
    print(f"測試數據：{len(test_data)}條記錄 ({test_data.index[0]} 至 {test_data.index[-1]})")
    
    # 3. 增強版參數優化
    print("\n階段2：增強版參數優化")
    print("-" * 50)
    print("優化參數包括：")
    print("- CCB窗口期: 10-30")
    print("- 標準差倍數: 1.5-2.5")
    print("- ATR倍數: 1.0-2.0")
    print("- EMA趨勢期間: 30-60")
    print("- ADX閾值: 20-30")
    print("- 優化目標: Sharpe Ratio")
    
    best_params, optimization_results = optimize_ccb_parameters_enhanced(
        train_data,
        lookback_range=(10, 30),
        std_range=(1.5, 2.5),
        atr_range=(1.0, 2.0),
        ema_range=(30, 60),
        adx_threshold_range=(20, 30),
        optimization_metric='sharpe'
    )
    
    print(f"\n最優參數：{best_params}")
    
    # 顯示前5名結果
    if optimization_results:
        sorted_results = sorted(optimization_results, key=lambda x: x['score'], reverse=True)
        print(f"\n前5名參數組合 (按Sharpe Ratio排序):")
        for i, result in enumerate(sorted_results[:5]):
            print(f"{i+1}. 窗口={result['window']}, 標準差={result['std_dev']}, ATR={result['atr_multiplier']}")
            print(f"   EMA={result['ema_period']}, ADX閾值={result['adx_threshold']}")
            print(f"   夏普={result['sharpe']:.4f}, Calmar={result['calmar']:.4f}, 年化收益={result['annual_return']:.2%}")
            print(f"   最大回撤={result['max_drawdown']:.2%}, 勝率={result['win_rate']:.2%}, 交易次數={result['total_trades']}")
            print()
    
    # 4. 樣本外測試
    print("\n階段3：樣本外測試")
    print("-" * 50)
    
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_entry_logic_enhanced(
        test_df,
        atr_multiplier=best_params['atr_multiplier'],
        ema_trend_period=best_params['ema_period'],
        adx_threshold=best_params['adx_threshold']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 增強版策略績效 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 檢查是否達到目標
    try:
        sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
        if sharpe_ratio >= 1.5:
            print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
            print("需要進一步優化...")
    except:
        print("\n無法解析Sharpe Ratio")
    
    # 出場原因分析
    exit_reasons = test_df['Exit_Reason'].value_counts()
    if len(exit_reasons) > 0:
        print(f"\n=== 出場原因分析 ===")
        total_exits = exit_reasons.sum()
        for reason, count in exit_reasons.items():
            if reason:
                percentage = (count / total_exits) * 100
                print(f"{reason}: {count}次 ({percentage:.1f}%)")
    
    # 5. 信號質量分析
    print("\n階段4：信號質量分析")
    print("-" * 50)
    
    analyze_signal_quality(test_df)
    
    # 6. 可視化結果
    print("\n階段5：結果可視化")
    print("-" * 50)
    
    plot_enhanced_results(test_df, best_params, performance)
    
    # 7. 策略改進建議
    print("\n階段6：策略改進建議")
    print("-" * 50)
    
    generate_improvement_suggestions(performance, best_params, test_df)
    
    return test_df, best_params, performance

def analyze_signal_quality(df):
    """分析信號質量"""
    
    # 統計信號強度分佈
    if 'Signal_Strength' in df.columns:
        signal_strength_dist = df['Signal_Strength'].value_counts().sort_index()
        print("信號強度分佈:")
        for strength, count in signal_strength_dist.items():
            if strength > 0:
                print(f"  強度 {strength}: {count}次")
    
    # 分析不同技術指標的有效性
    signals = df[df['Signal'] != 0]
    if len(signals) > 0:
        print(f"\n總信號數: {len(signals)}")
        print(f"多頭信號: {len(signals[signals['Signal'] == 1])}")
        print(f"空頭信號: {len(signals[signals['Signal'] == -1])}")
        
        # 分析趨勢過濾效果
        if 'Trend_Up' in df.columns:
            trend_aligned_long = len(signals[(signals['Signal'] == 1) & (signals['Trend_Up'])])
            trend_aligned_short = len(signals[(signals['Signal'] == -1) & (signals['Trend_Down'])])
            print(f"趨勢對齊的多頭信號: {trend_aligned_long}")
            print(f"趨勢對齊的空頭信號: {trend_aligned_short}")

def plot_enhanced_results(df, params, performance):
    """繪製增強版策略結果"""
    
    fig, axes = plt.subplots(3, 2, figsize=(20, 15))
    
    # 1. 價格和籌碼集中帶
    ax1 = axes[0, 0]
    ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--', alpha=0.8)
    ax1_twin.plot(df.index, df['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-', alpha=0.8)
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--', alpha=0.8)
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=30, label='Buy', alpha=0.8)
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=30, label='Sell', alpha=0.8)
    
    ax1.set_title('增強版籌碼集中帶策略 - 真實BTC數據')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 累積收益
    axes[0, 1].plot(df.index, df['cumulative_strategy'], label='增強策略', color='blue', linewidth=2)
    axes[0, 1].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 趨勢指標
    if 'EMA_Trend' in df.columns:
        axes[1, 0].plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
        axes[1, 0].plot(df.index, df['EMA_Trend'], label=f'EMA({params["ema_period"]})', color='orange', linewidth=1)
        axes[1, 0].set_title('趨勢過濾 - EMA')
        axes[1, 0].set_ylabel('Price (USD)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. ADX指標
    if 'ADX' in df.columns:
        axes[1, 1].plot(df.index, df['ADX'], label='ADX', color='purple')
        axes[1, 1].axhline(y=params['adx_threshold'], color='red', linestyle='--', alpha=0.5, 
                          label=f'閾值 ({params["adx_threshold"]})')
        axes[1, 1].set_title('ADX - 趨勢強度指標')
        axes[1, 1].set_ylabel('ADX')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    # 5. OBV指標
    if 'OBV' in df.columns:
        axes[2, 0].plot(df.index, df['OBV'], label='OBV', color='green')
        axes[2, 0].plot(df.index, df['OBV_MA'], label='OBV MA', color='red', linestyle='--')
        axes[2, 0].set_title('OBV - 成交量確認指標')
        axes[2, 0].set_ylabel('OBV')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)
    
    # 6. 績效總結
    axes[2, 1].axis('off')
    
    perf_text = "=== 增強版策略績效 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        perf_text += f"{key}: {value}\n"
    
    axes[2, 1].text(0.1, 0.9, perf_text, transform=axes[2, 1].transAxes, 
                   fontsize=9, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("增強版策略圖表已生成完成！")

def generate_improvement_suggestions(performance, params, df):
    """生成策略改進建議"""
    
    try:
        sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
        win_rate = float(performance.get('Win Rate', '0%').replace('%', ''))
        max_drawdown = float(performance.get('Max Drawdown', '0%').replace('%', ''))
        total_trades = int(performance.get('Total Trades', 0))
    except:
        print("無法解析績效指標")
        return
    
    print("基於當前結果的改進建議：")
    
    if sharpe_ratio < 1.5:
        print(f"• Sharpe Ratio ({sharpe_ratio:.4f}) 未達目標，建議：")
        if win_rate < 50:
            print("  - 提高信號質量：加入更多確認指標")
            print("  - 調整入場條件：提高信號強度要求")
        if abs(max_drawdown) > 15:
            print("  - 加強風險控制：縮小ATR倍數或加入動態止損")
        if total_trades < 50:
            print("  - 增加交易頻率：放寬部分入場條件")
    
    if win_rate < 45:
        print(f"• 勝率 ({win_rate:.1f}%) 偏低，建議：")
        print("  - 加入更多技術指標確認")
        print("  - 考慮使用機器學習優化信號")
    
    if abs(max_drawdown) > 20:
        print(f"• 最大回撤 ({max_drawdown:.1f}%) 過大，建議：")
        print("  - 實施更嚴格的風險管理")
        print("  - 考慮倉位管理系統")
    
    print(f"\n下一步優化方向：")
    print("1. 嘗試不同的技術指標組合")
    print("2. 實施機器學習參數優化")
    print("3. 加入市場狀態識別機制")
    print("4. 考慮多時間框架分析")

if __name__ == "__main__":
    # 執行增強版策略測試
    results = run_enhanced_strategy_test()

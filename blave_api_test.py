#!/usr/bin/env python3
"""
測試Blave API連接和數據獲取
"""

import requests
import pandas as pd
from datetime import datetime, timedelta

# Blave API配置
API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
BASE_URL = "https://api.blave.org/"

headers = {
    "api-key": API_KEY,
    "secret-key": SECRET_KEY
}

def test_api_connection():
    """測試API連接"""
    print("🔗 測試Blave API連接...")
    
    try:
        # 測試Taker Intensity symbols
        print("📊 測試Taker Intensity symbols...")
        url = BASE_URL + "taker_intensity/get_symbols"
        resp = requests.get(url, headers=headers, timeout=30)
        
        print(f"Status Code: {resp.status_code}")
        print(f"Response Headers: {dict(resp.headers)}")
        print(f"Response: {resp.text[:500]}")
        
        if resp.status_code == 200:
            ti_symbols = resp.json()["data"]
            print(f"✅ Taker Intensity支持的交易對: {len(ti_symbols)}個")
            print(f"   前10個: {ti_symbols[:10]}")
            return ti_symbols
        else:
            print(f"❌ Taker Intensity API錯誤: {resp.status_code}")
            print(f"   錯誤內容: {resp.text}")
            return []
            
    except Exception as e:
        print(f"❌ API測試失敗: {e}")
        return []

def test_data_fetch(symbol: str, period: str):
    """測試數據獲取"""
    print(f"\n📊 測試 {symbol} {period} 數據獲取...")
    
    try:
        # 設定時間範圍
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)  # 測試30天
        
        # 測試Taker Intensity數據
        print(f"📊 獲取 {symbol} Taker Intensity {period} 數據...")
        url = BASE_URL + "taker_intensity/get_alpha"
        params = {
            "symbol": symbol,
            "period": period,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "timeframe": "24h"
        }
        
        print(f"請求URL: {url}")
        print(f"請求參數: {params}")
        print(f"請求頭: {headers}")
        
        resp = requests.get(url, headers=headers, params=params, timeout=60)
        
        print(f"Status Code: {resp.status_code}")
        print(f"Response: {resp.text[:1000]}")
        
        if resp.status_code == 200:
            data = resp.json()["data"]
            
            if "alpha" in data and "timestamp" in data:
                df = pd.DataFrame({
                    "timestamp": pd.to_datetime(data["timestamp"], unit="s"),
                    "taker_intensity": data["alpha"]
                })
                
                print(f"✅ Taker Intensity數據: {len(df)} 條記錄")
                print(f"   時間範圍: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
                print(f"   數據樣本:")
                print(df.head())
                
                return True
            else:
                print(f"❌ Taker Intensity數據格式錯誤")
                return False
        else:
            print(f"❌ Taker Intensity API錯誤: {resp.status_code}")
            print(f"   錯誤內容: {resp.text}")
            return False
            
    except Exception as e:
        print(f"❌ 數據獲取測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 開始Blave API測試...")
    
    # 測試API連接
    symbols = test_api_connection()
    
    if symbols:
        # 測試數據獲取
        test_symbol = symbols[0]  # 使用第一個支持的交易對
        
        print(f"\n🎯 使用 {test_symbol} 進行數據獲取測試...")
        
        # 測試不同時間框架
        for period in ['1d', '4h', '1h']:
            print(f"\n{'='*50}")
            success = test_data_fetch(test_symbol, period)
            if success:
                print(f"✅ {test_symbol} {period} 數據獲取成功")
            else:
                print(f"❌ {test_symbol} {period} 數據獲取失敗")
    else:
        print("❌ 無法獲取支持的交易對，無法進行數據測試")

if __name__ == "__main__":
    main()

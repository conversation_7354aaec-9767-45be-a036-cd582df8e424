#!/usr/bin/env python3
"""
測試Blave API連接
"""

import requests
import asyncio
import aiohttp
from datetime import datetime, timedelta

async def test_blave_api():
    """測試Blave API"""
    
    base_url = "https://api.blave.org/"
    api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
    secret_key = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
    
    headers = {
        "api-key": api_key,
        "secret-key": secret_key
    }
    
    print("🔍 測試Blave API連接...")
    
    # 1. 測試獲取支援的交易對
    try:
        url = f"{base_url}taker_intensity/get_symbols"
        
        print(f"📊 測試獲取支援交易對...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=30) as response:
                print(f"狀態碼: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 支援的交易對: {data}")
                else:
                    text = await response.text()
                    print(f"❌ 錯誤響應: {text}")
                    
    except Exception as e:
        print(f"❌ API測試異常: {e}")
    
    # 2. 測試同步請求
    try:
        print(f"\n📊 測試同步請求...")
        
        response = requests.get(f"{base_url}taker_intensity/get_symbols", headers=headers, timeout=30)
        print(f"同步請求狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 同步請求成功: {data}")
        else:
            print(f"❌ 同步請求失敗: {response.text}")
            
    except Exception as e:
        print(f"❌ 同步請求異常: {e}")
    
    # 3. 測試獲取歷史數據
    try:
        print(f"\n📊 測試獲取BTCUSDT歷史數據...")
        
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=7)  # 只測試7天
        
        url = f"{base_url}taker_intensity/get_alpha"
        params = {
            "symbol": "BTCUSDT",
            "period": "1h",
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "timeframe": "24h"
        }
        
        print(f"參數: {params}")
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        print(f"歷史數據請求狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 歷史數據獲取成功!")
            
            if 'data' in data:
                alpha_data = data['data'].get('alpha', [])
                timestamp_data = data['data'].get('timestamp', [])
                print(f"數據點數: {len(alpha_data)}")
                
                if alpha_data:
                    print(f"TI範圍: {min(alpha_data):.3f} 到 {max(alpha_data):.3f}")
                    print(f"最近5個TI值: {alpha_data[-5:]}")
        else:
            print(f"❌ 歷史數據獲取失敗: {response.text}")
            
    except Exception as e:
        print(f"❌ 歷史數據測試異常: {e}")

if __name__ == "__main__":
    asyncio.run(test_blave_api())

#!/usr/bin/env python3
"""
自動化多幣種RSI濾網策略報單系統
- 自動篩選符合條件的策略組合
- 多幣種實時信號生成
- Telegram報單推送
- 交易監控和P&L計算
- 每日參數優化更新
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import json
import logging
from typing import Dict, List, Optional
import aiohttp

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rsi_signal_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutoRSISignalSystem:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)

        # 策略篩選條件
        self.min_win_rate = 70.0  # 勝率 ≥ 70%
        self.min_total_return = 5.0  # 總收益 ≥ 5%
        self.min_daily_signals = 0.5  # 每日平均信號 ≥ 0.5個

        # 測試幣種池（50+幣種）
        self.test_symbols = [
            # 主流幣
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT',

            # 大型山寨幣
            'ADAUSDT', 'DOTUSDT', 'LTCUSDT', 'LINKUSDT', 'AVAXUSDT',
            'MATICUSDT', 'ALGOUSDT', 'ATOMUSDT', 'UNIUSDT', 'AAVEUSDT',

            # 中型山寨幣
            'DOGEUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT', 'SUIUSDT',
            'INJUSDT', 'TIAUSDT', 'SEIUSDT', 'WLDUSDT',

            # 小型山寨幣
            'GRTUSDT', 'SANDUSDT', 'MANAUSDT', 'ENJUSDT', 'CHZUSDT',
            'BATUSDT', 'COMPUSDT', 'MKRUSDT', 'SUSHIUSDT', 'CRVUSDT',
            '1INCHUSDT', 'SNXUSDT', 'LRCUSDT', 'BANDUSDT', 'ZRXUSDT',

            # 新興山寨幣
            'WIFUSDT', 'BOMEUSDT', 'JUPUSDT', 'PYTHUSDT', 'MEMEUSDT',
            'NOTUSDT'
        ]

        # 測試時間框架
        self.timeframes = ['1H', '4H']

        # RSI濾網參數
        self.rsi_period = 14
        self.rsi_long_threshold = 70
        self.rsi_short_threshold = 30

        # Taker Intensity參數
        self.ti_lookback = 24
        self.confidence_level = 0.70

        # 布林帶參數範圍
        self.bb_window_range = (10, 30)
        self.bb_std_range = (1.5, 2.5)

        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0]

        # ATR週期
        self.atr_period = 14

        # 活躍策略組合
        self.active_strategies = {}

        # 活躍交易記錄
        self.active_trades = {}

        # Telegram配置
        self.telegram_bot_token = "YOUR_BOT_TOKEN"
        self.telegram_chat_id = "YOUR_CHAT_ID"

    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)

        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()

        return atr

    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算RSI"""
        close = data['Close']
        delta = close.diff()

        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int, std_dev: float) -> pd.DataFrame:
        """計算布林帶"""
        data = data.copy()

        data['BB_Middle'] = data['Close'].rolling(window=window).mean()
        rolling_std = data['Close'].rolling(window=window).std()
        data['BB_Upper'] = data['BB_Middle'] + (rolling_std * std_dev)
        data['BB_Lower'] = data['BB_Middle'] - (rolling_std * std_dev)

        return data

    def calculate_ti_confidence_intervals(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算Taker Intensity的滾動70%信賴區間"""
        data = data.copy()

        data['TI_Lower_70'] = np.nan
        data['TI_Upper_70'] = np.nan

        for i in range(self.ti_lookback, len(data)):
            ti_window = data['taker_intensity'].iloc[i-self.ti_lookback:i]

            if len(ti_window.dropna()) >= self.ti_lookback * 0.8:
                lower_percentile = (1 - self.confidence_level) / 2 * 100  # 15%
                upper_percentile = (1 + self.confidence_level) / 2 * 100  # 85%

                data.iloc[i, data.columns.get_loc('TI_Lower_70')] = np.percentile(ti_window.dropna(), lower_percentile)
                data.iloc[i, data.columns.get_loc('TI_Upper_70')] = np.percentile(ti_window.dropna(), upper_percentile)

        return data

    async def get_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取數據"""
        try:
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)

            if data is None or data.empty:
                return None

            # 檢查必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                return None

            # 清理數據
            data_clean = data.dropna(subset=required_columns)

            if len(data_clean) < 100:
                return None

            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] -
                                           data_clean['short_taker_intensity'])

            return data_clean

        except Exception as e:
            logger.error(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None

    def generate_rsi_signals(self, data: pd.DataFrame, bb_window: int, bb_std: float) -> list:
        """生成RSI濾網信號"""
        signals = []

        # 計算所有指標
        data = self.calculate_bollinger_bands(data, bb_window, bb_std)
        data['RSI'] = self.calculate_rsi(data, self.rsi_period)
        data['atr'] = self.calculate_atr(data, self.atr_period)
        data = self.calculate_ti_confidence_intervals(data)

        start_idx = max(bb_window, self.ti_lookback, self.atr_period, self.rsi_period) + 5

        for i in range(start_idx, len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            atr = data['atr'].iloc[i]
            rsi = data['RSI'].iloc[i]

            bb_upper = data['BB_Upper'].iloc[i]
            bb_lower = data['BB_Lower'].iloc[i]

            ti_upper_70 = data['TI_Upper_70'].iloc[i]
            ti_lower_70 = data['TI_Lower_70'].iloc[i]

            # 跳過NaN值
            if pd.isna(bb_upper) or pd.isna(ti) or pd.isna(atr) or pd.isna(ti_upper_70) or pd.isna(rsi):
                continue

            # 多頭信號條件（RSI>=70）
            long_conditions = [
                price > bb_upper,
                prev_price <= bb_upper,
                ti > ti_upper_70,
                ti > 0,
                rsi >= self.rsi_long_threshold  # RSI>=70
            ]

            # 空頭信號條件（RSI<=30）
            short_conditions = [
                price < bb_lower,
                prev_price >= bb_lower,
                ti < ti_lower_70,
                ti < 0,
                rsi <= self.rsi_short_threshold  # RSI<=30
            ]

            if all(long_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi
                })

            elif all(short_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'rsi': rsi
                })

        return signals

    def execute_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行交易回測"""
        trades = []

        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']

            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio

            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance

            exit_result = self.find_exit(data, entry_time, entry_price, direction,
                                       stop_loss_price, take_profit_price)

            if exit_result:
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'price_change_pct': exit_result['price_change_pct'],
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'rsi': signal['rsi']
                }

                trades.append(trade)

        return trades

    def find_exit(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                 stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點"""
        try:
            entry_idx = data.index.get_loc(entry_time)

            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]

                if direction == 'LONG':
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }

            # 數據結束時平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]

            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100

            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }

        except Exception as e:
            return None

    def calculate_performance(self, trades: list) -> dict:
        """計算策略表現"""
        if not trades:
            return {
                'total_trades': 0, 'long_trades': 0, 'short_trades': 0,
                'win_rate': 0, 'avg_return_pct': 0, 'profit_factor': 0,
                'take_profit_rate': 0, 'stop_loss_rate': 0, 'strategy_score': 0,
                'total_return_pct': 0
            }

        total_trades = len(trades)
        long_trades = [t for t in trades if t['direction'] == 'LONG']
        short_trades = [t for t in trades if t['direction'] == 'SHORT']

        winning_trades = [t for t in trades if t['price_change_pct'] > 0]
        losing_trades = [t for t in trades if t['price_change_pct'] < 0]

        win_rate = len(winning_trades) / total_trades * 100
        avg_return_pct = np.mean([t['price_change_pct'] for t in trades])
        total_return_pct = sum([t['price_change_pct'] for t in trades])

        gross_profit = sum([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['price_change_pct'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])
        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])

        take_profit_rate = take_profit_count / total_trades * 100
        stop_loss_rate = stop_loss_count / total_trades * 100

        long_short_balance = abs(len(long_trades) - len(short_trades)) / total_trades * 100

        # 策略評分
        strategy_score = (
            win_rate * 0.25 +
            min(profit_factor * 12, 100) * 0.25 +
            min(abs(avg_return_pct) * 15, 100) * 0.3 +
            (100 - long_short_balance) * 0.1 +
            take_profit_rate * 0.1
        )

        return {
            'total_trades': total_trades,
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'win_rate': win_rate,
            'avg_return_pct': avg_return_pct,
            'total_return_pct': total_return_pct,
            'profit_factor': profit_factor,
            'take_profit_rate': take_profit_rate,
            'stop_loss_rate': stop_loss_rate,
            'long_short_balance': long_short_balance,
            'strategy_score': strategy_score
        }

    async def optimize_strategy(self, symbol: str, timeframe: str) -> dict:
        """優化單個策略"""
        try:
            data = await self.get_data(symbol, timeframe)
            if data is None:
                return None

            best_score = 0
            best_params = {}

            # 簡化參數搜索
            window_range = range(self.bb_window_range[0], self.bb_window_range[1] + 1, 4)
            std_range = np.arange(self.bb_std_range[0], self.bb_std_range[1] + 0.1, 0.2)

            for bb_window in window_range:
                for bb_std in std_range:
                    for rr in self.risk_reward_ratios:
                        try:
                            signals = self.generate_rsi_signals(data, bb_window, bb_std)

                            if len(signals) < 2:
                                continue

                            trades = self.execute_trades(data, signals, rr)

                            if len(trades) < 2:
                                continue

                            performance = self.calculate_performance(trades)

                            if performance['total_trades'] == 0:
                                continue

                            score = performance['strategy_score']

                            if score > best_score:
                                best_score = score
                                best_params = {
                                    'symbol': symbol,
                                    'timeframe': timeframe,
                                    'bb_window': bb_window,
                                    'bb_std': round(bb_std, 1),
                                    'risk_reward_ratio': rr,
                                    'score': round(score, 2),
                                    'signals': len(signals),
                                    **performance
                                }

                        except Exception as e:
                            continue

            return best_params if best_params else None

        except Exception as e:
            logger.error(f"優化 {symbol} {timeframe} 策略失敗: {e}")
            return None

    def filter_qualified_strategies(self, all_strategies: list) -> list:
        """篩選符合條件的策略"""
        qualified = []

        for strategy in all_strategies:
            if strategy is None:
                continue

            # 計算每日平均信號數（8天數據）
            daily_signals = strategy['signals'] / 8.0

            # 檢查篩選條件
            conditions = [
                strategy['win_rate'] >= self.min_win_rate,  # 勝率 >= 70%
                strategy['total_return_pct'] >= self.min_total_return,  # 總收益 >= 5%
                daily_signals >= self.min_daily_signals  # 每日平均信號 >= 1個
            ]

            if all(conditions):
                strategy['daily_signals'] = round(daily_signals, 2)
                qualified.append(strategy)
                logger.info(f"✅ 符合條件: {strategy['symbol']} {strategy['timeframe']} - "
                          f"勝率{strategy['win_rate']:.1f}% 總收益{strategy['total_return_pct']:.1f}% "
                          f"日均信號{daily_signals:.1f}個")

        # 按策略評分排序
        qualified.sort(key=lambda x: x['score'], reverse=True)

        return qualified

    async def send_telegram_message(self, message: str):
        """發送Telegram消息"""
        try:
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info("Telegram消息發送成功")
                    else:
                        logger.error(f"Telegram消息發送失敗: {response.status}")

        except Exception as e:
            logger.error(f"發送Telegram消息失敗: {e}")

    async def generate_live_signal(self, strategy: dict) -> dict:
        """生成實時信號"""
        try:
            symbol = strategy['symbol']
            timeframe = strategy['timeframe']
            bb_window = strategy['bb_window']
            bb_std = strategy['bb_std']
            risk_reward_ratio = strategy['risk_reward_ratio']

            # 獲取最新數據
            data = await self.get_data(symbol, timeframe)
            if data is None:
                return None

            # 生成信號
            signals = self.generate_rsi_signals(data, bb_window, bb_std)

            if not signals:
                return None

            # 取最新信號
            latest_signal = signals[-1]
            current_time = datetime.now()
            signal_time = latest_signal['timestamp']

            # 檢查信號是否為最近生成（避免重複信號）
            time_diff = (current_time - signal_time).total_seconds() / 3600

            if time_diff > 2:  # 信號超過2小時則忽略
                return None

            # 計算止盈止損
            entry_price = latest_signal['price']
            atr = latest_signal['atr']
            direction = latest_signal['type']

            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio

            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance

            signal_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'direction': direction,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'atr': atr,
                'rsi': latest_signal['rsi'],
                'ti': latest_signal['ti'],
                'signal_time': signal_time,
                'strategy_score': strategy['score'],
                'win_rate': strategy['win_rate']
            }

            return signal_data

        except Exception as e:
            logger.error(f"生成 {strategy['symbol']} {strategy['timeframe']} 實時信號失敗: {e}")
            return None

    async def send_signal_notification(self, signal: dict):
        """發送信號通知"""
        try:
            direction_emoji = "🟢" if signal['direction'] == 'LONG' else "🔴"

            message = f"""
{direction_emoji} <b>多空策略信號</b>

💰 <b>幣種:</b> {signal['symbol']}
⏰ <b>時框:</b> {signal['timeframe']}
📈 <b>方向:</b> {signal['direction']}
💵 <b>入場價:</b> ${signal['entry_price']:.6f}

🎯 <b>止盈價:</b> ${signal['take_profit_price']:.6f}
🛑 <b>止損價:</b> ${signal['stop_loss_price']:.6f}
"""

            await self.send_telegram_message(message)

            # 記錄活躍交易
            trade_id = f"{signal['symbol']}_{signal['timeframe']}_{signal['signal_time'].strftime('%Y%m%d_%H%M%S')}"
            self.active_trades[trade_id] = {
                **signal,
                'trade_id': trade_id,
                'status': 'ACTIVE',
                'entry_time': signal['signal_time']
            }

            logger.info(f"信號通知已發送: {signal['symbol']} {signal['timeframe']} {signal['direction']}")

        except Exception as e:
            logger.error(f"發送信號通知失敗: {e}")

    async def monitor_active_trades(self):
        """監控活躍交易"""
        try:
            for trade_id, trade in list(self.active_trades.items()):
                if trade['status'] != 'ACTIVE':
                    continue

                symbol = trade['symbol']
                timeframe = trade['timeframe']

                # 獲取最新價格
                data = await self.get_data(symbol, timeframe)
                if data is None:
                    continue

                current_price = data['Close'].iloc[-1]
                entry_price = trade['entry_price']
                stop_loss_price = trade['stop_loss_price']
                take_profit_price = trade['take_profit_price']
                direction = trade['direction']

                # 檢查是否觸發止盈或止損
                exit_reason = None
                exit_price = None

                if direction == 'LONG':
                    if current_price >= take_profit_price:
                        exit_reason = 'TAKE_PROFIT'
                        exit_price = take_profit_price
                    elif current_price <= stop_loss_price:
                        exit_reason = 'STOP_LOSS'
                        exit_price = stop_loss_price
                else:
                    if current_price <= take_profit_price:
                        exit_reason = 'TAKE_PROFIT'
                        exit_price = take_profit_price
                    elif current_price >= stop_loss_price:
                        exit_reason = 'STOP_LOSS'
                        exit_price = stop_loss_price

                if exit_reason:
                    # 計算收益
                    if direction == 'LONG':
                        pnl_pct = (exit_price / entry_price - 1) * 100
                    else:
                        pnl_pct = (entry_price / exit_price - 1) * 100

                    # 更新交易狀態
                    trade['status'] = 'CLOSED'
                    trade['exit_time'] = datetime.now()
                    trade['exit_price'] = exit_price
                    trade['exit_reason'] = exit_reason
                    trade['pnl_pct'] = pnl_pct

                    # 發送平倉通知
                    await self.send_close_notification(trade)

        except Exception as e:
            logger.error(f"監控活躍交易失敗: {e}")

    async def send_close_notification(self, trade: dict):
        """發送平倉通知"""
        try:
            result_emoji = "✅" if trade['exit_reason'] == 'TAKE_PROFIT' else "❌"
            pnl_emoji = "📈" if trade['pnl_pct'] > 0 else "📉"

            message = f"""
{result_emoji} <b>交易平倉通知</b>

💰 <b>幣種:</b> {trade['symbol']}
⏰ <b>時框:</b> {trade['timeframe']}
📈 <b>方向:</b> {trade['direction']}

💵 <b>入場價:</b> ${trade['entry_price']:.6f}
💵 <b>出場價:</b> ${trade['exit_price']:.6f}
🏁 <b>平倉原因:</b> {trade['exit_reason']}

{pnl_emoji} <b>收益率:</b> {trade['pnl_pct']:+.2f}%

⏰ <b>入場時間:</b> {trade['entry_time'].strftime('%Y-%m-%d %H:%M:%S')}
⏰ <b>出場時間:</b> {trade['exit_time'].strftime('%Y-%m-%d %H:%M:%S')}
"""

            await self.send_telegram_message(message)
            logger.info(f"平倉通知已發送: {trade['symbol']} {trade['direction']} {trade['pnl_pct']:+.2f}%")

        except Exception as e:
            logger.error(f"發送平倉通知失敗: {e}")

    async def daily_strategy_optimization(self):
        """每日策略優化"""
        try:
            logger.info("🔄 開始每日策略優化...")

            # 對所有測試幣種進行優化
            all_strategies = []

            for symbol in self.test_symbols:
                for timeframe in self.timeframes:
                    try:
                        strategy = await self.optimize_strategy(symbol, timeframe)
                        if strategy:
                            all_strategies.append(strategy)

                        await asyncio.sleep(0.5)  # 避免API限制

                    except Exception as e:
                        logger.error(f"優化 {symbol} {timeframe} 失敗: {e}")

            # 篩選符合條件的策略
            qualified_strategies = self.filter_qualified_strategies(all_strategies)

            if not qualified_strategies:
                logger.warning("⚠️ 未找到符合條件的策略")
                await self.send_telegram_message("⚠️ 今日策略優化未找到符合條件的策略組合")
                return

            # 更新活躍策略
            self.active_strategies = {}
            for strategy in qualified_strategies:
                key = f"{strategy['symbol']}_{strategy['timeframe']}"
                self.active_strategies[key] = strategy

            # 發送優化結果通知
            await self.send_optimization_report(qualified_strategies)

            logger.info(f"✅ 策略優化完成，共{len(qualified_strategies)}個符合條件的策略")

        except Exception as e:
            logger.error(f"每日策略優化失敗: {e}")

    async def send_optimization_report(self, strategies: list):
        """發送優化報告"""
        try:
            message = f"""
🔄 <b>每日策略優化報告</b>

📊 <b>測試結果:</b>
• 測試幣種: {len(self.test_symbols)}個
• 符合條件策略: {len(strategies)}個

🏆 <b>前5名策略:</b>
"""

            for i, strategy in enumerate(strategies[:5], 1):
                message += f"""
{i}. <b>{strategy['symbol']} {strategy['timeframe']}</b>
   評分: {strategy['score']:.1f} | 勝率: {strategy['win_rate']:.1f}%
   總收益: {strategy['total_return_pct']:+.1f}% | 日均信號: {strategy['daily_signals']:.1f}個
"""

            message += f"""
📈 <b>篩選條件:</b>
• 勝率 ≥ {self.min_win_rate}%
• 總收益 ≥ {self.min_total_return}%
• 日均信號 ≥ {self.min_daily_signals}個

⏰ <b>優化時間:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            await self.send_telegram_message(message)

        except Exception as e:
            logger.error(f"發送優化報告失敗: {e}")

    async def signal_generation_loop(self):
        """信號生成循環"""
        try:
            while True:
                if not self.active_strategies:
                    logger.info("無活躍策略，等待下次檢查...")
                    await asyncio.sleep(300)  # 5分鐘後重試
                    continue

                logger.info(f"🔍 檢查 {len(self.active_strategies)} 個活躍策略的信號...")

                for key, strategy in self.active_strategies.items():
                    try:
                        signal = await self.generate_live_signal(strategy)

                        if signal:
                            # 檢查是否為重複信號
                            signal_key = f"{signal['symbol']}_{signal['timeframe']}_{signal['signal_time'].strftime('%Y%m%d_%H')}"

                            if signal_key not in [t.get('signal_key') for t in self.active_trades.values()]:
                                await self.send_signal_notification(signal)
                                signal['signal_key'] = signal_key

                        await asyncio.sleep(1)  # 避免API限制

                    except Exception as e:
                        logger.error(f"檢查 {key} 信號失敗: {e}")

                # 監控活躍交易
                await self.monitor_active_trades()

                # 等待下次檢查（每小時檢查一次）
                await asyncio.sleep(3600)

        except Exception as e:
            logger.error(f"信號生成循環失敗: {e}")

    async def run_system(self):
        """運行系統"""
        try:
            logger.info("🚀 啟動自動化RSI濾網策略報單系統")

            # 發送啟動通知
            await self.send_telegram_message("🚀 自動化RSI濾網策略報單系統已啟動")

            # 首次策略優化
            await self.daily_strategy_optimization()

            # 創建定時任務
            async def daily_optimization_task():
                while True:
                    # 每天凌晨2點執行策略優化
                    now = datetime.now()
                    next_run = now.replace(hour=2, minute=0, second=0, microsecond=0)
                    if next_run <= now:
                        next_run += timedelta(days=1)

                    sleep_seconds = (next_run - now).total_seconds()
                    await asyncio.sleep(sleep_seconds)

                    await self.daily_strategy_optimization()

            # 並行運行信號生成和每日優化
            await asyncio.gather(
                self.signal_generation_loop(),
                daily_optimization_task()
            )

        except Exception as e:
            logger.error(f"系統運行失敗: {e}")
            await self.send_telegram_message(f"❌ 系統運行失敗: {e}")

async def main():
    """主函數"""
    system = AutoRSISignalSystem()

    try:
        await system.run_system()

    except KeyboardInterrupt:
        logger.info("⚠️ 系統被用戶中斷")
        await system.send_telegram_message("⚠️ RSI濾網策略報單系統已停止")
    except Exception as e:
        logger.error(f"❌ 系統失敗: {e}")

if __name__ == "__main__":
    asyncio.run(main())
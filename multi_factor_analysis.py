"""
多因子分析工具 - 籌碼集中度、多方力道、空方力道對價格的解釋力分析
使用迴歸分析檢驗各因子的統計顯著性

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_data_for_analysis():
    """載入數據進行多因子分析"""
    print("載入數據進行多因子分析...")
    
    # 這裡應該載入真實的市場數據
    # 為了演示，我們生成模擬數據
    np.random.seed(42)
    
    dates = pd.date_range(start='2022-01-01', end='2024-12-31', freq='D')
    n = len(dates)
    
    # 生成BTC價格數據
    price_base = 50000
    price_trend = np.linspace(0, 50000, n)  # 上升趨勢
    price_noise = np.random.normal(0, 2000, n)
    prices = price_base + price_trend + price_noise
    
    # 確保價格為正
    prices = np.maximum(prices, 10000)
    
    # 計算價格變化
    returns = np.diff(prices) / prices[:-1]
    returns = np.concatenate([[0], returns])
    
    # 生成籌碼集中度 (與價格變化負相關)
    concentration = 0.5 + returns * (-0.3) + np.random.normal(0, 0.05, n)
    concentration = np.clip(concentration, 0.1, 0.9)
    
    # 生成多方力道 (與價格上漲正相關)
    long_intensity = 50 + returns * 200 + np.random.normal(0, 10, n)
    long_intensity = np.clip(long_intensity, 0, 100)
    
    # 生成空方力道 (與價格下跌正相關)
    short_intensity = 50 - returns * 200 + np.random.normal(0, 10, n)
    short_intensity = np.clip(short_intensity, 0, 100)
    
    df = pd.DataFrame({
        'date': dates,
        'price': prices,
        'returns': returns,
        'concentration': concentration,
        'long_intensity': long_intensity,
        'short_intensity': short_intensity
    })
    
    print(f"數據載入完成：{len(df)}條記錄")
    return df

def calculate_factor_correlations(df):
    """計算因子相關性分析"""
    print("\n=== 因子相關性分析 ===")
    
    # 選擇分析的因子
    factors = ['returns', 'concentration', 'long_intensity', 'short_intensity']
    corr_matrix = df[factors].corr()
    
    print("相關性矩陣:")
    print(corr_matrix.round(4))
    
    # 繪製相關性熱力圖
    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f', cbar_kws={'label': '相關係數'})
    plt.title('因子相關性熱力圖')
    plt.tight_layout()
    plt.show()
    
    return corr_matrix

def single_factor_regression(df):
    """單因子迴歸分析"""
    print("\n=== 單因子迴歸分析 ===")
    
    factors = ['concentration', 'long_intensity', 'short_intensity']
    results = {}
    
    for factor in factors:
        # 準備數據
        X = df[factor].values.reshape(-1, 1)
        y = df['returns'].values
        
        # 移除NaN值
        mask = ~(np.isnan(X.flatten()) | np.isnan(y))
        X_clean = X[mask]
        y_clean = y[mask]
        
        if len(X_clean) > 10:
            # 線性迴歸
            model = LinearRegression()
            model.fit(X_clean, y_clean)
            
            # 預測
            y_pred = model.predict(X_clean)
            
            # 計算統計指標
            r2 = r2_score(y_clean, y_pred)
            
            # t檢驗
            n = len(X_clean)
            residuals = y_clean - y_pred
            mse = np.mean(residuals**2)
            se_beta = np.sqrt(mse / np.sum((X_clean.flatten() - np.mean(X_clean))**2))
            t_stat = model.coef_[0] / se_beta
            p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n-2))
            
            results[factor] = {
                'coefficient': model.coef_[0],
                'intercept': model.intercept_,
                'r_squared': r2,
                't_statistic': t_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
            
            print(f"\n{factor} 對價格變化的解釋力:")
            print(f"  迴歸係數: {model.coef_[0]:.6f}")
            print(f"  R²: {r2:.4f}")
            print(f"  t統計量: {t_stat:.4f}")
            print(f"  p值: {p_value:.6f}")
            print(f"  統計顯著性: {'是' if p_value < 0.05 else '否'}")
    
    return results

def multi_factor_regression(df):
    """多因子迴歸分析"""
    print("\n=== 多因子迴歸分析 ===")
    
    # 準備數據
    factors = ['concentration', 'long_intensity', 'short_intensity']
    X = df[factors].values
    y = df['returns'].values
    
    # 移除包含NaN的行
    mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
    X_clean = X[mask]
    y_clean = y[mask]
    
    if len(X_clean) > 10:
        # 多元線性迴歸
        model = LinearRegression()
        model.fit(X_clean, y_clean)
        
        # 預測
        y_pred = model.predict(X_clean)
        
        # 計算統計指標
        r2 = r2_score(y_clean, y_pred)
        adjusted_r2 = 1 - (1 - r2) * (len(y_clean) - 1) / (len(y_clean) - len(factors) - 1)
        
        print(f"多因子模型結果:")
        print(f"  R²: {r2:.4f}")
        print(f"  調整R²: {adjusted_r2:.4f}")
        print(f"  截距項: {model.intercept_:.6f}")
        
        for i, factor in enumerate(factors):
            print(f"  {factor}係數: {model.coef_[i]:.6f}")
        
        # 計算各因子的重要性
        feature_importance = np.abs(model.coef_) / np.sum(np.abs(model.coef_))
        
        print(f"\n因子重要性排序:")
        importance_df = pd.DataFrame({
            'factor': factors,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        for _, row in importance_df.iterrows():
            print(f"  {row['factor']}: {row['importance']:.2%}")
        
        return {
            'model': model,
            'r_squared': r2,
            'adjusted_r_squared': adjusted_r2,
            'feature_importance': importance_df
        }
    
    return None

def factor_performance_analysis(df):
    """因子表現分析"""
    print("\n=== 因子表現分析 ===")
    
    # 計算滾動相關性
    window = 60  # 60天滾動窗口
    
    factors = ['concentration', 'long_intensity', 'short_intensity']
    rolling_corr = {}
    
    for factor in factors:
        rolling_corr[factor] = df['returns'].rolling(window).corr(df[factor])
    
    # 繪製滾動相關性圖
    plt.figure(figsize=(15, 10))
    
    for i, factor in enumerate(factors):
        plt.subplot(2, 2, i+1)
        plt.plot(df['date'], rolling_corr[factor], label=f'{factor} vs Returns')
        plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        plt.title(f'{factor}與價格變化的滾動相關性 ({window}天)')
        plt.xlabel('日期')
        plt.ylabel('相關係數')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    # 因子穩定性分析
    plt.subplot(2, 2, 4)
    for factor in factors:
        corr_std = rolling_corr[factor].rolling(30).std()
        plt.plot(df['date'], corr_std, label=f'{factor} 相關性波動')
    
    plt.title('因子相關性穩定性分析')
    plt.xlabel('日期')
    plt.ylabel('相關係數標準差')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 計算因子穩定性指標
    stability_metrics = {}
    for factor in factors:
        corr_series = rolling_corr[factor].dropna()
        if len(corr_series) > 0:
            stability_metrics[factor] = {
                'mean_correlation': corr_series.mean(),
                'correlation_std': corr_series.std(),
                'stability_ratio': abs(corr_series.mean()) / corr_series.std() if corr_series.std() > 0 else 0
            }
    
    print("因子穩定性指標:")
    for factor, metrics in stability_metrics.items():
        print(f"\n{factor}:")
        print(f"  平均相關性: {metrics['mean_correlation']:.4f}")
        print(f"  相關性標準差: {metrics['correlation_std']:.4f}")
        print(f"  穩定性比率: {metrics['stability_ratio']:.4f}")
    
    return stability_metrics

def generate_factor_report(df):
    """生成完整的因子分析報告"""
    print("\n" + "="*80)
    print("多因子分析報告")
    print("="*80)
    
    # 1. 相關性分析
    corr_results = calculate_factor_correlations(df)
    
    # 2. 單因子迴歸
    single_results = single_factor_regression(df)
    
    # 3. 多因子迴歸
    multi_results = multi_factor_regression(df)
    
    # 4. 因子表現分析
    stability_results = factor_performance_analysis(df)
    
    # 5. 總結報告
    print("\n" + "="*60)
    print("分析總結")
    print("="*60)
    
    print("\n1. 因子解釋力排序:")
    if single_results:
        sorted_factors = sorted(single_results.items(), 
                              key=lambda x: abs(x[1]['r_squared']), 
                              reverse=True)
        
        for factor, results in sorted_factors:
            significance = "顯著" if results['significant'] else "不顯著"
            print(f"   {factor}: R² = {results['r_squared']:.4f} ({significance})")
    
    print("\n2. 統計顯著性檢驗:")
    if single_results:
        significant_factors = [f for f, r in single_results.items() if r['significant']]
        print(f"   顯著因子: {', '.join(significant_factors) if significant_factors else '無'}")
    
    print("\n3. 多因子模型表現:")
    if multi_results:
        print(f"   整體解釋力 (R²): {multi_results['r_squared']:.4f}")
        print(f"   調整後解釋力: {multi_results['adjusted_r_squared']:.4f}")
    
    print("\n4. 因子穩定性評估:")
    if stability_results:
        most_stable = max(stability_results.items(), 
                         key=lambda x: x[1]['stability_ratio'])
        print(f"   最穩定因子: {most_stable[0]} (穩定性比率: {most_stable[1]['stability_ratio']:.4f})")
    
    return {
        'correlations': corr_results,
        'single_factor': single_results,
        'multi_factor': multi_results,
        'stability': stability_results
    }

if __name__ == "__main__":
    print("="*80)
    print("多因子分析工具")
    print("分析籌碼集中度、多方力道、空方力道對價格的解釋力")
    print("="*80)
    
    # 載入數據
    data = load_data_for_analysis()
    
    # 執行完整分析
    analysis_results = generate_factor_report(data)
    
    # 保存分析結果
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存數據
    data.to_csv(f"factor_analysis_data_{timestamp}.csv", index=False)
    
    # 保存分析結果摘要
    summary = {
        'analysis_date': timestamp,
        'data_points': len(data),
        'factors_analyzed': ['concentration', 'long_intensity', 'short_intensity']
    }
    
    if analysis_results['single_factor']:
        summary['single_factor_results'] = {
            factor: {
                'r_squared': results['r_squared'],
                'significant': results['significant']
            }
            for factor, results in analysis_results['single_factor'].items()
        }
    
    if analysis_results['multi_factor']:
        summary['multi_factor_r_squared'] = analysis_results['multi_factor']['r_squared']
    
    import json
    with open(f"factor_analysis_summary_{timestamp}.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n分析完成！結果已保存到:")
    print(f"  數據文件: factor_analysis_data_{timestamp}.csv")
    print(f"  摘要文件: factor_analysis_summary_{timestamp}.json")

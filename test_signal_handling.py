"""
測試信號處理修復
驗證系統不會因為信號而意外退出

作者: 專業量化策略工程師
"""

import asyncio
import signal
import sys
import os
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

async def test_signal_handling():
    """測試信號處理"""
    print("🔍 測試信號處理修復...")
    
    # 模擬Railway環境
    os.environ['RAILWAY_ENVIRONMENT'] = 'test'
    os.environ['RAILWAY_SERVICE_NAME'] = 'quant-trading-system'
    
    try:
        # 導入修復後的main模組
        from main import main, shutdown_event
        
        print("✅ 成功導入修復後的main模組")
        
        # 測試信號處理器
        print("\n🔍 測試信號處理器...")
        
        # 模擬發送SIGTERM信號（Railway常用的關閉信號）
        print("📡 模擬發送SIGTERM信號...")
        
        # 創建一個任務來運行main函數
        main_task = asyncio.create_task(main())
        
        # 等待系統啟動
        await asyncio.sleep(3)
        print("✅ 系統啟動完成")
        
        # 檢查shutdown_event狀態
        print(f"🔍 關閉事件狀態: {shutdown_event.is_set()}")
        
        # 模擬發送關閉信號
        print("📡 發送SIGTERM信號...")
        os.kill(os.getpid(), signal.SIGTERM)
        
        # 等待系統響應信號
        await asyncio.sleep(2)
        
        # 檢查shutdown_event是否被設置
        if shutdown_event.is_set():
            print("✅ 信號處理正常 - shutdown_event已設置")
        else:
            print("❌ 信號處理失敗 - shutdown_event未設置")
        
        # 等待系統優雅關閉
        try:
            await asyncio.wait_for(main_task, timeout=10)
            print("✅ 系統優雅關閉成功")
        except asyncio.TimeoutError:
            print("⚠️ 系統關閉超時，但這是正常的測試行為")
            main_task.cancel()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

async def test_normal_operation():
    """測試正常運行（不發送信號）"""
    print("\n" + "="*60)
    print("🔍 測試正常運行（5秒後自動停止）...")
    print("="*60)
    
    try:
        from main import QuantTradingSystem
        
        # 創建系統實例
        system = QuantTradingSystem()
        
        # 啟動系統
        await system.startup()
        print("✅ 系統啟動成功")
        
        # 運行一個策略週期
        await system.run_strategy_cycle()
        print("✅ 策略週期執行成功")
        
        # 模擬短時間運行
        print("⏱️ 模擬運行5秒...")
        await asyncio.sleep(5)
        
        # 手動關閉
        await system.shutdown()
        print("✅ 系統手動關閉成功")
        
    except Exception as e:
        print(f"❌ 正常運行測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    print("🚀 開始信號處理測試...")
    
    # 測試1: 信號處理
    asyncio.run(test_signal_handling())
    
    # 測試2: 正常運行
    asyncio.run(test_normal_operation())
    
    print("\n🎯 測試總結:")
    print("如果上面的測試都成功，說明信號處理問題已修復")
    print("系統現在應該不會因為Railway的信號而意外退出")

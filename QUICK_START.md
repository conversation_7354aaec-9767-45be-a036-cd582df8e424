# 🚀 聖杯級交易信號系統 - 快速啟動指南

## 📋 系統概述

這是一個基於CCB (Chip Concentration Bands) + Taker Intensity策略的專業級加密貨幣交易信號系統，經過嚴格回測驗證，在PEPE、XRP、SOL的1小時時框上達到聖杯級別的表現。

### 🏆 核心策略表現
- **PEPE-1H**: 夏普比率 7.70, 年化收益 71.85%, 最大回撤 -13.64%
- **XRP-1H**: 夏普比率 7.04, 年化收益 34.45%, 最大回撤 -10.81%
- **SOL-1H**: 夏普比率 6.25, 年化收益 39.12%, 最大回撤 -13.47%

## ⚡ 快速部署 (推薦)

### 1. 克隆倉庫
```bash
git clone https://github.com/YCRicky/QUANT.git
cd QUANT
```

### 2. 配置API密鑰
```bash
# 複製環境變量模板
cp .env.template .env

# 編輯.env文件，填入您的API密鑰
nano .env
```

需要配置的API密鑰：
- **Blave API**: 已預設，無需修改
- **Bybit API**: 用於獲取價格數據 (可選)
- **Telegram Bot**: 用於接收交易信號通知

### 3. 一鍵部署
```bash
# 給部署腳本執行權限
chmod +x deploy.sh

# 運行部署腳本
./deploy.sh
```

### 4. 監控系統
```bash
# 查看系統日誌
docker-compose logs -f quant-system

# 檢查系統狀態
docker-compose ps
```

## 🔧 手動部署

### 1. 安裝依賴
```bash
pip install -r requirements.txt
```

### 2. 配置文件
```bash
# 複製API密鑰模板
cp config/api_keys.yaml.template config/api_keys.yaml

# 編輯配置文件
nano config/api_keys.yaml
```

### 3. 運行系統
```bash
python main.py
```

## 📱 Telegram Bot 設置

### 1. 創建Telegram Bot
1. 在Telegram中搜索 `@BotFather`
2. 發送 `/newbot` 命令
3. 按提示設置Bot名稱和用戶名
4. 獲取Bot Token

### 2. 獲取Chat ID
1. 將Bot添加到您的聊天中
2. 發送任意消息給Bot
3. 訪問: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. 在返回的JSON中找到 `chat.id`

### 3. 配置環境變量
```bash
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

## 🎯 系統功能

### 實時信號監控
- 每小時自動分析PEPE、XRP、SOL市場
- 基於CCB和Taker Intensity生成交易信號
- 實時Telegram通知

### 交易信號類型
- **多頭信號**: 籌碼集中度跌破下軌 + 多方力道突破閾值
- **空頭信號**: 籌碼集中度突破上軌 + 空方力道突破閾值
- **平倉信號**: 基於反向條件的智能平倉

### 風險管理
- 內建止損止盈機制
- 持倉時間監控
- 實時盈虧計算

## 📊 信號示例

```
📈 PEPE-1H 交易信號
━━━━━━━━━━━━━━━━━━━━
📊 信號類型: LONG
💰 當前價格: $0.000012345
📈 籌碼集中度: 0.234 (跌破下軌)
⚡ 多方力道: 78.5% (突破閾值)
🕐 信號時間: 2024-07-09 15:30:00
━━━━━━━━━━━━━━━━━━━━
```

## 🛠️ 系統管理

### 停止系統
```bash
docker-compose down
```

### 重啟系統
```bash
docker-compose restart
```

### 更新系統
```bash
git pull origin main
docker-compose build
docker-compose up -d
```

### 查看交易記錄
```bash
# 信號記錄
cat data/signals/signals_history.csv

# 交易記錄
cat data/performance/trades_history.csv
```

## 🔍 故障排除

### 常見問題

1. **API連接失敗**
   - 檢查網絡連接
   - 驗證API密鑰是否正確
   - 確認API服務是否正常

2. **Telegram通知不工作**
   - 檢查Bot Token和Chat ID
   - 確認Bot已添加到聊天中
   - 檢查網絡防火牆設置

3. **數據獲取失敗**
   - 檢查Blave API狀態
   - 驗證API密鑰權限
   - 查看系統日誌獲取詳細錯誤

### 日誌查看
```bash
# Docker日誌
docker-compose logs quant-system

# 系統日誌文件
tail -f logs/quant_system_*.log
```

## 📈 性能監控

系統會自動生成以下報告：
- 實時交易信號
- 每日績效總結
- 每週策略分析
- 盈虧統計報告

## ⚠️ 重要提醒

1. **風險聲明**: 本系統僅供教育和研究目的，加密貨幣交易存在高風險
2. **資金管理**: 請合理控制倉位，不要投入超過承受能力的資金
3. **系統監控**: 建議定期檢查系統運行狀態和交易記錄
4. **備份數據**: 定期備份交易記錄和配置文件

## 🆘 技術支持

- **GitHub Issues**: [提交問題](https://github.com/YCRicky/QUANT/issues)
- **系統監控**: 通過Telegram接收系統狀態通知
- **文檔更新**: 查看README.md獲取最新信息

---

**🎉 恭喜！您的聖杯級交易信號系統已準備就緒！**

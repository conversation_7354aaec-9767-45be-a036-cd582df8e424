{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Backtest Report ===\n", "Pair: FIL - SAND\n", "Hurst Exponent of Spread: nan\n", "Performance Metrics:\n", "Total Return                  : 167.8845%\n", "Avg Return per Interval       : 0.0024%\n", "Std Dev                       : 71.9693%\n", "Sharpe Ratio                  : 1.1679\n", "<PERSON><PERSON><PERSON>                 : 1.8239\n", "Calmar Ratio                  : 2.7655\n", "Max Drawdown (Abs)            : -60.7065%\n", "Max Drawdown Period           : 3461 bars (36d 1h 15m)\n", "Trade Count                   : 424\n", "Trading Fees                  : 0.0400%\n", "Fees / PnL Ratio              : 40.4087%\n", "Half-life (bars)              : 2430.1155\n", "Hurst Exponent                : nan\n", "Median Holding Period (bars)  : 3.0000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_12695/3459026252.py:65: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Normal Backtest\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "\n", "# === Core Functions ===\n", "\n", "def load_data(asset1, asset2, path):\n", "    df1 = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df2 = pd.read_csv(f\"{path}/{asset2}/bybit_{asset2.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df1['time'] = pd.to_datetime(df1['time'])\n", "    df2['time'] = pd.to_datetime(df2['time'])\n", "    prices1 = df1[['time', 'close']].rename(columns={'close': asset1})\n", "    prices2 = df2[['time', 'close']].rename(columns={'close': asset2})\n", "    df = pd.merge(prices1, prices2, on='time', how='inner').sort_values('time').reset_index(drop=True)\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "def compute_spread(df, asset1, asset2):\n", "    df['log_' + asset1] = np.log(df[asset1])\n", "    df['log_' + asset2] = np.log(df[asset2])\n", "    johansen = coint_johansen(df[[f'log_{asset1}', f'log_{asset2}']], 0, 1)\n", "    beta = johansen.evec[:, 0]\n", "    hedge_ratio = -beta[1] / beta[0]\n", "    df['spread'] = df[f'log_{asset1}'] - hedge_ratio * df[f'log_{asset2}']\n", "    return df, hedge_ratio\n", "\n", "def fit_ou_model(df):\n", "    df['spread_lag'] = df['spread'].shift(1)\n", "    ou_data = df.dropna(subset=['spread', 'spread_lag'])\n", "    model = sm.OLS(ou_data['spread'], sm.add_constant(ou_data['spread_lag'])).fit()\n", "    phi = model.params[1]\n", "    mu = model.params[0] / (1 - phi)\n", "    sigma = np.std(model.resid)\n", "    return mu, sigma, phi\n", "\n", "def apply_trade_logic(df, mu, sigma):\n", "    df['z_score'] = (df['spread'] - mu) / sigma\n", "    df['position'] = 0\n", "    df.loc[df['z_score'] > upper_entry_threshold, 'position'] = -1\n", "    df.loc[df['z_score'] < lower_entry_threshold, 'position'] = 1\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    exit_signals = ((df['position'] == 1) & (df['z_score'] >= exit_threshold)) | \\\n", "                   ((df['position'] == -1) & (df['z_score'] <= -exit_threshold))\n", "    df.loc[exit_signals, 'position'] = 0\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    return df\n", "\n", "def calculate_returns(df):\n", "    df['spread_return'] = df['spread'].diff()\n", "    df['raw_return'] = df['position'].shift(1) * df['spread_return']\n", "    df['trade'] = df['position'].diff().abs()\n", "    df['fee'] = df['trade'] * 2 * trading_fee_per_leg\n", "    df['strategy_return'] = df['raw_return'] - df['fee']\n", "    df['equity_curve'] = df['strategy_return'].cumsum()\n", "    return df\n", "\n", "def calculate_hurst(spread, max_lag=80, min_valid_points=10):\n", "    lags = range(2, max_lag)\n", "    tau = [np.std(spread[lag:] - spread[:-lag]) for lag in lags if len(spread) - lag > min_valid_points]\n", "    if len(tau) < 2:\n", "        return np.nan\n", "    poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "    return poly[0]\n", "\n", "def calculate_median_holding_period(df):\n", "    df['position_change'] = df['position'].diff()\n", "    trade_starts = df[df['position_change'] != 0].index\n", "    holding_periods = []\n", "    for i in range(0, len(trade_starts) - 1, 2):\n", "        entry = trade_starts[i]\n", "        exit = trade_starts[i + 1] if i + 1 < len(trade_starts) else None\n", "        if exit:\n", "            holding_periods.append(exit - entry)\n", "    return np.median(holding_periods) if holding_periods else np.nan\n", "\n", "def evaluate_performance(df, data_interval_str='1h', phi=None, hurst=None):\n", "    returns = df['strategy_return'].dropna()\n", "    balance_series = df['equity_curve'].dropna()\n", "    fees_paid = df['fee'].sum()\n", "    interval_map = {'15m': 15, '30m': 30, '1h': 60, '3h': 180, '1d': 1440}\n", "    data_interval = interval_map.get(data_interval_str, 60)\n", "    annual_factor = 365 * 24 * 60 / data_interval\n", "    total_return = balance_series.iloc[-1] - balance_series.iloc[0]\n", "    avg_return = returns.mean()\n", "    std_dev_annualised = returns.std() * np.sqrt(annual_factor)\n", "    std_dev = returns.std()\n", "    sharpe = (avg_return / std_dev) * np.sqrt(annual_factor) if std_dev != 0 else 0\n", "    downside_returns = np.minimum(0, returns)\n", "    sortino = (avg_return / downside_returns.std()) * np.sqrt(annual_factor) if downside_returns.std() != 0 else 0\n", "    running_max = np.maximum.accumulate(balance_series)\n", "    drawdown = balance_series - running_max\n", "    max_dd = drawdown.min()\n", "    calmar = total_return / abs(max_dd) if max_dd != 0 else np.nan\n", "    dd_end = drawdown.idxmin()\n", "    dd_start = balance_series[:dd_end].idxmax()\n", "    dd_duration = dd_end - dd_start\n", "    trade_count = int(df['trade'].sum() / 2)\n", "    fee_ratio = fees_paid / total_return if total_return != 0 else np.nan\n", "    dd_days = dd_duration * data_interval / 1440\n", "    dd_hours = (dd_duration * data_interval % 1440) // 60\n", "    dd_minutes = (dd_duration * data_interval) % 60\n", "    dd_duration_str = f\"{int(dd_days)}d {int(dd_hours)}h {int(dd_minutes)}m\"\n", "\n", "    # New metrics\n", "    half_life = -np.log(2) / np.log(phi) if phi < 1 else np.nan\n", "    median_holding = calculate_median_holding_period(df)\n", "\n", "    metrics = {\n", "        'Total Return': total_return,\n", "        'Avg Return per Interval': avg_return,\n", "        'Std Dev': std_dev_annualised,\n", "        'Sharpe Ratio': sharpe,\n", "        'So<PERSON><PERSON>': sortino,\n", "        '<PERSON><PERSON>': calmar,\n", "        'Max Drawdown (Abs)': max_dd,\n", "        'Max Drawdown Period': f\"{dd_duration} bars ({dd_duration_str})\",\n", "        'Trade Count': trade_count,\n", "        'Trading Fees': trading_fee_per_leg,\n", "        'Fees / PnL Ratio': fee_ratio,\n", "        'Half-life (bars)': half_life,\n", "        'Hurst Exponent': hurst,\n", "        'Median Holding Period (bars)': median_holding\n", "    }\n", "    return metrics\n", "\n", "def plot_equity_curve(df, asset1, asset2):\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(df['time'], df['equity_curve'], label='Equity Curve')\n", "    plt.title(f'Pair Trading Strategy Equity Curve ({asset1}-{asset2})')\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Cumulative Return (Spread Units)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# === Example Execution ===\n", "data_interval_str = '15m'  # Can be '15m', '30m', '1h', '3h', '1d'\n", "date_range = '20230101_20241231'\n", "asset1 = 'FIL'\n", "asset2 = 'SAND'\n", "data_path = '/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", "\n", "# === Configurable Parameters ===\n", "trading_fee_per_leg = 0.0004\n", "upper_entry_threshold = 1\n", "lower_entry_threshold = -1\n", "exit_threshold = 0\n", "\n", "# === Pipeline ===\n", "raw_df = load_data(asset1, asset2, data_path)\n", "df, hedge_ratio = compute_spread(raw_df, asset1, asset2)\n", "mu, sigma, phi = fit_ou_model(df)\n", "df = apply_trade_logic(df, mu, sigma)\n", "df = calculate_returns(df)\n", "hurst = calculate_hurst(df['spread'].dropna())\n", "metrics = evaluate_performance(df, data_interval_str, phi, hurst)\n", "\n", "# === Output ===\n", "print(\"=== Backtest Report ===\")\n", "print(f\"Pair: {asset1} - {asset2}\")\n", "print(f\"Hurst Exponent of Spread: {hurst:.4f}\")\n", "print(\"Performance Metrics:\")\n", "for k, v in metrics.items():\n", "    print(f\"{k:<30}: {v * 100:.4f}%\" if k in [\n", "        'Total Return', 'Avg Return per Interval', 'Std Dev', 'Trading Fees','Max Drawdown (Abs)','Fees / PnL Ratio'] else \\\n", "        f\"{k:<30}: {v:.4f}\" if isinstance(v, float) else f\"{k:<30}: {v}\")\n", "\n", "plot_equity_curve(df, asset1, asset2)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Walk-forward Summary (Sorted by <PERSON>):\n", "                  Start                 End  Total Return  Sharpe <PERSON>  \\\n", "66  2023-06-29 04:00:00 2023-07-02 06:45:00      0.080830     22.848908   \n", "260 2024-11-15 09:00:00 2024-11-18 11:45:00      0.193236     20.310208   \n", "26  2023-03-17 00:00:00 2023-03-20 02:45:00      0.126866     19.085253   \n", "4   2023-01-18 17:00:00 2023-01-21 19:45:00      0.129553     19.069276   \n", "164 2024-03-10 09:00:00 2024-03-13 11:45:00      0.120880     16.797851   \n", "..                  ...                 ...           ...           ...   \n", "208 2024-07-02 23:00:00 2024-07-06 01:45:00     -0.110733    -17.016967   \n", "199 2024-06-09 12:30:00 2024-06-12 15:15:00     -0.091087    -18.924327   \n", "221 2024-08-05 19:30:00 2024-08-08 22:15:00     -0.097495    -20.691129   \n", "14  2023-02-13 18:00:00 2023-02-16 20:45:00     -0.136357    -21.794694   \n", "15  2023-02-16 08:30:00 2023-02-19 11:15:00     -0.540496    -32.863520   \n", "\n", "     Calmar Ratio  Optimized Upper  Optimized Lower  Optimized Exit  \n", "66       6.839689              2.1             -2.9             0.6  \n", "260      3.781041              2.9             -2.1             0.6  \n", "26       3.248480              2.7             -2.3             0.6  \n", "4        3.909130              2.9             -2.5             0.6  \n", "164      2.062555              1.5             -1.7             0.6  \n", "..            ...              ...              ...             ...  \n", "208     -0.922285              2.5             -1.5             0.6  \n", "199     -0.654697              2.1             -2.3             0.6  \n", "221     -0.810190              1.9             -2.3             0.6  \n", "14      -0.943809              1.5             -2.9             0.6  \n", "15      -0.990586              2.7             -1.9             0.6  \n", "\n", "[277 rows x 8 columns]\n", "\n", "🚀 Running full-sample backtest with optimized parameters...\n", "\n", "🧠 Optimized Parameters (Full Data): Upper=2.3, Lower=-2.5, Exit=0.6000000000000001\n", "\n", "=== Optimized Parameters (Full Sample) ===\n", "Upper Entry Threshold              : 2.3\n", "Lower Entry Threshold              : -2.5\n", "Exit Threshold                     : 0.6000000000000001\n", "\n", "=== Full-Sample Backtest Report ===\n", "Total Return                       : 195.2788%\n", "Avg Return per Interval            : 0.0028%\n", "Std Dev                            : 71.1844%\n", "Sharpe Ratio                       : 1.3732\n", "<PERSON><PERSON><PERSON> Ratio                      : 2.1383\n", "Calmar Ratio                       : 3.2414\n", "Max Drawdown (Abs)                 : -60.2460%\n", "Max Drawdown Period                : 3373 bars (35d 3h 15m)\n", "Trade Count                        : 508.0000\n", "Trading Fees                       : 0.0400%\n", "Fees / PnL Ratio                   : 41.6225%\n", "Half-life (bars)                   : 2430.1155\n", "Hurst Exponent                     : nan\n", "Median Holding Period (bars)       : 3.0000\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Plateau Detection Optimisation\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "from itertools import product\n", "from scipy.ndimage import uniform_filter\n", "\n", "# === Helper Constants ===\n", "data_interval_str = '15m'\n", "interval_map = {'15m': 15, '30m': 30, '1h': 60, '3h': 180, '1d': 1440}\n", "\n", "# === Core Functions ===\n", "def load_data(asset1, asset2, path):\n", "    df1 = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df2 = pd.read_csv(f\"{path}/{asset2}/bybit_{asset2.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df1['time'] = pd.to_datetime(df1['time'])\n", "    df2['time'] = pd.to_datetime(df2['time'])\n", "    prices1 = df1[['time', 'close']].rename(columns={'close': asset1})\n", "    prices2 = df2[['time', 'close']].rename(columns={'close': asset2})\n", "    df = pd.merge(prices1, prices2, on='time', how='inner').sort_values('time').reset_index(drop=True)\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "def compute_spread(df, asset1, asset2):\n", "    df['log_' + asset1] = np.log(df[asset1])\n", "    df['log_' + asset2] = np.log(df[asset2])\n", "    johansen = coint_johansen(df[[f'log_{asset1}', f'log_{asset2}']], 0, 1)\n", "    beta = johansen.evec[:, 0]\n", "    hedge_ratio = -beta[1] / beta[0]\n", "    df['spread'] = df[f'log_{asset1}'] - hedge_ratio * df[f'log_{asset2}']\n", "    return df, hedge_ratio\n", "\n", "def fit_ou_model(df):\n", "    df['spread_lag'] = df['spread'].shift(1)\n", "    ou_data = df.dropna(subset=['spread', 'spread_lag'])\n", "    model = sm.OLS(ou_data['spread'], sm.add_constant(ou_data['spread_lag'])).fit()\n", "    phi = model.params[1]\n", "    mu = model.params[0] / (1 - phi)\n", "    sigma = np.std(model.resid)\n", "    return mu, sigma, phi\n", "\n", "def apply_trade_logic(df, mu, sigma, upper_entry_threshold, lower_entry_threshold, exit_threshold):\n", "    df['z_score'] = (df['spread'] - mu) / sigma\n", "    df['position'] = 0\n", "    df.loc[df['z_score'] > upper_entry_threshold, 'position'] = -1\n", "    df.loc[df['z_score'] < lower_entry_threshold, 'position'] = 1\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    exit_signals = ((df['position'] == 1) & (df['z_score'] >= exit_threshold)) | \\\n", "                   ((df['position'] == -1) & (df['z_score'] <= -exit_threshold))\n", "    df.loc[exit_signals, 'position'] = 0\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    return df\n", "\n", "def calculate_returns(df):\n", "    df['spread_return'] = df['spread'].diff()\n", "    df['raw_return'] = df['position'].shift(1) * df['spread_return']\n", "    df['trade'] = df['position'].diff().abs()\n", "    df['fee'] = df['trade'] * 2 * trading_fee_per_leg\n", "    df['strategy_return'] = df['raw_return'] - df['fee']\n", "    df['equity_curve'] = df['strategy_return'].cumsum()\n", "    return df\n", "\n", "def calculate_hurst(spread, max_lag=80, min_valid_points=10):\n", "    lags = range(2, max_lag)\n", "    tau = []\n", "\n", "    for lag in lags:\n", "        if len(spread) - lag > min_valid_points:\n", "            diff = spread[lag:] - spread[:-lag]\n", "            std = np.std(diff)\n", "            if std > 0:\n", "                tau.append(std)\n", "            else:\n", "                tau.append(np.nan)\n", "        else:\n", "            tau.append(np.nan)\n", "\n", "    tau = np.array(tau)\n", "    valid = ~np.isnan(tau) & (tau > 0)\n", "\n", "    if valid.sum() < 2:\n", "        return np.nan\n", "\n", "    log_lags = np.log(np.array(list(lags))[valid])\n", "    log_tau = np.log(tau[valid])\n", "\n", "    poly = np.polyfit(log_lags, log_tau, 1)\n", "    return poly[0]\n", "\n", "def calculate_median_holding_period(df):\n", "    df['position_change'] = df['position'].diff()\n", "    trade_starts = df[df['position_change'] != 0].index\n", "    holding_periods = []\n", "    for i in range(0, len(trade_starts) - 1, 2):\n", "        entry = trade_starts[i]\n", "        exit = trade_starts[i + 1] if i + 1 < len(trade_starts) else None\n", "        if exit:\n", "            holding_periods.append(exit - entry)\n", "    return np.median(holding_periods) if holding_periods else np.nan\n", "\n", "def evaluate_performance(df, data_interval_str='1h', phi=None, hurst=None):\n", "    returns = df['strategy_return'].dropna()\n", "    balance_series = df['equity_curve'].dropna()\n", "    fees_paid = df['fee'].sum()\n", "    data_interval = interval_map.get(data_interval_str, 60)\n", "    annual_factor = 365 * 24 * 60 / data_interval\n", "    total_return = balance_series.iloc[-1] - balance_series.iloc[0]\n", "    avg_return = returns.mean()\n", "    std_dev_annualised = returns.std() * np.sqrt(annual_factor)\n", "    std_dev = returns.std()\n", "    sharpe = (avg_return / std_dev) * np.sqrt(annual_factor) if std_dev != 0 else 0\n", "    downside_returns = np.minimum(0, returns)\n", "    sortino = (avg_return / downside_returns.std()) * np.sqrt(annual_factor) if downside_returns.std() != 0 else 0\n", "    running_max = np.maximum.accumulate(balance_series)\n", "    drawdown = balance_series - running_max\n", "    max_dd = drawdown.min()\n", "    calmar = total_return / abs(max_dd) if max_dd != 0 else np.nan\n", "    dd_end = drawdown.idxmin()\n", "    dd_start = balance_series[:dd_end].idxmax()\n", "    dd_duration = dd_end - dd_start\n", "    trade_count = int(df['trade'].sum() / 2)\n", "    fee_ratio = fees_paid / total_return if total_return != 0 else np.nan\n", "    dd_days = dd_duration * data_interval / 1440\n", "    dd_hours = (dd_duration * data_interval % 1440) // 60\n", "    dd_minutes = (dd_duration * data_interval) % 60\n", "    dd_duration_str = f\"{int(dd_days)}d {int(dd_hours)}h {int(dd_minutes)}m\"\n", "    half_life = -np.log(2) / np.log(phi) if phi and phi < 1 else np.nan\n", "    median_holding = calculate_median_holding_period(df)\n", "\n", "    return {\n", "        'Total Return': total_return,\n", "        'Avg Return per Interval': avg_return,\n", "        'Std Dev': std_dev_annualised,\n", "        'Sharpe Ratio': sharpe,\n", "        'So<PERSON><PERSON>': sortino,\n", "        '<PERSON><PERSON>': calmar,\n", "        'Max Drawdown (Abs)': max_dd,\n", "        'Max Drawdown Period': f\"{dd_duration} bars ({dd_duration_str})\",\n", "        'Trade Count': trade_count,\n", "        'Trading Fees': trading_fee_per_leg,\n", "        'Fees / PnL Ratio': fee_ratio,\n", "        'Half-life (bars)': half_life,\n", "        'Hurst Exponent': hurst,\n", "        'Median Holding Period (bars)': median_holding\n", "    }\n", "\n", "def optimize_parameters(train_df, mu, sigma, highland_radius=5, threshold_ratio=0.95):\n", "    upper_range = np.arange(1.5, 3.1, 0.2)\n", "    lower_range = -np.flip(upper_range)\n", "    exit_range  = np.arange(0.0, 1.6, 0.2)\n", "\n", "    grid_size = len(upper_range)\n", "    sharpe_matrix = np.zeros((grid_size, grid_size, len(exit_range)))\n", "\n", "    for u_idx, u in enumerate(upper_range):\n", "        for l_idx, l in enumerate(lower_range):\n", "            for e_idx, e in enumerate(exit_range):\n", "                temp_df = apply_trade_logic(train_df.copy(), mu, sigma, u, l, e)\n", "                temp_df = calculate_returns(temp_df)\n", "                returns = temp_df['strategy_return'].dropna()\n", "                if returns.std() != 0:\n", "                    sharpe = (returns.mean() / returns.std()) * np.sqrt(365 * 24 * 60 / interval_map[data_interval_str])\n", "                else:\n", "                    sharpe = 0\n", "                sharpe_matrix[u_idx, l_idx, e_idx] = sharpe\n", "\n", "    smoothed = uniform_filter(sharpe_matrix, size=highland_radius, mode='nearest')\n", "    max_sharpe = np.max(smoothed)\n", "    threshold_value = threshold_ratio * max_sharpe\n", "    highland_mask = smoothed >= threshold_value\n", "    highland_indices = np.argwhere(highland_mask)\n", "\n", "    if len(highland_indices) == 0:\n", "        centroid_idx = np.unravel_index(np.argmax(smoothed), smoothed.shape)\n", "    else:\n", "        centroid_idx = highland_indices.mean(axis=0).astype(int)\n", "\n", "    best_upper = upper_range[centroid_idx[0]]\n", "    best_lower = lower_range[centroid_idx[1]]\n", "    best_exit  = exit_range[centroid_idx[2]]\n", "\n", "    return best_upper, best_lower, best_exit\n", "\n", "def walk_forward_backtest(df, asset1, asset2, training_size=0.7, window_size=1000, step_size=250):\n", "    walk_metrics = []\n", "    total_bars = len(df)\n", "\n", "    for start in range(0, total_bars - window_size, step_size):\n", "        train_end = start + int(window_size * training_size)\n", "        test_end = start + window_size\n", "\n", "        train_df = df.iloc[start:train_end].copy()\n", "        test_df = df.iloc[train_end:test_end].copy()\n", "\n", "        if len(train_df) < 100 or len(test_df) < 50:\n", "            continue\n", "\n", "        mu, sigma, phi = fit_ou_model(train_df)\n", "        best_upper, best_lower, best_exit = optimize_parameters(train_df, mu, sigma)\n", "\n", "        test_df['spread'] = df['spread'].iloc[train_end:test_end].values\n", "        test_df = apply_trade_logic(test_df, mu, sigma, best_upper, best_lower, best_exit)\n", "        test_df = calculate_returns(test_df)\n", "\n", "        hurst = calculate_hurst(test_df['spread'].dropna())\n", "        metrics = evaluate_performance(test_df, data_interval_str, phi, hurst)\n", "        metrics.update({\n", "            'Start': df['time'].iloc[train_end],\n", "            'End': df['time'].iloc[test_end - 1],\n", "            'Optimized Upper': best_upper,\n", "            'Optimized Lower': best_lower,\n", "            'Optimized Exit': best_exit\n", "        })\n", "\n", "        walk_metrics.append(metrics)\n", "\n", "    return pd.DataFrame(walk_metrics)\n", "\n", "# === Equity Plot Function ===\n", "def plot_equity_curve(df, title=\"Equity Curve\"):\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(df['time'], df['equity_curve'], label='Equity Curve', linewidth=2)\n", "    plt.title(title)\n", "    plt.xlabel(\"Time\")\n", "    plt.ylabel(\"Cumulative Return\")\n", "    plt.grid(True, linestyle='--', alpha=0.5)\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# === Config ===\n", "asset1 = 'FIL'\n", "asset2 = 'SAND'\n", "date_range = '20230101_20241231'\n", "data_path = '/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", "trading_fee_per_leg = 0.0004\n", "\n", "# === Execution ===\n", "raw_df = load_data(asset1, asset2, data_path)\n", "df, hedge_ratio = compute_spread(raw_df, asset1, asset2)\n", "walk_metrics_df = walk_forward_backtest(df, asset1, asset2)\n", "\n", "# Show walk-forward sorted performance summary\n", "sorted_df = walk_metrics_df.sort_values(by='<PERSON>', ascending=False)\n", "print(\"\\n🔍 Walk-forward Summary (Sorted by <PERSON>):\")\n", "print(sorted_df[['Start', 'End', 'Total Return', 'Sharpe Ratio', 'Calmar Ratio', 'Optimized Upper', 'Optimized Lower', 'Optimized Exit']])\n", "\n", "# === Generate Full-Sample Backtest & Plot ===\n", "print(\"\\n🚀 Running full-sample backtest with optimized parameters...\")\n", "mu, sigma, phi = fit_ou_model(df)\n", "best_upper, best_lower, best_exit = optimize_parameters(df, mu, sigma)\n", "print(f\"\\n🧠 Optimized Parameters (Full Data): Upper={best_upper}, Lower={best_lower}, Exit={best_exit}\")\n", "\n", "# Apply strategy using full dataset\n", "df = apply_trade_logic(df, mu, sigma, best_upper, best_lower, best_exit)\n", "df = calculate_returns(df)\n", "\n", "# Evaluate and display full-sample performance\n", "hurst = calculate_hurst(df['spread'].dropna())\n", "full_metrics = evaluate_performance(df, data_interval_str, phi, hurst)\n", "\n", "# === Optimized Parameters Header ===\n", "print(\"\\n=== Optimized Parameters (Full Sample) ===\")\n", "optimized_params = {\n", "    \"Upper Entry Threshold\": best_upper,\n", "    \"Lower Entry Threshold\": best_lower,\n", "    \"Exit Threshold\": best_exit,\n", "}\n", "\n", "for k, v in optimized_params.items():\n", "    print(f\"{k:<35}: {v}\")\n", "\n", "# === Performance Metrics Report ===\n", "print(\"\\n=== Full-Sample Backtest Report ===\")\n", "percent_keys = {\n", "    'Total Return',\n", "    'Avg Return per Interval',\n", "    'Std Dev',\n", "    'Trading Fees',\n", "    'Max Drawdown (Abs)',\n", "    'Fees / PnL Ratio'\n", "}\n", "\n", "for k, v in full_metrics.items():\n", "    if isinstance(v, (int, float)):\n", "        if k in percent_keys:\n", "            print(f\"{k:<35}: {v * 100:.4f}%\")\n", "        else:\n", "            print(f\"{k:<35}: {v:.4f}\")\n", "    else:\n", "        print(f\"{k:<35}: {v}\")\n", "\n", "\n", "# print(\"\\n📊 Full-Sample Performance Metrics:\")\n", "# for k, v in full_metrics.items():\n", "#     print(f\"{k}: {v}\")\n", "\n", "# Plot equity curve with parameter info in the title\n", "plot_title = f\"Equity Curve: {asset1}/{asset2} | U={best_upper}, L={best_lower}, E={best_exit}\"\n", "plot_equity_curve(df, title=plot_title)\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["sorted_df.to_csv(\"optimised.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
#!/usr/bin/env python3
"""
簡化版Taker Intensity + 均線策略測試
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

async def test_simple_strategy():
    """簡單測試單一幣種"""
    
    # Blave API配置
    base_url = "https://api.blave.org/"
    api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
    secret_key = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
    headers = {
        "api-key": api_key,
        "secret-key": secret_key
    }
    
    symbol = "BTCUSDT"
    timeframe = "1h"
    days = 30
    
    print(f"🚀 測試 {symbol} {timeframe} Taker Intensity + 均線策略")
    
    # 1. 獲取Taker Intensity數據
    try:
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)
        
        url = f"{base_url}taker_intensity/get_alpha"
        params = {
            "symbol": symbol,
            "period": timeframe,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "timeframe": "24h"
        }
        
        print(f"📊 獲取 {symbol} Taker Intensity數據...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params, timeout=60) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'data' in data and 'alpha' in data['data']:
                        ti_df = pd.DataFrame({
                            'timestamp': pd.to_datetime(data['data']['timestamp'], unit='s'),
                            'taker_intensity': data['data']['alpha']
                        })
                        ti_df.set_index('timestamp', inplace=True)
                        print(f"✅ TI數據獲取成功: {len(ti_df)} 條記錄")
                        print(f"TI範圍: {ti_df['taker_intensity'].min():.3f} 到 {ti_df['taker_intensity'].max():.3f}")
                    else:
                        print(f"❌ TI數據格式錯誤: {data}")
                        return
                else:
                    print(f"❌ TI數據獲取失敗: {response.status}")
                    return
                    
    except Exception as e:
        print(f"❌ TI數據獲取異常: {e}")
        return
    
    # 2. 獲取K線數據
    try:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            'category': 'spot',
            'symbol': symbol,
            'interval': timeframe,
            'limit': days * 24  # 1小時數據
        }
        
        print(f"📈 獲取 {symbol} K線數據...")
        
        response = requests.get(url, params=params, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data['retCode'] == 0:
                klines = data['result']['list']
                
                kline_df = pd.DataFrame(klines, columns=[
                    'timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'Turnover'
                ])
                
                kline_df['timestamp'] = pd.to_datetime(kline_df['timestamp'].astype(int), unit='ms')
                for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                    kline_df[col] = kline_df[col].astype(float)
                
                kline_df.set_index('timestamp', inplace=True)
                kline_df.sort_index(inplace=True)
                
                print(f"✅ K線數據獲取成功: {len(kline_df)} 條記錄")
                print(f"價格範圍: ${kline_df['Close'].min():.2f} 到 ${kline_df['Close'].max():.2f}")
            else:
                print(f"❌ K線數據API錯誤: {data}")
                return
        else:
            print(f"❌ K線數據請求失敗: {response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ K線數據獲取異常: {e}")
        return
    
    # 3. 合併數據
    print(f"🔗 合併數據...")
    combined_df = pd.merge(kline_df, ti_df, left_index=True, right_index=True, how='inner')
    print(f"✅ 合併後數據: {len(combined_df)} 條記錄")
    
    if len(combined_df) < 50:
        print(f"❌ 數據不足，無法進行測試")
        return
    
    # 4. 計算均線
    ma_periods = [10, 20]
    for period in ma_periods:
        combined_df[f'MA_{period}'] = combined_df['Close'].rolling(window=period).mean()
    
    print(f"✅ 均線計算完成")
    
    # 5. 生成簡單信號
    signals = []
    ti_threshold = 0.3
    
    for i in range(20, len(combined_df)):  # 跳過前20個數據點
        price = combined_df['Close'].iloc[i]
        prev_price = combined_df['Close'].iloc[i-1]
        ti = combined_df['taker_intensity'].iloc[i]
        ma10 = combined_df['MA_10'].iloc[i]
        ma20 = combined_df['MA_20'].iloc[i]
        prev_ma10 = combined_df['MA_10'].iloc[i-1]
        
        # 多頭信號：價格突破MA10 + TI > 閾值
        if (price > ma10 and prev_price <= prev_ma10 and ti > ti_threshold):
            signals.append({
                'timestamp': combined_df.index[i],
                'type': 'LONG',
                'price': price,
                'ti': ti,
                'ma10': ma10,
                'ma20': ma20
            })
        
        # 空頭信號：價格跌破MA10 + TI < -閾值
        elif (price < ma10 and prev_price >= prev_ma10 and ti < -ti_threshold):
            signals.append({
                'timestamp': combined_df.index[i],
                'type': 'SHORT',
                'price': price,
                'ti': ti,
                'ma10': ma10,
                'ma20': ma20
            })
    
    print(f"\n📊 信號統計:")
    print(f"總信號數: {len(signals)}")
    
    if signals:
        long_signals = [s for s in signals if s['type'] == 'LONG']
        short_signals = [s for s in signals if s['type'] == 'SHORT']
        
        print(f"多頭信號: {len(long_signals)}")
        print(f"空頭信號: {len(short_signals)}")
        
        print(f"\n最近5個信號:")
        for signal in signals[-5:]:
            print(f"  {signal['timestamp'].strftime('%Y-%m-%d %H:%M')} {signal['type']} ${signal['price']:.2f} TI:{signal['ti']:.3f}")
    
    # 6. 簡單回測
    if len(signals) >= 2:
        print(f"\n💰 簡單回測結果:")
        
        trades = []
        for i in range(0, len(signals)-1, 2):  # 每兩個信號配對
            entry = signals[i]
            exit_signal = signals[i+1] if i+1 < len(signals) else None
            
            if exit_signal:
                if entry['type'] == 'LONG':
                    pnl_pct = (exit_signal['price'] / entry['price'] - 1) * 100
                else:
                    pnl_pct = (entry['price'] / exit_signal['price'] - 1) * 100
                
                trades.append(pnl_pct)
                
                print(f"  {entry['type']} ${entry['price']:.2f} -> ${exit_signal['price']:.2f} = {pnl_pct:+.2f}%")
        
        if trades:
            total_return = sum(trades)
            avg_return = np.mean(trades)
            win_rate = len([t for t in trades if t > 0]) / len(trades) * 100
            
            print(f"\n📈 回測統計:")
            print(f"交易次數: {len(trades)}")
            print(f"總收益: {total_return:+.2f}%")
            print(f"平均收益: {avg_return:+.2f}%")
            print(f"勝率: {win_rate:.1f}%")
    
    print(f"\n✅ 測試完成！")

if __name__ == "__main__":
    asyncio.run(test_simple_strategy())

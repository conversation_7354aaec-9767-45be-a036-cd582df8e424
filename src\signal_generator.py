"""
信號生成器模組
生成和管理交易信號

作者: 專業量化策略工程師
"""

from typing import Dict, Any, Optional
from datetime import datetime
from .logger_setup import setup_logger

class SignalGenerator:
    """交易信號生成器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("SignalGenerator")
        
        # 當前持倉狀態
        self.positions = {
            'PEPE': {'status': 'NONE', 'entry_time': None, 'entry_price': None},
            'XRP': {'status': 'NONE', 'entry_time': None, 'entry_price': None},
            'SOL': {'status': 'NONE', 'entry_time': None, 'entry_price': None}
        }
    
    def generate_signal(self, indicators: Dict[str, Any], coin: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """生成交易信號"""
        try:
            self.logger.info(f"🎯 生成 {coin}-{timeframe} 交易信號")
            
            current_position = self.positions.get(coin, {}).get('status', 'NONE')
            current_price = indicators.get('current_price')
            timestamp = indicators.get('timestamp')
            
            # 檢查是否需要平倉
            if current_position in ['LONG', 'SHORT']:
                if self._should_close_position(indicators, current_position):
                    signal = self._create_close_signal(coin, current_position, current_price, timestamp, indicators)
                    self._update_position(coin, 'NONE', None, None)
                    return signal
            
            # 檢查是否有新的入場信號
            if current_position == 'NONE':
                entry_signal = self._get_entry_signal(indicators)
                if entry_signal:
                    self.logger.info(f"🚨 {coin} 生成 {entry_signal} 信號！")
                    signal = self._create_entry_signal(coin, entry_signal, current_price, timestamp, indicators)
                    self._update_position(coin, entry_signal, current_price, timestamp)
                    self.logger.info(f"✅ {coin} 信號創建完成: {signal}")
                    return signal
                else:
                    self.logger.debug(f"😴 {coin} 無入場信號")

            # 無信號
            self.logger.debug(f"😴 {coin} 當前無信號 (持倉: {current_position})")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ {coin} 信號生成失敗: {e}")
            return None
    
    def _get_entry_signal(self, indicators: Dict[str, Any]) -> Optional[str]:
        """獲取入場信號"""
        try:
            ccb = indicators.get('ccb', {})
            taker = indicators.get('taker_signals', {})

            ccb_position = ccb.get('position')
            long_signal = taker.get('long_signal', False)
            short_signal = taker.get('short_signal', False)

            # 記錄當前條件狀態
            self.logger.info(f"📊 信號條件檢查:")
            self.logger.info(f"   CCB位置: {ccb_position}")
            self.logger.info(f"   多方信號: {long_signal}")
            self.logger.info(f"   空方信號: {short_signal}")

            # 多頭信號：籌碼集中度跌破下軌 AND 多方力道突破閾值
            if ccb_position == 'BELOW_LOWER' and long_signal:
                self.logger.info(f"🚨 多頭信號條件滿足！")
                return 'LONG'

            # 空頭信號：籌碼集中度突破上軌 AND 空方力道突破閾值
            elif ccb_position == 'ABOVE_UPPER' and short_signal:
                self.logger.info(f"🚨 空頭信號條件滿足！")
                return 'SHORT'

            self.logger.info(f"😴 信號條件未滿足")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 入場信號判斷失敗: {e}")
            return None
    
    def _should_close_position(self, indicators: Dict[str, Any], current_position: str) -> bool:
        """判斷是否應該平倉"""
        try:
            ccb = indicators.get('ccb', {})
            taker = indicators.get('taker_signals', {})
            
            ccb_position = ccb.get('position')
            long_signal = taker.get('long_signal', False)
            short_signal = taker.get('short_signal', False)
            
            if current_position == 'LONG':
                # 多頭平倉條件：籌碼集中度回到中軌以上 OR 空方力道突破
                return ccb_position != 'BELOW_LOWER' or short_signal
            
            elif current_position == 'SHORT':
                # 空頭平倉條件：籌碼集中度回到中軌以下 OR 多方力道突破
                return ccb_position != 'ABOVE_UPPER' or long_signal
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 平倉判斷失敗: {e}")
            return False
    
    def _create_entry_signal(self, coin: str, direction: str, price: float, 
                           timestamp: datetime, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """創建入場信號"""
        ccb = indicators.get('ccb', {})
        taker = indicators.get('taker_signals', {})
        
        signal = {
            'coin': coin,
            'action': direction,
            'price': price,
            'timestamp': timestamp,
            'signal_type': 'ENTRY',
            'concentration': ccb.get('concentration'),
            'ccb_position': ccb.get('position'),
            'long_intensity': taker.get('long_intensity'),
            'short_intensity': taker.get('short_intensity'),
            'long_percentile': taker.get('long_percentile'),
            'short_percentile': taker.get('short_percentile'),
            'confidence': self._calculate_signal_confidence(indicators)
        }
        
        return signal
    
    def _create_close_signal(self, coin: str, position: str, price: float, 
                           timestamp: datetime, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """創建平倉信號"""
        ccb = indicators.get('ccb', {})
        taker = indicators.get('taker_signals', {})
        
        action = 'CLOSE_LONG' if position == 'LONG' else 'CLOSE_SHORT'
        
        signal = {
            'coin': coin,
            'action': action,
            'price': price,
            'timestamp': timestamp,
            'signal_type': 'EXIT',
            'concentration': ccb.get('concentration'),
            'ccb_position': ccb.get('position'),
            'long_intensity': taker.get('long_intensity'),
            'short_intensity': taker.get('short_intensity'),
            'long_percentile': taker.get('long_percentile'),
            'short_percentile': taker.get('short_percentile'),
            'confidence': self._calculate_signal_confidence(indicators)
        }
        
        return signal
    
    def _calculate_signal_confidence(self, indicators: Dict[str, Any]) -> float:
        """計算信號置信度"""
        try:
            ccb = indicators.get('ccb', {})
            taker = indicators.get('taker_signals', {})
            
            confidence = 0.5  # 基礎置信度
            
            # CCB位置加分
            ccb_position = ccb.get('position')
            if ccb_position in ['ABOVE_UPPER', 'BELOW_LOWER']:
                confidence += 0.2
            
            # Taker Intensity強度加分
            long_pct = taker.get('long_percentile', 50)
            short_pct = taker.get('short_percentile', 50)
            
            if long_pct > 80 or short_pct > 80:
                confidence += 0.2
            elif long_pct > 70 or short_pct > 70:
                confidence += 0.1
            
            # 信號一致性加分
            if ccb_position == 'BELOW_LOWER' and taker.get('long_signal', False):
                confidence += 0.1
            elif ccb_position == 'ABOVE_UPPER' and taker.get('short_signal', False):
                confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            self.logger.error(f"❌ 置信度計算失敗: {e}")
            return 0.5
    
    def _update_position(self, coin: str, status: str, price: Optional[float], timestamp: Optional[datetime]):
        """更新持倉狀態"""
        if coin in self.positions:
            self.positions[coin] = {
                'status': status,
                'entry_price': price,
                'entry_time': timestamp
            }
    
    def get_position_status(self, coin: str) -> Dict[str, Any]:
        """獲取持倉狀態"""
        return self.positions.get(coin, {'status': 'NONE', 'entry_time': None, 'entry_price': None})
    
    def get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """獲取所有持倉狀態"""
        return self.positions.copy()
    
    def reset_position(self, coin: str):
        """重置持倉狀態"""
        if coin in self.positions:
            self.positions[coin] = {'status': 'NONE', 'entry_time': None, 'entry_price': None}

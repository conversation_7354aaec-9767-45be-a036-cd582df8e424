{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Backtest Report ===\n", "Pair: FIL - SAND\n", "Hurst Exponent of Spread: nan\n", "Performance Metrics:\n", "Total Return                  : 167.8845%\n", "Avg Return per Interval       : 0.0024%\n", "Std Dev                       : 71.9693%\n", "Sharpe Ratio                  : 1.1679\n", "<PERSON><PERSON><PERSON>                 : 1.8239\n", "Calmar Ratio                  : 2.7655\n", "Max Drawdown (Abs)            : -60.7065%\n", "Max Drawdown Period           : 3461 bars (36d 1h 15m)\n", "Trade Count                   : 424\n", "Trading Fees                  : 0.0400%\n", "Fees / PnL Ratio              : 40.4087%\n", "Half-life (bars)              : 2430.1155\n", "Hurst Exponent                : nan\n", "Median Holding Period (bars)  : 3.0000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/rm/g81vx3697gd1lhp947mpdn2w0000gn/T/ipykernel_12695/3459026252.py:65: RuntimeWarning: divide by zero encountered in log\n", "  poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Normal Backtest\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "\n", "# === Core Functions ===\n", "\n", "def load_data(asset1, asset2, path):\n", "    df1 = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df2 = pd.read_csv(f\"{path}/{asset2}/bybit_{asset2.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df1['time'] = pd.to_datetime(df1['time'])\n", "    df2['time'] = pd.to_datetime(df2['time'])\n", "    prices1 = df1[['time', 'close']].rename(columns={'close': asset1})\n", "    prices2 = df2[['time', 'close']].rename(columns={'close': asset2})\n", "    df = pd.merge(prices1, prices2, on='time', how='inner').sort_values('time').reset_index(drop=True)\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "def compute_spread(df, asset1, asset2):\n", "    df['log_' + asset1] = np.log(df[asset1])\n", "    df['log_' + asset2] = np.log(df[asset2])\n", "    johansen = coint_johansen(df[[f'log_{asset1}', f'log_{asset2}']], 0, 1)\n", "    beta = johansen.evec[:, 0]\n", "    hedge_ratio = -beta[1] / beta[0]\n", "    df['spread'] = df[f'log_{asset1}'] - hedge_ratio * df[f'log_{asset2}']\n", "    return df, hedge_ratio\n", "\n", "def fit_ou_model(df):\n", "    df['spread_lag'] = df['spread'].shift(1)\n", "    ou_data = df.dropna(subset=['spread', 'spread_lag'])\n", "    model = sm.OLS(ou_data['spread'], sm.add_constant(ou_data['spread_lag'])).fit()\n", "    phi = model.params[1]\n", "    mu = model.params[0] / (1 - phi)\n", "    sigma = np.std(model.resid)\n", "    return mu, sigma, phi\n", "\n", "def apply_trade_logic(df, mu, sigma):\n", "    df['z_score'] = (df['spread'] - mu) / sigma\n", "    df['position'] = 0\n", "    df.loc[df['z_score'] > upper_entry_threshold, 'position'] = -1\n", "    df.loc[df['z_score'] < lower_entry_threshold, 'position'] = 1\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    exit_signals = ((df['position'] == 1) & (df['z_score'] >= exit_threshold)) | \\\n", "                   ((df['position'] == -1) & (df['z_score'] <= -exit_threshold))\n", "    df.loc[exit_signals, 'position'] = 0\n", "    df['position'] = df['position'].ffill().fillna(0)\n", "    return df\n", "\n", "def calculate_returns(df):\n", "    df['spread_return'] = df['spread'].diff()\n", "    df['raw_return'] = df['position'].shift(1) * df['spread_return']\n", "    df['trade'] = df['position'].diff().abs()\n", "    df['fee'] = df['trade'] * 2 * trading_fee_per_leg\n", "    df['strategy_return'] = df['raw_return'] - df['fee']\n", "    df['equity_curve'] = df['strategy_return'].cumsum()\n", "    return df\n", "\n", "def calculate_hurst(spread, max_lag=80, min_valid_points=10):\n", "    lags = range(2, max_lag)\n", "    tau = [np.std(spread[lag:] - spread[:-lag]) for lag in lags if len(spread) - lag > min_valid_points]\n", "    if len(tau) < 2:\n", "        return np.nan\n", "    poly = np.polyfit(np.log(lags[:len(tau)]), np.log(tau), 1)\n", "    return poly[0]\n", "\n", "def calculate_median_holding_period(df):\n", "    df['position_change'] = df['position'].diff()\n", "    trade_starts = df[df['position_change'] != 0].index\n", "    holding_periods = []\n", "    for i in range(0, len(trade_starts) - 1, 2):\n", "        entry = trade_starts[i]\n", "        exit = trade_starts[i + 1] if i + 1 < len(trade_starts) else None\n", "        if exit:\n", "            holding_periods.append(exit - entry)\n", "    return np.median(holding_periods) if holding_periods else np.nan\n", "\n", "def evaluate_performance(df, data_interval_str='1h', phi=None, hurst=None):\n", "    returns = df['strategy_return'].dropna()\n", "    balance_series = df['equity_curve'].dropna()\n", "    fees_paid = df['fee'].sum()\n", "    interval_map = {'15m': 15, '30m': 30, '1h': 60, '3h': 180, '1d': 1440}\n", "    data_interval = interval_map.get(data_interval_str, 60)\n", "    annual_factor = 365 * 24 * 60 / data_interval\n", "    total_return = balance_series.iloc[-1] - balance_series.iloc[0]\n", "    avg_return = returns.mean()\n", "    std_dev_annualised = returns.std() * np.sqrt(annual_factor)\n", "    std_dev = returns.std()\n", "    sharpe = (avg_return / std_dev) * np.sqrt(annual_factor) if std_dev != 0 else 0\n", "    downside_returns = np.minimum(0, returns)\n", "    sortino = (avg_return / downside_returns.std()) * np.sqrt(annual_factor) if downside_returns.std() != 0 else 0\n", "    running_max = np.maximum.accumulate(balance_series)\n", "    drawdown = balance_series - running_max\n", "    max_dd = drawdown.min()\n", "    calmar = total_return / abs(max_dd) if max_dd != 0 else np.nan\n", "    dd_end = drawdown.idxmin()\n", "    dd_start = balance_series[:dd_end].idxmax()\n", "    dd_duration = dd_end - dd_start\n", "    trade_count = int(df['trade'].sum() / 2)\n", "    fee_ratio = fees_paid / total_return if total_return != 0 else np.nan\n", "    dd_days = dd_duration * data_interval / 1440\n", "    dd_hours = (dd_duration * data_interval % 1440) // 60\n", "    dd_minutes = (dd_duration * data_interval) % 60\n", "    dd_duration_str = f\"{int(dd_days)}d {int(dd_hours)}h {int(dd_minutes)}m\"\n", "\n", "    # New metrics\n", "    half_life = -np.log(2) / np.log(phi) if phi < 1 else np.nan\n", "    median_holding = calculate_median_holding_period(df)\n", "\n", "    metrics = {\n", "        'Total Return': total_return,\n", "        'Avg Return per Interval': avg_return,\n", "        'Std Dev': std_dev_annualised,\n", "        'Sharpe Ratio': sharpe,\n", "        'So<PERSON><PERSON>': sortino,\n", "        '<PERSON><PERSON>': calmar,\n", "        'Max Drawdown (Abs)': max_dd,\n", "        'Max Drawdown Period': f\"{dd_duration} bars ({dd_duration_str})\",\n", "        'Trade Count': trade_count,\n", "        'Trading Fees': trading_fee_per_leg,\n", "        'Fees / PnL Ratio': fee_ratio,\n", "        'Half-life (bars)': half_life,\n", "        'Hurst Exponent': hurst,\n", "        'Median Holding Period (bars)': median_holding\n", "    }\n", "    return metrics\n", "\n", "def plot_equity_curve(df, asset1, asset2):\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(df['time'], df['equity_curve'], label='Equity Curve')\n", "    plt.title(f'Pair Trading Strategy Equity Curve ({asset1}-{asset2})')\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Cumulative Return (Spread Units)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# === Example Execution ===\n", "data_interval_str = '15m'  # Can be '15m', '30m', '1h', '3h', '1d'\n", "date_range = '20230101_20241231'\n", "asset1 = 'FIL'\n", "asset2 = 'SAND'\n", "data_path = '/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", "\n", "# === Configurable Parameters ===\n", "trading_fee_per_leg = 0.0004\n", "upper_entry_threshold = 1\n", "lower_entry_threshold = -1\n", "exit_threshold = 0\n", "\n", "# === Pipeline ===\n", "raw_df = load_data(asset1, asset2, data_path)\n", "df, hedge_ratio = compute_spread(raw_df, asset1, asset2)\n", "mu, sigma, phi = fit_ou_model(df)\n", "df = apply_trade_logic(df, mu, sigma)\n", "df = calculate_returns(df)\n", "hurst = calculate_hurst(df['spread'].dropna())\n", "metrics = evaluate_performance(df, data_interval_str, phi, hurst)\n", "\n", "# === Output ===\n", "print(\"=== Backtest Report ===\")\n", "print(f\"Pair: {asset1} - {asset2}\")\n", "print(f\"Hurst Exponent of Spread: {hurst:.4f}\")\n", "print(\"Performance Metrics:\")\n", "for k, v in metrics.items():\n", "    print(f\"{k:<30}: {v * 100:.4f}%\" if k in [\n", "        'Total Return', 'Avg Return per Interval', 'Std Dev', 'Trading Fees','Max Drawdown (Abs)','Fees / PnL Ratio'] else \\\n", "        f\"{k:<30}: {v:.4f}\" if isinstance(v, float) else f\"{k:<30}: {v}\")\n", "\n", "plot_equity_curve(df, asset1, asset2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
#!/usr/bin/env python3
"""
測試修復效果
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from src.portfolio_manager import PortfolioManager
from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator

async def test_fixes():
    """測試修復效果"""
    print("🔧 測試修復效果...")
    
    # 初始化組件
    from config.config import Config
    config = Config()

    portfolio_manager = PortfolioManager(config)
    data_fetcher = DataFetcher(config)
    strategy_engine = StrategyEngine(config)
    signal_generator = SignalGenerator(config)
    
    # 測試1: 檢查交易記錄
    print("\n📊 測試1: 檢查交易記錄")
    trades_df = portfolio_manager.trades_df
    print(f"交易記錄數量: {len(trades_df)}")
    
    if not trades_df.empty:
        print("最近5筆交易:")
        recent_trades = trades_df.tail(5)
        for _, trade in recent_trades.iterrows():
            print(f"  {trade['coin']} {trade['direction']} {trade['pnl_pct']:+.2f}% ({trade['exit_time']})")
    
    # 測試2: 生成每日報告
    print("\n📊 測試2: 生成每日報告")
    daily_report = portfolio_manager.generate_daily_report()
    print(daily_report)
    
    # 測試3: 模擬信號處理
    print("\n📊 測試3: 模擬信號處理")
    
    # 獲取XRP數據
    try:
        data = await data_fetcher.fetch_data('XRPUSDT', '1h', 200)
        indicators = strategy_engine.calculate_indicators(data, '1H')
        
        print(f"當前價格: ${data['Close'].iloc[-1]:.6f}")
        print(f"CCB位置: {indicators['ccb']['position']}")
        print(f"多方信號: {indicators['taker_signals']['long_signal']}")
        print(f"空方信號: {indicators['taker_signals']['short_signal']}")
        
        # 測試信號生成
        signal = signal_generator.generate_signal(indicators, 'XRP', '1H')
        if signal:
            print(f"生成信號: {signal}")
        else:
            print("當前無信號")
            
    except Exception as e:
        print(f"❌ 信號測試失敗: {e}")
    
    print("\n✅ 測試完成")

if __name__ == "__main__":
    asyncio.run(test_fixes())

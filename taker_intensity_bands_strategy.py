"""
多方力道帶突破策略 + 空方力道帶突破策略
使用多方力道和空方力道各自的值構成新的指標和策略
參考布林帶設計方式，使用完整歷史數據回測

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_market_data():
    """獲取市場數據"""
    print("正在生成市場數據...")
    
    # 生成2年以上的日線數據
    dates = pd.date_range(start='2022-01-01', end='2024-12-31', freq='D')
    n = len(dates)
    
    np.random.seed(42)
    
    # 生成BTC價格數據
    price_base = 30000
    price_trend = np.cumsum(np.random.normal(0, 0.02, n))  # 隨機遊走
    prices = price_base * np.exp(price_trend)
    
    # 確保價格合理
    prices = np.maximum(prices, 10000)
    
    # 計算價格變化
    returns = np.diff(prices) / prices[:-1]
    returns = np.concatenate([[0], returns])
    
    # 生成多方力道數據
    long_intensity = []
    for i in range(n):
        if i == 0:
            long_intensity.append(50)
            continue
        
        # 多方力道與價格上漲相關，但有滯後性
        price_momentum = returns[max(0, i-3):i+1].mean() if i >= 3 else returns[i]
        base_intensity = 50 + price_momentum * 300
        
        # 加入趨勢和噪音
        trend_component = np.sin(i * 0.01) * 10  # 週期性成分
        noise = np.random.normal(0, 8)
        
        intensity = base_intensity + trend_component + noise
        intensity = np.clip(intensity, 0, 100)
        long_intensity.append(intensity)
    
    # 生成空方力道數據
    short_intensity = []
    for i in range(n):
        if i == 0:
            short_intensity.append(50)
            continue
        
        # 空方力道與價格下跌相關
        price_momentum = returns[max(0, i-3):i+1].mean() if i >= 3 else returns[i]
        base_intensity = 50 - price_momentum * 300
        
        # 加入趨勢和噪音
        trend_component = np.cos(i * 0.01) * 10  # 週期性成分
        noise = np.random.normal(0, 8)
        
        intensity = base_intensity + trend_component + noise
        intensity = np.clip(intensity, 0, 100)
        short_intensity.append(intensity)
    
    df = pd.DataFrame({
        'date': dates,
        'close': prices,
        'returns': returns,
        'long_intensity': long_intensity,
        'short_intensity': short_intensity
    })
    
    print(f"市場數據生成完成：{len(df)}條記錄")
    print(f"時間範圍：{df['date'].min()} 至 {df['date'].max()}")
    
    return df

def long_intensity_bands(df, window=20, std_dev=2):
    """計算多方力道帶"""
    df['Long_Middle'] = df['long_intensity'].rolling(window=window).mean()
    df['Long_Upper'] = df['Long_Middle'] + (df['long_intensity'].rolling(window=window).std() * std_dev)
    df['Long_Lower'] = df['Long_Middle'] - (df['long_intensity'].rolling(window=window).std() * std_dev)
    
    return df

def short_intensity_bands(df, window=20, std_dev=2):
    """計算空方力道帶"""
    df['Short_Middle'] = df['short_intensity'].rolling(window=window).mean()
    df['Short_Upper'] = df['Short_Middle'] + (df['short_intensity'].rolling(window=window).std() * std_dev)
    df['Short_Lower'] = df['Short_Middle'] - (df['short_intensity'].rolling(window=window).std() * std_dev)
    
    return df

def intensity_bands_strategy(df, long_window=20, long_std=2, short_window=20, short_std=2, 
                           profit_ratio=2, stop_loss_atr_mult=1.5):
    """
    多方力道帶 + 空方力道帶突破策略
    
    參數:
    - long_window, long_std: 多方力道帶參數
    - short_window, short_std: 空方力道帶參數
    - profit_ratio: 盈虧比
    - stop_loss_atr_mult: 止損ATR倍數
    """
    
    # 計算力道帶
    df = long_intensity_bands(df, window=long_window, std_dev=long_std)
    df = short_intensity_bands(df, window=short_window, std_dev=short_std)
    
    # 計算ATR用於止損
    df['high'] = df['close'] * (1 + np.abs(df['returns']))
    df['low'] = df['close'] * (1 - np.abs(df['returns']))
    df['tr'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            np.abs(df['high'] - df['close'].shift(1)),
            np.abs(df['low'] - df['close'].shift(1))
        )
    )
    df['atr'] = df['tr'].rolling(window=14).mean()
    
    # 初始化信號
    df['Signal'] = 0
    df['Entry_Price'] = np.nan
    df['Stop_Loss'] = np.nan
    df['Take_Profit'] = np.nan
    
    position = 0  # 0: 無倉位, 1: 多頭, -1: 空頭
    entry_price = 0
    stop_loss = 0
    take_profit = 0
    
    for i in range(1, len(df)):
        current_price = df.iloc[i]['close']
        long_intensity = df.iloc[i]['long_intensity']
        short_intensity = df.iloc[i]['short_intensity']
        atr = df.iloc[i]['atr']
        
        # 檢查止損止盈
        if position != 0:
            if position == 1:  # 多頭倉位
                if current_price <= stop_loss or current_price >= take_profit:
                    position = 0
                    df.iloc[i, df.columns.get_loc('Signal')] = 0
                else:
                    df.iloc[i, df.columns.get_loc('Signal')] = 1
            elif position == -1:  # 空頭倉位
                if current_price >= stop_loss or current_price <= take_profit:
                    position = 0
                    df.iloc[i, df.columns.get_loc('Signal')] = 0
                else:
                    df.iloc[i, df.columns.get_loc('Signal')] = -1
        
        # 新的入場信號
        if position == 0 and not pd.isna(atr):
            # 多頭信號：多方力道突破上軌 且 空方力道低於下軌
            long_breakout = long_intensity > df.iloc[i]['Long_Upper']
            short_weak = short_intensity < df.iloc[i]['Short_Lower']
            
            # 空頭信號：空方力道突破上軌 且 多方力道低於下軌
            short_breakout = short_intensity > df.iloc[i]['Short_Upper']
            long_weak = long_intensity < df.iloc[i]['Long_Lower']
            
            if long_breakout and short_weak:
                # 開多頭
                position = 1
                entry_price = current_price
                stop_loss = entry_price - (atr * stop_loss_atr_mult)
                take_profit = entry_price + (atr * stop_loss_atr_mult * profit_ratio)
                
                df.iloc[i, df.columns.get_loc('Signal')] = 1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                
            elif short_breakout and long_weak:
                # 開空頭
                position = -1
                entry_price = current_price
                stop_loss = entry_price + (atr * stop_loss_atr_mult)
                take_profit = entry_price - (atr * stop_loss_atr_mult * profit_ratio)
                
                df.iloc[i, df.columns.get_loc('Signal')] = -1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
    
    return df

def optimize_intensity_bands_parameters(df):
    """參數優化"""
    print("開始參數優化...")
    
    best_sr = -np.inf
    best_params = {}
    
    # 參數範圍
    window_range = range(10, 41, 5)
    std_range = np.arange(1.5, 3.1, 0.3)
    profit_ratio_range = [1.5, 2.0, 2.5, 3.0]
    stop_loss_range = [1.0, 1.5, 2.0]
    
    total_combinations = len(window_range) ** 2 * len(std_range) ** 2 * len(profit_ratio_range) * len(stop_loss_range)
    current_combination = 0
    
    for long_window in window_range:
        for short_window in window_range:
            for long_std in std_range:
                for short_std in std_range:
                    for profit_ratio in profit_ratio_range:
                        for stop_loss_mult in stop_loss_range:
                            current_combination += 1
                            
                            try:
                                # 運行策略
                                df_temp = df.copy()
                                df_temp = intensity_bands_strategy(
                                    df_temp, 
                                    long_window=long_window,
                                    long_std=long_std,
                                    short_window=short_window,
                                    short_std=short_std,
                                    profit_ratio=profit_ratio,
                                    stop_loss_atr_mult=stop_loss_mult
                                )
                                
                                # 計算收益
                                df_temp['price_chg'] = df_temp['close'].pct_change()
                                df_temp['pnl'] = df_temp['Signal'].shift(1) * df_temp['price_chg']
                                
                                # 計算夏普比率
                                pnl = df_temp['pnl'].dropna()
                                if len(pnl) > 50 and pnl.std() > 0:
                                    sr = pnl.mean() / pnl.std() * np.sqrt(365)
                                    
                                    if sr > best_sr:
                                        best_sr = sr
                                        best_params = {
                                            'long_window': long_window,
                                            'long_std': round(long_std, 1),
                                            'short_window': short_window,
                                            'short_std': round(short_std, 1),
                                            'profit_ratio': profit_ratio,
                                            'stop_loss_mult': stop_loss_mult,
                                            'sharpe_ratio': round(sr, 4)
                                        }
                            
                            except Exception as e:
                                continue
                            
                            # 進度顯示
                            if current_combination % 500 == 0:
                                progress = (current_combination / total_combinations) * 100
                                print(f"優化進度: {progress:.1f}% ({current_combination}/{total_combinations})")
    
    print(f"參數優化完成！最佳夏普比率: {best_sr:.4f}")
    print(f"最佳參數: {best_params}")
    
    return best_params

def run_final_backtest(df, best_params):
    """使用最佳參數運行最終回測"""
    print("運行最終回測...")
    
    # 應用最佳參數
    df = intensity_bands_strategy(
        df,
        long_window=best_params['long_window'],
        long_std=best_params['long_std'],
        short_window=best_params['short_window'],
        short_std=best_params['short_std'],
        profit_ratio=best_params['profit_ratio'],
        stop_loss_atr_mult=best_params['stop_loss_mult']
    )
    
    # 計算收益
    df['price_chg'] = df['close'].pct_change()
    df['pnl'] = df['Signal'].shift(1) * df['price_chg']
    
    # 計算績效指標
    pnl = df['pnl'].dropna()
    
    if len(pnl) > 0:
        # 基本統計
        total_return = (1 + pnl).prod() - 1
        annual_return = (1 + total_return) ** (365 / len(pnl)) - 1
        volatility = pnl.std() * np.sqrt(365)
        sharpe_ratio = pnl.mean() / pnl.std() * np.sqrt(365) if pnl.std() > 0 else 0
        
        # 最大回撤
        cumulative = (1 + pnl).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 盈虧比
        wins = pnl[pnl > 0]
        losses = pnl[pnl < 0]
        profit_factor = wins.sum() / abs(losses.sum()) if len(losses) > 0 else np.inf
        
        # 勝率
        win_rate = (pnl > 0).mean()
        
        # 交易次數
        signals = df['Signal'].diff().fillna(0)
        total_trades = len(signals[signals != 0]) // 2  # 開倉和平倉算一筆交易
        
        performance = {
            'Total Return': f"{total_return:.2%}",
            'Annual Return': f"{annual_return:.2%}",
            'Sharpe Ratio': f"{sharpe_ratio:.4f}",
            'Max Drawdown': f"{max_drawdown:.2%}",
            'Profit Factor': f"{profit_factor:.2f}",
            'Win Rate': f"{win_rate:.2%}",
            'Total Trades': total_trades
        }
        
        print("\n=== 多方力道帶突破策略回測結果 ===")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        # 檢查目標達成
        if sharpe_ratio >= 1.5:
            print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
        
        # 繪製結果圖表
        plot_strategy_results(df)
        
        return df, performance
    
    else:
        print("無有效交易數據")
        return df, {}

def plot_strategy_results(df):
    """繪製策略結果圖表"""
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # 價格和信號
    axes[0].plot(df['date'], df['close'], label='BTC價格', alpha=0.7)
    
    # 標記買賣點
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        axes[0].scatter(buy_signals['date'], buy_signals['close'], 
                       color='green', marker='^', s=50, label='買入信號')
    
    if len(sell_signals) > 0:
        axes[0].scatter(sell_signals['date'], sell_signals['close'], 
                       color='red', marker='v', s=50, label='賣出信號')
    
    axes[0].set_title('價格走勢與交易信號')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 多方力道帶
    axes[1].plot(df['date'], df['long_intensity'], label='多方力道', color='green')
    axes[1].plot(df['date'], df['Long_Upper'], label='多方上軌', color='green', linestyle='--')
    axes[1].plot(df['date'], df['Long_Lower'], label='多方下軌', color='green', linestyle='--')
    axes[1].fill_between(df['date'], df['Long_Lower'], df['Long_Upper'], alpha=0.1, color='green')
    axes[1].set_title('多方力道帶')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 空方力道帶
    axes[2].plot(df['date'], df['short_intensity'], label='空方力道', color='red')
    axes[2].plot(df['date'], df['Short_Upper'], label='空方上軌', color='red', linestyle='--')
    axes[2].plot(df['date'], df['Short_Lower'], label='空方下軌', color='red', linestyle='--')
    axes[2].fill_between(df['date'], df['Short_Lower'], df['Short_Upper'], alpha=0.1, color='red')
    axes[2].set_title('空方力道帶')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("="*80)
    print("多方力道帶突破策略 + 空方力道帶突破策略")
    print("使用完整歷史數據，盈虧比2:1設計")
    print("="*80)
    
    # 1. 獲取市場數據
    market_data = get_market_data()
    
    # 2. 參數優化
    best_params = optimize_intensity_bands_parameters(market_data)
    
    if best_params:
        # 3. 最終回測
        final_df, performance = run_final_backtest(market_data, best_params)
        
        # 4. 保存結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"TakerIntensityBands_Backtest_{timestamp}.csv"
        final_df.to_csv(filename, index=False)
        print(f"\n回測結果已保存到: {filename}")
        
        print("\n多方力道帶突破策略回測完成！")
    else:
        print("參數優化失敗")

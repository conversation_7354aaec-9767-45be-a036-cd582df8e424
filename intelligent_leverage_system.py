"""
智能槓桿選擇系統
根據波動率動態調整槓桿倍數 (1-5倍)
目標：降低最大回撤，放大總收益

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_base_strategy_results():
    """載入基礎策略結果"""
    print("載入基礎策略回測結果...")
    
    import glob
    csv_files = glob.glob("Institutional_CCB_TakerIntensity_Backtest_*.csv")
    
    if not csv_files:
        print("❌ 找不到基礎策略結果")
        return None
    
    latest_file = max(csv_files)
    print(f"載入文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ 基礎策略數據載入完成：{len(df)}條記錄")
    return df

def calculate_volatility_indicators(df, vol_window=20):
    """計算波動率指標"""
    
    # 1. 價格波動率 (基於收益率)
    df['returns'] = df['Close'].pct_change()
    df['volatility'] = df['returns'].rolling(window=vol_window).std() * np.sqrt(365)  # 年化波動率
    
    # 2. ATR波動率
    df['high'] = df['Close'] * (1 + np.abs(df['returns']))  # 模擬高價
    df['low'] = df['Close'] * (1 - np.abs(df['returns']))   # 模擬低價
    
    df['tr'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            np.abs(df['high'] - df['Close'].shift(1)),
            np.abs(df['low'] - df['Close'].shift(1))
        )
    )
    df['atr'] = df['tr'].rolling(window=14).mean()
    df['atr_pct'] = df['atr'] / df['Close']  # ATR百分比
    
    # 3. 波動率百分位數 (用於動態調整) - 修復pandas兼容性
    df['vol_percentile'] = df['volatility'].rolling(window=60).apply(
        lambda x: (x.iloc[-1] <= x).mean() if len(x) > 0 else 0.5
    )
    df['atr_percentile'] = df['atr_pct'].rolling(window=60).apply(
        lambda x: (x.iloc[-1] <= x).mean() if len(x) > 0 else 0.5
    )
    
    return df

def intelligent_leverage_selection(df, min_leverage=1, max_leverage=5):
    """
    智能槓桿選擇邏輯
    
    核心思想：
    - 低波動率時使用高槓桿
    - 高波動率時使用低槓桿
    - 考慮信號強度
    """
    
    df['leverage'] = 1.0  # 默認1倍槓桿
    
    for i in range(len(df)):
        if pd.isna(df.iloc[i]['vol_percentile']) or pd.isna(df.iloc[i]['atr_percentile']):
            continue
            
        # 獲取當前市場狀態
        vol_pct = df.iloc[i]['vol_percentile']
        atr_pct = df.iloc[i]['atr_percentile']
        signal = df.iloc[i]['Signal']
        
        if signal != 0:  # 只在有交易信號時調整槓桿
            
            # 基礎槓桿計算 (波動率越低，槓桿越高)
            vol_factor = 1 - vol_pct  # 0-1之間，波動率低時接近1
            atr_factor = 1 - atr_pct  # 0-1之間，ATR低時接近1
            
            # 綜合波動率因子
            combined_factor = (vol_factor + atr_factor) / 2
            
            # 計算槓桿倍數
            leverage = min_leverage + (max_leverage - min_leverage) * combined_factor
            
            # 額外的風險控制
            # 如果波動率極高 (>80%百分位)，強制降低槓桿
            if vol_pct > 0.8 or atr_pct > 0.8:
                leverage = min(leverage, 2.0)  # 最高2倍
            
            # 如果波動率極低 (<20%百分位)，可以使用更高槓桿
            elif vol_pct < 0.2 and atr_pct < 0.2:
                leverage = min(leverage * 1.2, max_leverage)  # 適度提升
            
            # 確保槓桿在合理範圍內
            leverage = np.clip(leverage, min_leverage, max_leverage)
            
            df.iloc[i, df.columns.get_loc('leverage')] = leverage
        
        else:
            # 無信號時保持1倍槓桿
            df.iloc[i, df.columns.get_loc('leverage')] = 1.0
    
    return df

def calculate_leveraged_returns(df):
    """計算槓桿收益"""
    
    # 基礎收益 (1倍槓桿)
    df['base_return'] = df['Signal'].shift(1) * df['returns']
    
    # 槓桿收益
    df['leveraged_return'] = df['Signal'].shift(1) * df['returns'] * df['leverage'].shift(1)
    
    # 計算累積收益
    df['base_cumulative'] = (1 + df['base_return'].fillna(0)).cumprod()
    df['leveraged_cumulative'] = (1 + df['leveraged_return'].fillna(0)).cumprod()
    
    return df

def calculate_leveraged_performance(df):
    """計算槓桿策略績效"""
    
    base_returns = df['base_return'].dropna()
    leveraged_returns = df['leveraged_return'].dropna()
    
    if len(leveraged_returns) == 0:
        return None
    
    # 基礎策略績效
    base_total_return = (1 + base_returns).prod() - 1
    base_annual_return = (1 + base_total_return) ** (365 / len(base_returns)) - 1
    base_volatility = base_returns.std() * np.sqrt(365)
    base_sharpe = base_returns.mean() / base_returns.std() * np.sqrt(365) if base_returns.std() > 0 else 0
    
    # 基礎策略最大回撤
    base_cumulative = (1 + base_returns).cumprod()
    base_rolling_max = base_cumulative.expanding().max()
    base_drawdown = (base_cumulative - base_rolling_max) / base_rolling_max
    base_max_drawdown = base_drawdown.min()
    
    # 槓桿策略績效
    lev_total_return = (1 + leveraged_returns).prod() - 1
    lev_annual_return = (1 + lev_total_return) ** (365 / len(leveraged_returns)) - 1
    lev_volatility = leveraged_returns.std() * np.sqrt(365)
    lev_sharpe = leveraged_returns.mean() / leveraged_returns.std() * np.sqrt(365) if leveraged_returns.std() > 0 else 0
    
    # 槓桿策略最大回撤
    lev_cumulative = (1 + leveraged_returns).cumprod()
    lev_rolling_max = lev_cumulative.expanding().max()
    lev_drawdown = (lev_cumulative - lev_rolling_max) / lev_rolling_max
    lev_max_drawdown = lev_drawdown.min()
    
    # 槓桿使用統計
    leverage_stats = df['leverage'][df['Signal'] != 0].describe()
    
    performance = {
        'base_strategy': {
            'total_return': base_total_return,
            'annual_return': base_annual_return,
            'sharpe_ratio': base_sharpe,
            'max_drawdown': base_max_drawdown,
            'volatility': base_volatility
        },
        'leveraged_strategy': {
            'total_return': lev_total_return,
            'annual_return': lev_annual_return,
            'sharpe_ratio': lev_sharpe,
            'max_drawdown': lev_max_drawdown,
            'volatility': lev_volatility
        },
        'leverage_stats': {
            'mean': leverage_stats['mean'],
            'min': leverage_stats['min'],
            'max': leverage_stats['max'],
            'std': leverage_stats['std']
        },
        'improvement': {
            'return_improvement': lev_total_return - base_total_return,
            'drawdown_improvement': lev_max_drawdown - base_max_drawdown,
            'sharpe_improvement': lev_sharpe - base_sharpe
        }
    }
    
    return performance

def plot_leverage_comparison(df, performance):
    """繪製槓桿策略比較圖"""
    
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 10
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    
    # 1. 累積收益比較
    ax1 = axes[0, 0]
    ax1.plot(df.index, df['base_cumulative'], label='Base Strategy (1x)', color='blue', linewidth=2)
    ax1.plot(df.index, df['leveraged_cumulative'], label='Intelligent Leverage Strategy', color='red', linewidth=2)
    
    # BTC基準
    btc_cumulative = df['Close'] / df['Close'].iloc[0]
    ax1.plot(df.index, btc_cumulative, label='BTC Buy & Hold', color='orange', linewidth=1, alpha=0.7)
    
    ax1.set_title('Cumulative Returns Comparison', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Cumulative Return', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 槓桿使用分佈
    ax2 = axes[0, 1]
    leverage_data = df['leverage'][df['Signal'] != 0]
    ax2.hist(leverage_data, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(leverage_data.mean(), color='red', linestyle='--', 
               label=f'Mean: {leverage_data.mean():.2f}x')
    ax2.set_title('Leverage Distribution', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Leverage Multiplier')
    ax2.set_ylabel('Frequency')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 波動率 vs 槓桿
    ax3 = axes[1, 0]
    trading_data = df[df['Signal'] != 0]
    scatter = ax3.scatter(trading_data['volatility'], trading_data['leverage'], 
                         c=trading_data['Signal'], cmap='RdYlGn', alpha=0.6)
    ax3.set_title('Volatility vs Leverage Selection', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Volatility')
    ax3.set_ylabel('Leverage Multiplier')
    plt.colorbar(scatter, ax=ax3, label='Signal Direction')
    ax3.grid(True, alpha=0.3)
    
    # 4. 績效比較表
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    perf_text = "=== Performance Comparison ===\n\n"
    
    base_perf = performance['base_strategy']
    lev_perf = performance['leveraged_strategy']
    improvement = performance['improvement']
    lev_stats = performance['leverage_stats']
    
    perf_text += f"Base Strategy (1x Leverage):\n"
    perf_text += f"  Total Return: {base_perf['total_return']:.2%}\n"
    perf_text += f"  Sharpe Ratio: {base_perf['sharpe_ratio']:.4f}\n"
    perf_text += f"  Max Drawdown: {base_perf['max_drawdown']:.2%}\n\n"
    
    perf_text += f"Intelligent Leverage Strategy:\n"
    perf_text += f"  Total Return: {lev_perf['total_return']:.2%}\n"
    perf_text += f"  Sharpe Ratio: {lev_perf['sharpe_ratio']:.4f}\n"
    perf_text += f"  Max Drawdown: {lev_perf['max_drawdown']:.2%}\n\n"
    
    perf_text += f"Improvements:\n"
    perf_text += f"  Return: {improvement['return_improvement']:+.2%}\n"
    perf_text += f"  Sharpe: {improvement['sharpe_improvement']:+.4f}\n"
    perf_text += f"  Drawdown: {improvement['drawdown_improvement']:+.2%}\n\n"
    
    perf_text += f"Leverage Statistics:\n"
    perf_text += f"  Average: {lev_stats['mean']:.2f}x\n"
    perf_text += f"  Range: {lev_stats['min']:.2f}x - {lev_stats['max']:.2f}x\n"
    
    ax4.text(0.1, 0.9, perf_text, transform=ax4.transAxes, 
            fontsize=11, verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存圖表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Intelligent_Leverage_Strategy_Comparison_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 槓桿策略比較圖已保存: {filename}")

def run_intelligent_leverage_backtest():
    """運行智能槓桿回測"""
    
    print("="*80)
    print("智能槓桿選擇系統")
    print("根據波動率動態調整槓桿倍數 (1-5倍)")
    print("目標：降低最大回撤，放大總收益")
    print("="*80)
    
    # 1. 載入基礎策略結果
    df = load_base_strategy_results()
    if df is None:
        return None
    
    # 2. 計算波動率指標
    print("\n計算波動率指標...")
    df = calculate_volatility_indicators(df)
    
    # 3. 智能槓桿選擇
    print("執行智能槓桿選擇...")
    df = intelligent_leverage_selection(df, min_leverage=1, max_leverage=5)
    
    # 4. 計算槓桿收益
    print("計算槓桿收益...")
    df = calculate_leveraged_returns(df)
    
    # 5. 計算績效
    print("計算績效指標...")
    performance = calculate_leveraged_performance(df)
    
    if performance is None:
        print("❌ 績效計算失敗")
        return None
    
    # 6. 顯示結果
    print("\n" + "="*60)
    print("智能槓桿策略結果")
    print("="*60)
    
    base_perf = performance['base_strategy']
    lev_perf = performance['leveraged_strategy']
    improvement = performance['improvement']
    
    print(f"\n📊 基礎策略 (1倍槓桿):")
    print(f"   總收益: {base_perf['total_return']:.2%}")
    print(f"   夏普比率: {base_perf['sharpe_ratio']:.4f}")
    print(f"   最大回撤: {base_perf['max_drawdown']:.2%}")
    
    print(f"\n🚀 智能槓桿策略:")
    print(f"   總收益: {lev_perf['total_return']:.2%}")
    print(f"   夏普比率: {lev_perf['sharpe_ratio']:.4f}")
    print(f"   最大回撤: {lev_perf['max_drawdown']:.2%}")
    
    print(f"\n📈 改進效果:")
    print(f"   收益提升: {improvement['return_improvement']:+.2%}")
    print(f"   夏普提升: {improvement['sharpe_improvement']:+.4f}")
    print(f"   回撤變化: {improvement['drawdown_improvement']:+.2%}")
    
    # 評估目標達成情況
    print(f"\n🎯 目標達成評估:")
    if improvement['return_improvement'] > 0:
        print(f"   ✅ 總收益放大: +{improvement['return_improvement']:.2%}")
    else:
        print(f"   ❌ 總收益未放大: {improvement['return_improvement']:.2%}")
    
    if improvement['drawdown_improvement'] > 0:  # 回撤變大（負值變小）
        print(f"   ❌ 最大回撤增加: {improvement['drawdown_improvement']:+.2%}")
    else:
        print(f"   ✅ 最大回撤減小: {improvement['drawdown_improvement']:+.2%}")
    
    # 7. 可視化
    plot_leverage_comparison(df, performance)
    
    # 8. 保存結果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Intelligent_Leverage_Strategy_Results_{timestamp}.csv"
    df.to_csv(filename)
    print(f"\n✅ 智能槓桿策略結果已保存: {filename}")
    
    return df, performance

if __name__ == "__main__":
    # 執行智能槓桿回測
    results = run_intelligent_leverage_backtest()

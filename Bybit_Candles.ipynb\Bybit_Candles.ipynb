{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching data for W (1h)...\n", "No data found for W between 2023-01-01 00:00:00+00:00 and 2024-04-03 14:00:00+00:00\n", "Saved to /Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data/bybit_w_1h_20230101_20241231.csv\n"]}], "source": ["from pybit.unified_trading import HTTP\n", "import pandas as pd\n", "from datetime import datetime, UTC\n", "import time\n", "import os\n", "\n", "def download_bybit_klines(\n", "    asset_name: str,\n", "    data_interval_str: str,\n", "    date_range: str,\n", "    data_path: str,\n", "    testnet: bool = False\n", "):\n", "    session = HTTP(testnet=testnet)\n", "\n", "    # Interval mapping for Bybit API\n", "    interval_map = {\n", "        '1m':'1',\n", "        '3m':'3',\n", "        '5m':'5',\n", "        '15m': '15',\n", "        '30m': '30',\n", "        '1h': '60',\n", "        '2h': '120',\n", "        '4h': '240',\n", "        '6h': '360',\n", "        '12h': '720',\n", "        '1d': 'D',\n", "        '1w': 'W',\n", "        '1M': 'M',\n", "    }\n", "    if data_interval_str not in interval_map:\n", "        raise ValueError(f\"Unsupported interval: {data_interval_str}\")\n", "    \n", "    interval = interval_map[data_interval_str]\n", "    limit = 1000\n", "\n", "    # Time gap per 1000 candles in milliseconds\n", "    gap_map = {\n", "        '15': 15 * 60 * 1000,\n", "        '30': 30 * 60 * 1000,\n", "        '60': 60 * 60 * 1000,\n", "        '120': 120 * 60 * 1000,\n", "        '240': 240 * 60 * 1000,\n", "        '360': 360 * 60 * 1000,\n", "        '720': 720 * 60 * 1000,\n", "        'D': 1440 * 60 * 1000,\n", "        'W': 7 * 1440 * 60 * 1000,\n", "        'M': 30 * 1440 * 60 * 1000,\n", "    }\n", "    gap = gap_map[interval] * limit\n", "\n", "    # Parse date range\n", "    starttime = datetime.strptime(date_range.split('_')[0], \"%Y%m%d\").replace(tzinfo=UTC)\n", "    endtime = datetime.strptime(date_range.split('_')[1], \"%Y%m%d\").replace(tzinfo=UTC)\n", "    starttime_ts = int(starttime.timestamp() * 1000)\n", "    endtime_ts = int(endtime.timestamp() * 1000)\n", "\n", "    print(f\"Fetching data for {asset_name} ({data_interval_str})...\")\n", "    price_data = []\n", "    current_endtime = endtime_ts\n", "    first_data_timestamp = None\n", "\n", "    while starttime_ts < current_endtime:\n", "        try:\n", "            candle = session.get_kline(\n", "                category=\"linear\",\n", "                symbol=f\"{asset_name}USDT\",\n", "                interval=interval,\n", "                end=current_endtime,\n", "                limit=limit,\n", "            )\n", "            candle_data = candle[\"result\"][\"list\"]\n", "            if not candle_data:\n", "                current_endtime -= gap\n", "                continue\n", "\n", "            # Capture earliest available timestamp\n", "            if first_data_timestamp is None:\n", "                first_data_timestamp = int(candle_data[-1][0])\n", "\n", "            price_data.extend(candle_data)\n", "            current_endtime -= gap\n", "            time.sleep(0.1)\n", "        except Exception as e:\n", "            print(f\"Error fetching data for {asset_name}: {e}\")\n", "            break\n", "\n", "    if not price_data:\n", "        print(f\"No data available for {asset_name} between {starttime} and {endtime}\")\n", "        return\n", "\n", "    # Process and clean data\n", "    df = pd.DataFrame(price_data)\n", "    df.columns = [\"time\", \"open\", \"high\", \"low\", \"close\", \"volume\", \"turnover\"]\n", "    df[\"time\"] = df[\"time\"].astype(float)\n", "    df = df.drop_duplicates(subset=[\"time\"])\n", "    df[\"time\"] = pd.to_datetime(df[\"time\"], unit=\"ms\", utc=True)\n", "    df = df.sort_values(\"time\")\n", "\n", "    actual_start = df[\"time\"].min()\n", "\n", "    if actual_start > starttime:\n", "        print(f\"No data found for {asset_name} between {starttime} and {actual_start}\")\n", "\n", "    # Save CSV\n", "    filename = f\"bybit_{asset_name.lower()}_{data_interval_str}_{date_range}.csv\"\n", "    filepath = os.path.join(data_path, filename)\n", "    df.to_csv(filepath, index=False)\n", "    print(f\"Saved to {filepath}\")\n", "\n", "download_bybit_klines(\n", "    asset_name='W',\n", "    data_interval_str='1h', #1m,3m,5m,15m,30m,60m,120m,240m,360m,720,1d,1w,1M\n", "    date_range='20230101_20241231',\n", "    data_path='/Users/<USER>/Documents/Python_Code/Cybotrade_Projects/ssh-keys/cybotrade/YouTube_Shooting/Data'\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data(asset, path, data_interval_str, date_range):\n", "    df = pd.read_csv(f\"{path}/{asset1}/bybit_{asset1.lower()}_{data_interval_str}_{date_range}.csv\")\n", "    df['time'] = pd.to_datetime(df1['time'])\n", "    prices = df[['time', 'close']].rename(columns={'close': asset1})\n", "    return prices\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pypi_cybotrade", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
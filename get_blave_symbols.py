#!/usr/bin/env python3
"""
獲取Blave支持的所有幣種列表
"""

import asyncio
import aiohttp
import requests

async def get_blave_symbols():
    """獲取Blave支持的幣種"""
    
    base_url = "https://api.blave.org/"
    api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
    secret_key = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
    
    headers = {
        "api-key": api_key,
        "secret-key": secret_key
    }
    
    print("🔍 獲取Blave支持的幣種列表...")
    
    try:
        # 獲取Taker Intensity支持的幣種
        url = f"{base_url}taker_intensity/get_symbols"
        
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            ti_symbols = data.get('data', [])
            print(f"✅ Taker Intensity支持的幣種: {len(ti_symbols)} 個")
            print(f"幣種列表: {ti_symbols}")
        else:
            print(f"❌ 獲取TI幣種失敗: {response.status_code}")
            ti_symbols = []
        
        # 獲取Chip Concentration支持的幣種
        url = f"{base_url}holder_concentration/get_symbols"
        
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            cc_symbols = data.get('data', [])
            print(f"✅ Chip Concentration支持的幣種: {len(cc_symbols)} 個")
            print(f"幣種列表: {cc_symbols}")
        else:
            print(f"❌ 獲取CC幣種失敗: {response.status_code}")
            cc_symbols = []
        
        # 找出兩者都支持的幣種
        common_symbols = list(set(ti_symbols) & set(cc_symbols))
        common_symbols.sort()
        
        print(f"\n🎯 兩者都支持的幣種: {len(common_symbols)} 個")
        print(f"完整列表: {common_symbols}")
        
        return common_symbols
        
    except Exception as e:
        print(f"❌ 獲取幣種列表失敗: {e}")
        return []

def check_bybit_symbols(symbols):
    """檢查Bybit是否支持這些幣種"""
    print(f"\n🔍 檢查Bybit支持情況...")
    
    supported_symbols = []
    
    for symbol in symbols:
        try:
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                'category': 'spot',
                'symbol': symbol,
                'interval': '1h',
                'limit': 1
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data['retCode'] == 0 and data['result']['list']:
                    supported_symbols.append(symbol)
                    print(f"✅ {symbol}: 支持")
                else:
                    print(f"❌ {symbol}: 不支持或無數據")
            else:
                print(f"❌ {symbol}: API錯誤")
                
        except Exception as e:
            print(f"❌ {symbol}: 檢查失敗 - {e}")
    
    print(f"\n🎯 Bybit也支持的幣種: {len(supported_symbols)} 個")
    print(f"最終可用列表: {supported_symbols}")
    
    return supported_symbols

if __name__ == "__main__":
    symbols = asyncio.run(get_blave_symbols())
    
    if symbols:
        final_symbols = check_bybit_symbols(symbols)
        
        print(f"\n📊 最終回測幣種列表 ({len(final_symbols)} 個):")
        for i, symbol in enumerate(final_symbols, 1):
            print(f"{i:2d}. {symbol}")
    else:
        print("❌ 無法獲取幣種列表")

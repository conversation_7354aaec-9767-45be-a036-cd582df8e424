#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改良版策略回測系統
基於原始回測發現的問題，開發三個改良版策略進行測試
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import sys
import logging
import traceback
from typing import Dict, List, Optional, Tuple

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('improved_strategies_backtest.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技術指標計算類"""
    
    def bollinger_bands(self, prices, window=20, std_dev=2):
        """計算布林帶"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def atr(self, high, low, close, period=14):
        """計算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    def obv(self, close, volume):
        """計算OBV (On-Balance Volume)"""
        obv = np.where(close > close.shift(1), volume, 
                      np.where(close < close.shift(1), -volume, 0))
        return pd.Series(obv, index=close.index).cumsum()
    
    def calculate_ti_confidence_interval(self, ti_values, lookback=24, confidence=0.7):
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2
        
        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)
        
        return upper_limit, lower_limit

class HistoricalDataManager:
    """歷史數據管理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.data_fetcher = DataFetcher(config_manager)
        
    async def get_historical_data_range(self, symbol: str, timeframe: str, 
                                      start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """獲取指定時間範圍的歷史數據"""
        try:
            logger.info(f"獲取 {symbol} {timeframe} 歷史數據: {start_date.date()} 到 {end_date.date()}")
            
            # 計算需要的數據點數量
            if timeframe == '1h':
                total_hours = int((end_date - start_date).total_seconds() / 3600)
                required_points = min(total_hours, 2000)
            elif timeframe == '4h':
                total_4h_periods = int((end_date - start_date).total_seconds() / (4 * 3600))
                required_points = min(total_4h_periods, 1500)
            else:
                required_points = 730
            
            # 分批獲取數據
            all_data = []
            current_end = end_date
            batch_size = 200
            
            while len(all_data) < required_points and current_end > start_date:
                if timeframe == '1h':
                    batch_start = current_end - timedelta(hours=batch_size)
                elif timeframe == '4h':
                    batch_start = current_end - timedelta(hours=batch_size * 4)
                else:
                    batch_start = current_end - timedelta(days=batch_size)
                
                batch_start = max(batch_start, start_date)
                
                batch_data = await self.data_fetcher.get_latest_data(symbol, timeframe)
                
                if batch_data is not None and len(batch_data) > 0:
                    batch_data = batch_data[
                        (batch_data.index >= batch_start) & 
                        (batch_data.index <= current_end)
                    ]
                    
                    if len(batch_data) > 0:
                        all_data.append(batch_data)
                        logger.info(f"   獲取批次數據: {len(batch_data)} 條")
                
                current_end = batch_start - timedelta(minutes=1)
                await asyncio.sleep(0.1)
            
            if not all_data:
                logger.warning(f"❌ {symbol} {timeframe} 無法獲取歷史數據")
                return None
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index().drop_duplicates()
            combined_data = combined_data[
                (combined_data.index >= start_date) & 
                (combined_data.index <= end_date)
            ]
            
            logger.info(f"✅ {symbol} {timeframe} 歷史數據獲取完成: {len(combined_data)} 條記錄")
            return combined_data
            
        except Exception as e:
            logger.error(f"❌ {symbol} {timeframe} 歷史數據獲取失敗: {e}")
            return None

class ImprovedStrategiesBacktester:
    """改良版策略回測器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_manager = HistoricalDataManager(self.config_manager)
        self.indicators = TechnicalIndicators()
        
        # 加載策略配置
        self.strategies = self.load_strategies()
        
        # 回測結果存儲
        self.all_results = {}
        self.all_trade_records = {}
        
    def load_strategies(self) -> Dict:
        """加載所有策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('active_strategies', {})
        except Exception as e:
            logger.error(f"加載策略配置失敗: {e}")
            return {}
    
    def calculate_supertrend_stop_loss(self, data: pd.DataFrame, direction: str, 
                                     entry_price: float, period: int = 10, 
                                     multiplier: float = 3.0) -> Optional[Dict]:
        """計算SUPERTREND止盈止損"""
        try:
            if len(data) < period:
                return None
                
            hl2 = (data['High'] + data['Low']) / 2
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=period)
            
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)
            
            supertrend = np.where(data['Close'] <= lower_band.shift(1), lower_band, 
                                 np.where(data['Close'] >= upper_band.shift(1), upper_band, np.nan))
            
            supertrend = pd.Series(supertrend, index=data.index).fillna(method='ffill')
            
            if len(supertrend) == 0 or pd.isna(supertrend.iloc[-1]):
                return None
                
            current_supertrend = supertrend.iloc[-1]
            
            if direction == 'LONG':
                stop_loss = current_supertrend
                take_profit = entry_price + (abs(entry_price - current_supertrend) * 2.5)
            else:
                stop_loss = current_supertrend
                take_profit = entry_price - (abs(entry_price - current_supertrend) * 2.5)
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 2.5,
                'method': 'SUPERTREND'
            }
            
        except Exception as e:
            logger.error(f"SUPERTREND計算失敗: {e}")
            return None

    def strategy1_simplified_ti_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略1：簡化版TI+BB中軌策略
        核心邏輯：主要依賴Taker Intensity + 布林帶中軌突破
        信號條件：4選3邏輯，減少條件嚴格度
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            ti_confidence = strategy_config.get('ti_confidence', 0.7)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )

            # 計算TI信賴區間
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=ti_confidence
            )

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle

            # 簡化版多頭信號條件 (4選3)
            long_conditions = {
                'bb_middle_breakout': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive': current_ti > 0,
                'ti_momentum_up': current_ti > prev_ti,
                'ti_strong': current_ti > ti_upper_limit.iloc[-1] if not pd.isna(ti_upper_limit.iloc[-1]) else False
            }

            # 簡化版空頭信號條件 (4選3)
            short_conditions = {
                'bb_middle_breakdown': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative': current_ti < 0,
                'ti_momentum_down': current_ti < prev_ti,
                'ti_weak': current_ti < ti_lower_limit.iloc[-1] if not pd.isna(ti_lower_limit.iloc[-1]) else False
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_ti': current_ti,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'Simplified_TI_BB'
            }

        except Exception as e:
            logger.error(f"策略1計算失敗: {e}")
            return None

    def strategy2_contrarian_rsi_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略2：逆勢RSI+BB中軌策略
        修改RSI邏輯為逆勢操作：RSI<30時做多、RSI>70時做空
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            rsi_period = strategy_config.get('rsi_period', 14)
            ti_lookback = strategy_config.get('ti_lookback', 24)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(data['Close'], period=rsi_period)

            # 計算TI數據
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_rsi = rsi.iloc[-1]
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle

            # 逆勢多頭信號條件 (4選3)
            long_conditions = {
                'rsi_oversold': current_rsi < 30,  # 逆勢：RSI超賣時做多
                'bb_middle_support': current_price > current_bb_middle,
                'ti_positive_momentum': current_ti > 0 and current_ti > prev_ti,
                'price_near_bb_middle': abs(current_price - current_bb_middle) / current_bb_middle < 0.02
            }

            # 逆勢空頭信號條件 (4選3)
            short_conditions = {
                'rsi_overbought': current_rsi > 70,  # 逆勢：RSI超買時做空
                'bb_middle_resistance': current_price < current_bb_middle,
                'ti_negative_momentum': current_ti < 0 and current_ti < prev_ti,
                'price_near_bb_middle': abs(current_price - current_bb_middle) / current_bb_middle < 0.02
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_rsi': current_rsi,
                'current_ti': current_ti,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'Contrarian_RSI_BB'
            }

        except Exception as e:
            logger.error(f"策略2計算失敗: {e}")
            return None

    def strategy3_obv_ti_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略3：OBV成交量+TI+BB策略
        完全移除RSI指標，新增OBV成交量指標
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            obv_period = strategy_config.get('obv_period', 10)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )

            # 計算OBV和其移動平均
            obv = self.indicators.obv(data['Close'], data['Volume'])
            obv_ma = obv.rolling(window=obv_period).mean()

            # 計算TI數據
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=0.7
            )

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle
            current_obv = obv.iloc[-1]
            current_obv_ma = obv_ma.iloc[-1]
            prev_obv_ma = obv_ma.iloc[-2] if len(obv_ma) > 1 else current_obv_ma

            # OBV多頭信號條件 (4選3)
            long_conditions = {
                'bb_middle_breakout': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive_strong': current_ti > 0 and current_ti > ti_upper_limit.iloc[-1] if not pd.isna(ti_upper_limit.iloc[-1]) else current_ti > 0,
                'obv_rising': current_obv > current_obv_ma and current_obv_ma > prev_obv_ma,
                'volume_confirmation': data['Volume'].iloc[-1] > data['Volume'].rolling(10).mean().iloc[-1]
            }

            # OBV空頭信號條件 (4選3)
            short_conditions = {
                'bb_middle_breakdown': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative_strong': current_ti < 0 and current_ti < ti_lower_limit.iloc[-1] if not pd.isna(ti_lower_limit.iloc[-1]) else current_ti < 0,
                'obv_falling': current_obv < current_obv_ma and current_obv_ma < prev_obv_ma,
                'volume_confirmation': data['Volume'].iloc[-1] > data['Volume'].rolling(10).mean().iloc[-1]
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_ti': current_ti,
                'current_obv': current_obv,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'OBV_TI_BB'
            }

        except Exception as e:
            logger.error(f"策略3計算失敗: {e}")
            return None

    def simulate_trade_exit(self, data: pd.DataFrame, entry_index: int, trade: Dict) -> Optional[Dict]:
        """模擬交易平倉"""
        try:
            entry_price = trade['entry_price']
            stop_loss = trade['stop_loss']
            take_profit = trade['take_profit']
            direction = trade['direction']

            for i in range(entry_index + 1, len(data)):
                current_high = data['High'].iloc[i]
                current_low = data['Low'].iloc[i]
                current_close = data['Close'].iloc[i]

                if direction == 'LONG':
                    if current_low <= stop_loss:
                        pnl_pct = (stop_loss - entry_price) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    elif current_high >= take_profit:
                        pnl_pct = (take_profit - entry_price) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                else:  # SHORT
                    if current_high >= stop_loss:
                        pnl_pct = (entry_price - stop_loss) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    elif current_low <= take_profit:
                        pnl_pct = (entry_price - take_profit) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }

            # 如果沒有觸發止盈止損，用最後價格平倉
            final_price = data['Close'].iloc[-1]
            if direction == 'LONG':
                pnl_pct = (final_price - entry_price) / entry_price * 100
            else:
                pnl_pct = (entry_price - final_price) / entry_price * 100

            return {
                'exit_price': final_price,
                'exit_time': data.index[-1],
                'exit_type': 'MARKET_CLOSE',
                'pnl_pct': pnl_pct,
                'duration_hours': (data.index[-1] - trade['timestamp']).total_seconds() / 3600
            }

        except Exception as e:
            logger.error(f"模擬交易平倉失敗: {e}")
            return None

"""
測試系統啟動的健壯性
確保單個組件失敗不會導致整個系統退出

作者: 專業量化策略工程師
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

async def test_startup_robustness():
    """測試啟動健壯性"""
    print("🔍 測試系統啟動健壯性...")
    
    # 模擬Railway環境
    os.environ['RAILWAY_ENVIRONMENT'] = 'test'
    os.environ['RAILWAY_SERVICE_NAME'] = 'quant-trading-system'
    
    try:
        from main import QuantTradingSystem
        
        # 創建系統實例
        system = QuantTradingSystem()
        
        print("✅ 系統實例創建成功")
        
        # 測試啟動過程
        print("\n🔍 測試啟動過程...")
        await system.startup()
        
        print("✅ 系統啟動完成，沒有拋出異常")
        
        # 測試運行一個週期
        print("\n🔍 測試策略週期...")
        await system.run_strategy_cycle()
        
        print("✅ 策略週期執行成功")
        
        # 測試關閉
        print("\n🔍 測試系統關閉...")
        await system.shutdown()
        
        print("✅ 系統關閉成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

async def test_component_failure_tolerance():
    """測試組件失敗容錯性"""
    print("\n" + "="*60)
    print("🔍 測試組件失敗容錯性...")
    print("="*60)
    
    try:
        from main import QuantTradingSystem
        
        # 創建系統實例
        system = QuantTradingSystem()
        
        # 故意破壞某些組件來測試容錯性
        print("🔧 模擬組件失敗...")
        
        # 模擬健康檢查端口被占用
        import socket
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            test_socket.bind(('0.0.0.0', 8080))
            print("⚠️ 端口8080已被測試占用")
        except:
            print("ℹ️ 端口8080可能已被占用")
        
        # 嘗試啟動系統
        await system.startup()
        print("✅ 系統在組件失敗情況下仍然啟動成功")
        
        # 清理
        test_socket.close()
        await system.shutdown()
        
        return True
        
    except Exception as e:
        print(f"❌ 容錯測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

async def test_main_function():
    """測試main函數"""
    print("\n" + "="*60)
    print("🔍 測試main函數（5秒後自動停止）...")
    print("="*60)
    
    try:
        from main import main, shutdown_event
        
        # 創建main任務
        main_task = asyncio.create_task(main())
        
        # 等待系統啟動
        await asyncio.sleep(3)
        print("✅ 系統啟動完成")
        
        # 檢查系統是否還在運行
        if not main_task.done():
            print("✅ 系統正在正常運行")
        else:
            print("❌ 系統意外退出")
            return False
        
        # 優雅停止
        if shutdown_event:
            shutdown_event.set()
            print("📡 發送關閉信號...")
        
        # 等待系統關閉
        try:
            await asyncio.wait_for(main_task, timeout=10)
            print("✅ 系統優雅關閉")
        except asyncio.TimeoutError:
            print("⚠️ 系統關閉超時，強制取消")
            main_task.cancel()
        
        return True
        
    except Exception as e:
        print(f"❌ main函數測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 開始系統健壯性測試...")
    
    results = []
    
    # 測試1: 基本啟動
    result1 = asyncio.run(test_startup_robustness())
    results.append(("基本啟動測試", result1))
    
    # 測試2: 組件失敗容錯
    result2 = asyncio.run(test_component_failure_tolerance())
    results.append(("組件失敗容錯測試", result2))
    
    # 測試3: main函數
    result3 = asyncio.run(test_main_function())
    results.append(("main函數測試", result3))
    
    # 總結
    print("\n" + "="*60)
    print("🎯 測試總結:")
    print("="*60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有測試通過！系統現在應該不會意外退出了！")
        print("🚂 Railway部署後應該能穩定運行24/7")
    else:
        print("\n⚠️ 部分測試失敗，需要進一步修復")

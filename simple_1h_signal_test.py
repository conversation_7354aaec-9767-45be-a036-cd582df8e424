#!/usr/bin/env python3
"""
簡化版1H信號品質測試
專注於測試幾個主要幣種的信號品質
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class Simple1HSignalTest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 測試幣種 - 主流幣和山寨幣各選幾個
        self.symbols = [
            # 主流幣
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT',
            # 山寨幣
            'ALGOUSDT', 'DOGEUSDT', 'UNIUSDT', 'AVAXUSDT', 'LINKUSDT'
        ]
        
        # 1H策略參數
        self.ma_short = 8
        self.ma_long = 15
        self.ti_threshold = 0.3
        self.atr_period = 14
        
        # 測試盈虧比
        self.risk_reward_ratios = [2.0, 2.5, 3.0]
        
        self.results = []
        
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    async def get_1h_data(self, symbol: str) -> pd.DataFrame:
        """獲取1H數據"""
        try:
            print(f"📊 獲取 {symbol} 1H 數據...")
            
            data = await self.data_fetcher.get_latest_data(symbol, '1H')
            
            if data is None or data.empty:
                print(f"❌ {symbol} 數據獲取失敗")
                return None
                
            # 檢查必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} 缺少列: {missing_columns}")
                return None
            
            # 清理數據
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                print(f"❌ {symbol} 數據不足: {len(data_clean)}")
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            time_span = (data_clean.index[-1] - data_clean.index[0]).total_seconds() / (24 * 3600)
            
            print(f"✅ {symbol} 數據: {len(data_clean)} 條記錄, {time_span:.1f} 天")
            
            return data_clean
            
        except Exception as e:
            print(f"❌ {symbol} 數據獲取失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame) -> list:
        """生成交易信號"""
        signals = []
        
        if len(data) < max(self.ma_short, self.ma_long, self.atr_period) + 10:
            return signals
        
        for i in range(max(self.ma_short, self.ma_long, self.atr_period), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            atr = data['atr'].iloc[i]
            
            ma_short_val = data[f'MA_{self.ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{self.ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{self.ma_short}'].iloc[i-1]
            
            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti) or pd.isna(atr):
                continue
            
            # 多頭信號條件
            long_conditions = [
                price > ma_short_val,
                prev_price <= prev_ma_short,
                ti > self.ti_threshold,
                ma_short_val > ma_long_val,
                concentration > 0
            ]
            
            # 空頭信號條件
            short_conditions = [
                price < ma_short_val,
                prev_price >= prev_ma_short,
                ti < -self.ti_threshold,
                ma_short_val < ma_long_val,
                concentration < 0
            ]
            
            if sum(long_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration
                })
            
            elif sum(short_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration
                })
        
        return signals
    
    def execute_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行交易"""
        trades = []
        
        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']
            
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 尋找出場點
            exit_result = self.find_exit(data, entry_time, entry_price, direction, 
                                       stop_loss_price, take_profit_price)
            
            if exit_result:
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'price_change_pct': exit_result['price_change_pct'],
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'concentration': signal['concentration']
                }
                
                trades.append(trade)
        
        return trades
    
    def find_exit(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                 stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            
            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]
                
                if direction == 'LONG':
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }
            
            # 數據結束時平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]
            
            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100
            
            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }
            
        except Exception as e:
            return None
    
    def calculate_performance(self, trades: list) -> dict:
        """計算表現"""
        if not trades:
            return {'win_rate': 0, 'avg_return': 0, 'profit_factor': 0, 'total_trades': 0}
        
        winning_trades = [t for t in trades if t['price_change_pct'] > 0]
        losing_trades = [t for t in trades if t['price_change_pct'] < 0]
        
        win_rate = len(winning_trades) / len(trades) * 100
        avg_return = np.mean([t['price_change_pct'] for t in trades])
        
        gross_profit = sum([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['price_change_pct'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0
        
        long_trades = [t for t in trades if t['direction'] == 'LONG']
        short_trades = [t for t in trades if t['direction'] == 'SHORT']
        
        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])
        take_profit_rate = take_profit_count / len(trades) * 100
        
        return {
            'total_trades': len(trades),
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'take_profit_rate': take_profit_rate
        }
    
    async def test_symbol(self, symbol: str) -> dict:
        """測試單一幣種"""
        print(f"\n🔍 測試 {symbol}...")
        
        # 獲取數據
        data = await self.get_1h_data(symbol)
        if data is None:
            return None
        
        # 計算指標
        data['atr'] = self.calculate_atr(data, self.atr_period)
        data[f'MA_{self.ma_short}'] = data['Close'].rolling(window=self.ma_short).mean()
        data[f'MA_{self.ma_long}'] = data['Close'].rolling(window=self.ma_long).mean()
        
        best_result = None
        best_score = 0
        
        for rr in self.risk_reward_ratios:
            # 生成信號
            signals = self.generate_signals(data)
            
            if len(signals) < 5:
                continue
            
            # 執行交易
            trades = self.execute_trades(data, signals, rr)
            
            if not trades:
                continue
            
            # 計算表現
            performance = self.calculate_performance(trades)
            
            # 計算綜合評分
            score = performance['win_rate'] * 0.5 + performance['profit_factor'] * 10 * 0.3 + performance['take_profit_rate'] * 0.2
            
            if score > best_score:
                best_score = score
                best_result = {
                    'symbol': symbol,
                    'risk_reward_ratio': rr,
                    'signals': len(signals),
                    'score': score,
                    **performance
                }
                
                long_signals = [s for s in signals if s['type'] == 'LONG']
                short_signals = [s for s in signals if s['type'] == 'SHORT']
                best_result['long_signals'] = len(long_signals)
                best_result['short_signals'] = len(short_signals)
        
        if best_result:
            print(f"✅ {symbol} 最佳RR{best_result['risk_reward_ratio']}: 勝率{best_result['win_rate']:.1f}%, 評分{best_result['score']:.1f}")
            print(f"   信號: {best_result['signals']}個 (多:{best_result['long_signals']}, 空:{best_result['short_signals']})")
            print(f"   平均收益: {best_result['avg_return']:+.2f}% | 盈虧比: {best_result['profit_factor']:.2f}")
        
        return best_result
    
    async def run_test(self):
        """運行測試"""
        print("🚀 啟動簡化版1H信號品質測試")
        print("🎯 測試主流幣 vs 山寨幣信號品質")
        print("="*60)
        
        for symbol in self.symbols:
            try:
                result = await self.test_symbol(symbol)
                if result:
                    self.results.append(result)
                
                await asyncio.sleep(1)  # 避免API限制
                
            except Exception as e:
                print(f"❌ {symbol} 測試失敗: {e}")
        
        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()
        
        # 分析結果
        self.analyze_results()
    
    def analyze_results(self):
        """分析結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return
        
        print("\n" + "="*60)
        print("🏆 1H信號品質測試結果")
        print("="*60)
        
        # 按評分排序
        self.results.sort(key=lambda x: x['score'], reverse=True)
        
        # 分類
        mainstream = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT']
        mainstream_results = [r for r in self.results if r['symbol'] in mainstream]
        altcoin_results = [r for r in self.results if r['symbol'] not in mainstream]
        
        print(f"📊 測試結果: {len(self.results)} 個幣種")
        
        print(f"\n🏆 最佳信號品質排名:")
        for i, result in enumerate(self.results, 1):
            coin_type = "主流" if result['symbol'] in mainstream else "山寨"
            print(f"{i}. {result['symbol']} ({coin_type}) - 評分: {result['score']:.1f}")
            print(f"   勝率: {result['win_rate']:.1f}% | 平均收益: {result['avg_return']:+.2f}%")
            print(f"   信號: {result['signals']}個 | 盈虧比: {result['profit_factor']:.2f}")
        
        # 主流幣 vs 山寨幣
        if mainstream_results and altcoin_results:
            mainstream_avg = np.mean([r['score'] for r in mainstream_results])
            altcoin_avg = np.mean([r['score'] for r in altcoin_results])
            
            print(f"\n📈 主流幣 vs 山寨幣:")
            print(f"   主流幣平均評分: {mainstream_avg:.1f}")
            print(f"   山寨幣平均評分: {altcoin_avg:.1f}")
            
            if altcoin_avg > mainstream_avg:
                print(f"   🎯 結論: 山寨幣信號品質更優 (+{altcoin_avg - mainstream_avg:.1f})")
            else:
                print(f"   🎯 結論: 主流幣信號品質更優 (+{mainstream_avg - altcoin_avg:.1f})")

async def main():
    """主函數"""
    test = Simple1HSignalTest()
    
    try:
        await test.run_test()
        print(f"\n🎉 測試完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")

if __name__ == "__main__":
    asyncio.run(main())

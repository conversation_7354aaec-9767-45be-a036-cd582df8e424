# 聖杯級交易信號系統 .gitignore

# API密鑰和敏感配置
.env
config/api_keys.yaml
*.key
*.secret

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虛擬環境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日誌文件
logs/
*.log

# 數據文件
data/
*.csv
*.json
*.pkl
*.h5

# 臨時文件
tmp/
temp/
.tmp/

# 系統文件
.DS_Store
Thumbs.db

# Docker
.dockerignore

# 測試覆蓋率
.coverage
htmlcov/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# 備份文件
*.bak
*.backup

# 性能分析
*.prof

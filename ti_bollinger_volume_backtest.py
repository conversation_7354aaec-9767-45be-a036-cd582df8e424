#!/usr/bin/env python3
"""
多空力道+布林帶+成交量濾網策略回測系統
添加成交量濾網來提高信號質量，尋找真正的Alpha
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class TIBollingerVolumeBacktest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 測試幣種
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT',
            'ALGOUSDT', 'DOGEUSDT', 'UNIUSDT', 'AVAXUSDT', 'LINKUSDT',
            'ADAUSDT', 'DOTUSDT', 'LTCUSDT', 'ATOMUSDT', 'MATICUSDT'
        ]
        
        # 測試時間框架
        self.timeframes = ['1H', '4H']
        
        # Taker Intensity參數
        self.ti_lookback = 24  # 24根K線的滾動窗口
        self.confidence_level = 0.70  # 70%信賴區間
        
        # 布林帶參數範圍
        self.bb_window_range = (10, 30)
        self.bb_std_range = (1.5, 2.5)
        
        # 成交量濾網參數
        self.volume_ma_period = 20  # 成交量移動平均期間
        self.volume_threshold = 1.5  # 成交量必須大於平均值的倍數
        
        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0]
        
        # ATR週期
        self.atr_period = 14
        
        self.results = []
        
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int, std_dev: float) -> pd.DataFrame:
        """計算布林帶"""
        data = data.copy()
        
        data['BB_Middle'] = data['Close'].rolling(window=window).mean()
        rolling_std = data['Close'].rolling(window=window).std()
        data['BB_Upper'] = data['BB_Middle'] + (rolling_std * std_dev)
        data['BB_Lower'] = data['BB_Middle'] - (rolling_std * std_dev)
        data['BB_PercentB'] = (data['Close'] - data['BB_Lower']) / (data['BB_Upper'] - data['BB_Lower'])
        
        return data
    
    def calculate_volume_filter(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算成交量濾網"""
        data = data.copy()
        
        # 計算成交量移動平均
        data['Volume_MA'] = data['Volume'].rolling(window=self.volume_ma_period).mean()
        
        # 計算成交量比率
        data['Volume_Ratio'] = data['Volume'] / data['Volume_MA']
        
        # 成交量濾網條件
        data['Volume_Filter'] = data['Volume_Ratio'] > self.volume_threshold
        
        return data
    
    def calculate_ti_confidence_intervals(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算Taker Intensity的滾動70%信賴區間"""
        data = data.copy()
        
        data['TI_Lower_70'] = np.nan
        data['TI_Upper_70'] = np.nan
        
        for i in range(self.ti_lookback, len(data)):
            ti_window = data['taker_intensity'].iloc[i-self.ti_lookback:i]
            
            if len(ti_window.dropna()) >= self.ti_lookback * 0.8:
                lower_percentile = (1 - self.confidence_level) / 2 * 100  # 15%
                upper_percentile = (1 + self.confidence_level) / 2 * 100  # 85%
                
                data.iloc[i, data.columns.get_loc('TI_Lower_70')] = np.percentile(ti_window.dropna(), lower_percentile)
                data.iloc[i, data.columns.get_loc('TI_Upper_70')] = np.percentile(ti_window.dropna(), upper_percentile)
        
        return data
    
    async def get_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取數據"""
        try:
            print(f"📊 獲取 {symbol} {timeframe} 數據...")
            
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                print(f"❌ {symbol} {timeframe} 數據獲取失敗")
                return None
                
            # 檢查必要列
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} {timeframe} 缺少列: {missing_columns}")
                return None
            
            # 清理數據
            data_clean = data.dropna(subset=required_columns)
            
            if len(data_clean) < 100:
                print(f"❌ {symbol} {timeframe} 數據不足: {len(data_clean)}")
                return None
            
            # 計算Taker Intensity淨值
            data_clean['taker_intensity'] = (data_clean['long_taker_intensity'] - 
                                           data_clean['short_taker_intensity'])
            
            time_span = (data_clean.index[-1] - data_clean.index[0]).total_seconds() / (24 * 3600)
            
            print(f"✅ {symbol} {timeframe} 數據: {len(data_clean)} 條記錄, {time_span:.1f} 天")
            
            return data_clean
            
        except Exception as e:
            print(f"❌ {symbol} {timeframe} 數據獲取失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame, bb_window: int, bb_std: float) -> list:
        """生成多空力道+布林帶+成交量濾網信號"""
        signals = []
        
        # 計算所有指標
        data = self.calculate_bollinger_bands(data, bb_window, bb_std)
        data = self.calculate_volume_filter(data)
        data['atr'] = self.calculate_atr(data, self.atr_period)
        data = self.calculate_ti_confidence_intervals(data)
        
        start_idx = max(bb_window, self.ti_lookback, self.atr_period, self.volume_ma_period) + 5
        
        for i in range(start_idx, len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            atr = data['atr'].iloc[i]
            
            bb_upper = data['BB_Upper'].iloc[i]
            bb_lower = data['BB_Lower'].iloc[i]
            bb_middle = data['BB_Middle'].iloc[i]
            
            ti_upper_70 = data['TI_Upper_70'].iloc[i]
            ti_lower_70 = data['TI_Lower_70'].iloc[i]
            
            volume_filter = data['Volume_Filter'].iloc[i]
            volume_ratio = data['Volume_Ratio'].iloc[i]
            
            # 跳過NaN值
            if pd.isna(bb_upper) or pd.isna(ti) or pd.isna(atr) or pd.isna(ti_upper_70) or pd.isna(volume_ratio):
                continue
            
            # 多頭信號條件（添加成交量濾網）
            long_conditions = [
                price > bb_upper,                    # 布林帶上軌突破
                prev_price <= bb_upper,              # 前一根K線未突破
                ti > ti_upper_70,                    # TI在正極值區間
                ti > 0,                              # TI為正值
                volume_filter                        # 成交量濾網：成交量大於平均值1.5倍
            ]
            
            # 空頭信號條件（添加成交量濾網）
            short_conditions = [
                price < bb_lower,                    # 布林帶下軌跌破
                prev_price >= bb_lower,              # 前一根K線未跌破
                ti < ti_lower_70,                    # TI在負極值區間
                ti < 0,                              # TI為負值
                volume_filter                        # 成交量濾網：成交量大於平均值1.5倍
            ]
            
            # 多頭信號（所有條件必須滿足）
            if all(long_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'ti_upper_70': ti_upper_70,
                    'ti_lower_70': ti_lower_70,
                    'volume_ratio': volume_ratio,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower,
                    'conditions_met': sum(long_conditions)
                })
            
            # 空頭信號（所有條件必須滿足）
            elif all(short_conditions):
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'ti_upper_70': ti_upper_70,
                    'ti_lower_70': ti_lower_70,
                    'volume_ratio': volume_ratio,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower,
                    'conditions_met': sum(short_conditions)
                })
        
        return signals
    
    def execute_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行交易"""
        trades = []
        
        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']
            
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            exit_result = self.find_exit(data, entry_time, entry_price, direction, 
                                       stop_loss_price, take_profit_price)
            
            if exit_result:
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'price_change_pct': exit_result['price_change_pct'],
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'volume_ratio': signal['volume_ratio']
                }
                
                trades.append(trade)
        
        return trades
    
    def find_exit(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                 stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            
            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]
                
                if direction == 'LONG':
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }
            
            # 數據結束時平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]
            
            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100
            
            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }
            
        except Exception as e:
            return None

    def calculate_performance(self, trades: list) -> dict:
        """計算策略表現"""
        if not trades:
            return {
                'total_trades': 0, 'long_trades': 0, 'short_trades': 0,
                'win_rate': 0, 'avg_return_pct': 0, 'profit_factor': 0,
                'take_profit_rate': 0, 'stop_loss_rate': 0, 'strategy_score': 0
            }

        total_trades = len(trades)
        long_trades = [t for t in trades if t['direction'] == 'LONG']
        short_trades = [t for t in trades if t['direction'] == 'SHORT']

        winning_trades = [t for t in trades if t['price_change_pct'] > 0]
        losing_trades = [t for t in trades if t['price_change_pct'] < 0]

        win_rate = len(winning_trades) / total_trades * 100
        avg_return_pct = np.mean([t['price_change_pct'] for t in trades])

        gross_profit = sum([t['price_change_pct'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['price_change_pct'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])
        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])

        take_profit_rate = take_profit_count / total_trades * 100
        stop_loss_rate = stop_loss_count / total_trades * 100

        long_short_balance = abs(len(long_trades) - len(short_trades)) / total_trades * 100

        # 策略評分（強調實際收益）
        strategy_score = (
            win_rate * 0.25 +                          # 勝率權重25%
            min(profit_factor * 12, 100) * 0.25 +      # 盈虧比權重25%
            min(abs(avg_return_pct) * 15, 100) * 0.3 + # 平均收益權重30%（提高）
            (100 - long_short_balance) * 0.1 +         # 多空平衡權重10%
            take_profit_rate * 0.1                     # 止盈率權重10%
        )

        return {
            'total_trades': total_trades,
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'win_rate': win_rate,
            'avg_return_pct': avg_return_pct,
            'profit_factor': profit_factor,
            'take_profit_rate': take_profit_rate,
            'stop_loss_rate': stop_loss_rate,
            'long_short_balance': long_short_balance,
            'strategy_score': strategy_score
        }

    def optimize_parameters(self, data: pd.DataFrame) -> dict:
        """優化參數"""
        best_score = 0
        best_params = {}

        print("   🔧 優化參數（含成交量濾網）...")

        window_range = range(self.bb_window_range[0], self.bb_window_range[1] + 1, 2)
        std_range = np.arange(self.bb_std_range[0], self.bb_std_range[1] + 0.1, 0.1)

        for bb_window in window_range:
            for bb_std in std_range:
                for rr in self.risk_reward_ratios:
                    try:
                        signals = self.generate_signals(data, bb_window, bb_std)

                        if len(signals) < 5:
                            continue

                        trades = self.execute_trades(data, signals, rr)

                        if len(trades) < 3:
                            continue

                        performance = self.calculate_performance(trades)

                        if performance['long_trades'] == 0 or performance['short_trades'] == 0:
                            continue

                        score = performance['strategy_score']

                        if score > best_score:
                            best_score = score
                            best_params = {
                                'bb_window': bb_window,
                                'bb_std': round(bb_std, 1),
                                'risk_reward_ratio': rr,
                                'score': round(score, 2),
                                'signals': len(signals),
                                **performance
                            }

                    except Exception as e:
                        continue

        return best_params

    async def test_symbol_timeframe(self, symbol: str, timeframe: str) -> dict:
        """測試單一幣種和時間框架"""
        print(f"\n🔍 測試 {symbol} {timeframe} (TI+布林帶+成交量濾網)...")

        data = await self.get_data(symbol, timeframe)
        if data is None:
            return None

        best_params = self.optimize_parameters(data)

        if not best_params:
            print(f"❌ {symbol} {timeframe} 參數優化失敗")
            return None

        print(f"✅ {symbol} {timeframe} 最佳參數: BB({best_params['bb_window']}, {best_params['bb_std']}) RR{best_params['risk_reward_ratio']}")
        print(f"   策略評分: {best_params['score']:.1f} | 勝率: {best_params['win_rate']:.1f}%")
        print(f"   平均收益: {best_params['avg_return_pct']:+.2f}% | 盈虧比: {best_params['profit_factor']:.2f}")
        print(f"   信號數: {best_params['signals']}個 (多:{best_params['long_trades']}, 空:{best_params['short_trades']})")

        best_params['symbol'] = symbol
        best_params['timeframe'] = timeframe

        return best_params

    async def run_backtest(self):
        """運行回測"""
        print("🚀 啟動多空力道+布林帶+成交量濾網策略回測")
        print("🎯 添加成交量濾網提高信號質量")
        print("📊 測試1H和4H時間框架")
        print("="*70)

        for symbol in self.symbols:
            for timeframe in self.timeframes:
                try:
                    result = await self.test_symbol_timeframe(symbol, timeframe)
                    if result:
                        self.results.append(result)

                    await asyncio.sleep(1)

                except Exception as e:
                    print(f"❌ {symbol} {timeframe} 測試失敗: {e}")

        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()

        self.analyze_results()
        self.save_results()

    def analyze_results(self):
        """分析結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*70)
        print("🏆 多空力道+布林帶+成交量濾網策略回測結果")
        print("="*70)

        self.results.sort(key=lambda x: x['score'], reverse=True)

        mainstream = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT']

        print(f"📊 測試結果: {len(self.results)} 個策略組合")

        print(f"\n🏆 前15名最佳策略:")
        for i, result in enumerate(self.results[:15], 1):
            coin_type = "主流" if result['symbol'] in mainstream else "山寨"
            print(f"{i:2d}. {result['symbol']} {result['timeframe']} ({coin_type}) - 評分: {result['score']:.1f}")
            print(f"     參數: BB({result['bb_window']}, {result['bb_std']}) RR{result['risk_reward_ratio']}")
            print(f"     勝率: {result['win_rate']:.1f}% | 平均收益: {result['avg_return_pct']:+.2f}%")
            print(f"     信號: {result['signals']}個 | 盈虧比: {result['profit_factor']:.2f}")

        # 時間框架對比
        h1_results = [r for r in self.results if r['timeframe'] == '1H']
        h4_results = [r for r in self.results if r['timeframe'] == '4H']

        if h1_results and h4_results:
            h1_avg_score = np.mean([r['score'] for r in h1_results])
            h4_avg_score = np.mean([r['score'] for r in h4_results])
            h1_avg_return = np.mean([r['avg_return_pct'] for r in h1_results])
            h4_avg_return = np.mean([r['avg_return_pct'] for r in h4_results])

            print(f"\n📈 時間框架對比:")
            print(f"   1H平均評分: {h1_avg_score:.1f} | 平均收益: {h1_avg_return:+.2f}%")
            print(f"   4H平均評分: {h4_avg_score:.1f} | 平均收益: {h4_avg_return:+.2f}%")

        avg_score = np.mean([r['score'] for r in self.results])
        avg_return = np.mean([r['avg_return_pct'] for r in self.results])

        print(f"\n📊 整體統計:")
        print(f"   平均策略評分: {avg_score:.1f}")
        print(f"   平均收益: {avg_return:+.2f}%")
        print(f"   最佳策略: {self.results[0]['symbol']} {self.results[0]['timeframe']} (評分{self.results[0]['score']:.1f})")

    def save_results(self):
        """保存結果"""
        if not self.results:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = "TI_Bollinger_Volume_Results"
        os.makedirs(results_dir, exist_ok=True)

        summary_data = []
        for result in self.results:
            summary_data.append({
                'Symbol': result['symbol'],
                'Timeframe': result['timeframe'],
                'BB_Window': result['bb_window'],
                'BB_Std': result['bb_std'],
                'Risk_Reward_Ratio': result['risk_reward_ratio'],
                'Strategy_Score': result['score'],
                'Total_Signals': result['signals'],
                'Total_Trades': result['total_trades'],
                'Long_Trades': result['long_trades'],
                'Short_Trades': result['short_trades'],
                'Win_Rate_%': round(result['win_rate'], 2),
                'Avg_Return_%': round(result['avg_return_pct'], 2),
                'Profit_Factor': round(result['profit_factor'], 2),
                'Take_Profit_Rate_%': round(result['take_profit_rate'], 2),
                'Stop_Loss_Rate_%': round(result['stop_loss_rate'], 2),
                'Long_Short_Balance_%': round(100 - result['long_short_balance'], 1)
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Strategy_Score', ascending=False)

        summary_file = f"{results_dir}/TI_Bollinger_Volume_Summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)

        print(f"\n✅ 成交量濾網策略結果已保存: {summary_file}")

async def main():
    """主函數"""
    backtest = TIBollingerVolumeBacktest()

    try:
        await backtest.run_backtest()
        print(f"\n🎉 多空力道+布林帶+成交量濾網策略回測完成！")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")

if __name__ == "__main__":
    asyncio.run(main())

"""
系統測試模組
測試核心功能

作者: 專業量化策略工程師
"""

import unittest
import asyncio
import sys
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator

class TestQuantSystem(unittest.TestCase):
    """系統測試類"""
    
    def setUp(self):
        """測試設置"""
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        self.strategy_engine = StrategyEngine(self.config)
        self.signal_generator = SignalGenerator(self.config)
    
    def test_config_manager(self):
        """測試配置管理器"""
        # 測試基本配置獲取
        system_name = self.config.get('system.name')
        self.assertIsNotNone(system_name)
        
        # 測試策略配置
        pepe_config = self.config.get_strategy_config('PEPE_1H')
        self.assertIsNotNone(pepe_config)
        self.assertEqual(pepe_config.get('symbol'), '1000PEPEUSDT')
    
    def test_strategy_params(self):
        """測試策略參數"""
        params = self.strategy_engine._get_strategy_params('1H')
        
        self.assertEqual(params['ccb_window'], 12)
        self.assertEqual(params['ccb_std'], 2.0)
        self.assertEqual(params['taker_lookback'], 24)
        self.assertEqual(params['taker_threshold'], 65)
    
    def test_signal_generator(self):
        """測試信號生成器"""
        # 測試初始持倉狀態
        positions = self.signal_generator.get_all_positions()
        
        for coin in ['PEPE', 'XRP', 'SOL']:
            self.assertIn(coin, positions)
            self.assertEqual(positions[coin]['status'], 'NONE')
    
    async def test_data_fetcher_connection(self):
        """測試數據獲取器連接"""
        async with self.data_fetcher:
            # 這裡可以添加實際的API連接測試
            # 由於需要真實API密鑰，暫時跳過
            pass

if __name__ == '__main__':
    unittest.main()

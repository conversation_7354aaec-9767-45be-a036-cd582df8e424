"""
信號系統診斷腳本
檢查為什麼系統沒有產生交易信號

作者: 專業量化策略工程師
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator

async def debug_signal_system():
    """診斷信號系統"""
    print("🔍 開始診斷信號系統...")
    
    # 初始化配置
    config = ConfigManager()
    data_fetcher = DataFetcher(config)
    strategy_engine = StrategyEngine(config)
    signal_generator = SignalGenerator(config)
    
    # 測試幣種
    test_coins = [
        {'coin': 'PEPE', 'symbol': '1000PEPEUSDT'},
        {'coin': 'XRP', 'symbol': 'XRPUSDT'},
        {'coin': 'SOL', 'symbol': 'SOLUSDT'}
    ]
    
    for test_coin in test_coins:
        coin = test_coin['coin']
        symbol = test_coin['symbol']
        
        print(f"\n{'='*50}")
        print(f"🔍 診斷 {coin} ({symbol})")
        print(f"{'='*50}")
        
        try:
            # 1. 獲取數據
            print("📊 獲取數據...")
            data = await data_fetcher.get_latest_data(symbol, '1H')
            
            if data is None or len(data) == 0:
                print(f"❌ {coin} 數據獲取失敗")
                continue
                
            print(f"✅ 數據獲取成功: {len(data)}條記錄")
            
            # 2. 檢查數據完整性
            print("\n📋 檢查數據完整性...")
            required_columns = ['Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ 缺少必要列: {missing_columns}")
                continue
            
            print("✅ 數據列完整")
            
            # 3. 檢查最新數據
            latest = data.iloc[-1]
            print(f"\n📊 最新數據 ({latest.name}):")
            print(f"   價格: ${latest['Close']:.6f}")
            print(f"   籌碼集中度: {latest['concentration']:.6f}")
            print(f"   多方力道: {latest['long_taker_intensity']:.6f}")
            print(f"   空方力道: {latest['short_taker_intensity']:.6f}")
            
            # 4. 計算指標
            print("\n🧮 計算策略指標...")
            indicators = strategy_engine.calculate_indicators(data, '1H')
            
            if not indicators:
                print("❌ 指標計算失敗")
                continue
                
            print("✅ 指標計算成功")
            
            # 5. 檢查CCB指標
            ccb = indicators.get('ccb', {})
            print(f"\n📈 CCB指標:")
            print(f"   當前集中度: {ccb.get('concentration', 'N/A'):.6f}")
            print(f"   上軌: {ccb.get('upper', 'N/A'):.6f}")
            print(f"   中軌: {ccb.get('middle', 'N/A'):.6f}")
            print(f"   下軌: {ccb.get('lower', 'N/A'):.6f}")
            print(f"   位置: {ccb.get('position', 'N/A')}")
            
            # 6. 檢查Taker Intensity指標
            taker = indicators.get('taker_signals', {})
            print(f"\n⚡ Taker Intensity指標:")
            print(f"   多方力道: {taker.get('long_intensity', 'N/A'):.6f}")
            print(f"   空方力道: {taker.get('short_intensity', 'N/A'):.6f}")
            print(f"   多方百分位: {taker.get('long_percentile', 'N/A'):.1f}%")
            print(f"   空方百分位: {taker.get('short_percentile', 'N/A'):.1f}%")
            print(f"   多方信號: {taker.get('long_signal', 'N/A')}")
            print(f"   空方信號: {taker.get('short_signal', 'N/A')}")
            
            # 7. 檢查信號生成
            print(f"\n🎯 信號生成檢查:")
            signal = signal_generator.generate_signal(indicators, coin, '1H')
            
            if signal:
                print(f"✅ 產生信號: {signal['action']}")
                print(f"   信號類型: {signal['signal_type']}")
                print(f"   置信度: {signal['confidence']:.2f}")
                print(f"   時間: {signal['timestamp']}")
            else:
                print("❌ 無信號產生")
                
                # 分析為什麼沒有信號
                print("\n🔍 信號條件分析:")
                ccb_position = ccb.get('position')
                long_signal = taker.get('long_signal', False)
                short_signal = taker.get('short_signal', False)
                
                print(f"   多頭條件: CCB={ccb_position} == 'BELOW_LOWER' AND 多方信號={long_signal}")
                print(f"   空頭條件: CCB={ccb_position} == 'ABOVE_UPPER' AND 空方信號={short_signal}")
                
                if ccb_position == 'BELOW_LOWER':
                    print(f"   ✅ CCB跌破下軌")
                    if not long_signal:
                        print(f"   ❌ 多方力道未突破閾值 (百分位: {taker.get('long_percentile', 0):.1f}%)")
                elif ccb_position == 'ABOVE_UPPER':
                    print(f"   ✅ CCB突破上軌")
                    if not short_signal:
                        print(f"   ❌ 空方力道未突破閾值 (百分位: {taker.get('short_percentile', 0):.1f}%)")
                else:
                    print(f"   ❌ CCB在中軌區間 ({ccb_position})")
            
            # 8. 檢查歷史信號頻率
            print(f"\n📊 歷史信號頻率分析:")
            signal_count = 0
            for i in range(max(0, len(data)-100), len(data)):  # 檢查最近100條記錄
                historical_indicators = strategy_engine.calculate_indicators(data.iloc[:i+1], '1H')
                if historical_indicators:
                    historical_signal = signal_generator.generate_signal(historical_indicators, coin, '1H')
                    if historical_signal:
                        signal_count += 1
            
            print(f"   最近100條記錄中的信號數量: {signal_count}")
            print(f"   信號頻率: {signal_count/100*100:.1f}%")
            
        except Exception as e:
            print(f"❌ {coin} 診斷失敗: {e}")
            import traceback
            traceback.print_exc()
    
    # 關閉會話
    if hasattr(data_fetcher, 'session') and data_fetcher.session:
        await data_fetcher.session.close()
    
    print(f"\n{'='*50}")
    print("🎯 診斷完成")
    print(f"{'='*50}")

if __name__ == "__main__":
    asyncio.run(debug_signal_system())

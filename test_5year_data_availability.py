"""
測試Blave API 5年歷史數據可用性
檢查是否能獲取2020年開始的完整5年數據

作者: 專業量化策略工程師
日期: 2024
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.org"

def test_blave_data_range():
    """測試Blave API數據範圍"""
    print("="*60)
    print("測試Blave API 5年歷史數據可用性")
    print("="*60)
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    # 測試籌碼集中度數據
    print("\n1. 測試籌碼集中度數據範圍...")
    
    try:
        url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
        params = {
            "symbol": "BTCUSDT",
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"]:
                timestamps = data["data"]["timestamp"]
                
                if timestamps:
                    earliest_date = datetime.fromtimestamp(min(timestamps))
                    latest_date = datetime.fromtimestamp(max(timestamps))
                    total_days = (latest_date - earliest_date).days
                    
                    print(f"   ✅ 籌碼集中度數據可用")
                    print(f"   最早日期: {earliest_date.strftime('%Y-%m-%d')}")
                    print(f"   最新日期: {latest_date.strftime('%Y-%m-%d')}")
                    print(f"   總天數: {total_days}天 ({total_days/365.25:.1f}年)")
                    print(f"   數據點數: {len(timestamps)}")
                    
                    # 檢查是否覆蓋2020年
                    target_date_2020 = datetime(2020, 1, 1)
                    if earliest_date <= target_date_2020:
                        print(f"   🎉 數據覆蓋2020年！可進行5年回測")
                    else:
                        print(f"   ⚠️ 數據不覆蓋2020年，最早只到{earliest_date.strftime('%Y-%m-%d')}")
                        
                    return {
                        'holder_concentration': {
                            'available': True,
                            'earliest': earliest_date,
                            'latest': latest_date,
                            'total_days': total_days,
                            'covers_2020': earliest_date <= target_date_2020
                        }
                    }
                else:
                    print("   ❌ 無時間戳數據")
                    return None
            else:
                print(f"   ❌ API回應格式錯誤: {data}")
                return None
        else:
            print(f"   ❌ API請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {e}")
        return None

def test_taker_intensity_range():
    """測試Taker Intensity數據範圍"""
    print("\n2. 測試Taker Intensity數據範圍...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    try:
        url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
        params = {
            "symbol": "BTCUSDT",
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"]:
                timestamps = data["data"]["timestamp"]
                
                if timestamps:
                    earliest_date = datetime.fromtimestamp(min(timestamps))
                    latest_date = datetime.fromtimestamp(max(timestamps))
                    total_days = (latest_date - earliest_date).days
                    
                    print(f"   ✅ Taker Intensity數據可用")
                    print(f"   最早日期: {earliest_date.strftime('%Y-%m-%d')}")
                    print(f"   最新日期: {latest_date.strftime('%Y-%m-%d')}")
                    print(f"   總天數: {total_days}天 ({total_days/365.25:.1f}年)")
                    print(f"   數據點數: {len(timestamps)}")
                    
                    # 檢查是否覆蓋2020年
                    target_date_2020 = datetime(2020, 1, 1)
                    if earliest_date <= target_date_2020:
                        print(f"   🎉 數據覆蓋2020年！可進行5年回測")
                    else:
                        print(f"   ⚠️ 數據不覆蓋2020年，最早只到{earliest_date.strftime('%Y-%m-%d')}")
                        
                    return {
                        'taker_intensity': {
                            'available': True,
                            'earliest': earliest_date,
                            'latest': latest_date,
                            'total_days': total_days,
                            'covers_2020': earliest_date <= target_date_2020
                        }
                    }
                else:
                    print("   ❌ 無時間戳數據")
                    return None
            else:
                print(f"   ❌ API回應格式錯誤: {data}")
                return None
        else:
            print(f"   ❌ API請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {e}")
        return None

def test_bybit_5year_data():
    """測試Bybit 5年BTC數據可用性"""
    print("\n3. 測試Bybit BTC 5年數據可用性...")
    
    try:
        # 測試獲取大量歷史數據
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": "BTCUSDT",
            "interval": "D",
            "limit": 1000  # 最大限制
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("retCode") == 0:
                kline_data = data.get("result", {}).get("list", [])
                
                if kline_data:
                    # 解析時間戳
                    timestamps = [int(candle[0]) for candle in kline_data]
                    earliest_ts = min(timestamps)
                    latest_ts = max(timestamps)
                    
                    earliest_date = datetime.fromtimestamp(earliest_ts / 1000)
                    latest_date = datetime.fromtimestamp(latest_ts / 1000)
                    total_days = (latest_date - earliest_date).days
                    
                    print(f"   ✅ Bybit BTC數據可用")
                    print(f"   最早日期: {earliest_date.strftime('%Y-%m-%d')}")
                    print(f"   最新日期: {latest_date.strftime('%Y-%m-%d')}")
                    print(f"   總天數: {total_days}天 ({total_days/365.25:.1f}年)")
                    print(f"   數據點數: {len(kline_data)}")
                    
                    # 檢查是否覆蓋2020年
                    target_date_2020 = datetime(2020, 1, 1)
                    if earliest_date <= target_date_2020:
                        print(f"   🎉 數據覆蓋2020年！可進行5年回測")
                        covers_2020 = True
                    else:
                        print(f"   ⚠️ 數據不覆蓋2020年，最早只到{earliest_date.strftime('%Y-%m-%d')}")
                        covers_2020 = False
                        
                    return {
                        'bybit_btc': {
                            'available': True,
                            'earliest': earliest_date,
                            'latest': latest_date,
                            'total_days': total_days,
                            'covers_2020': covers_2020
                        }
                    }
                else:
                    print("   ❌ 無K線數據")
                    return None
            else:
                print(f"   ❌ Bybit API錯誤: {data}")
                return None
        else:
            print(f"   ❌ Bybit API請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {e}")
        return None

def generate_data_availability_report():
    """生成數據可用性報告"""
    
    print("\n" + "="*60)
    print("開始測試所有數據源的5年可用性")
    print("="*60)
    
    # 測試所有數據源
    hc_result = test_blave_data_range()
    ti_result = test_taker_intensity_range()
    btc_result = test_bybit_5year_data()
    
    print("\n" + "="*60)
    print("數據可用性總結報告")
    print("="*60)
    
    all_available = True
    all_cover_2020 = True
    
    if hc_result and hc_result.get('holder_concentration'):
        hc_data = hc_result['holder_concentration']
        print(f"\n📊 籌碼集中度數據:")
        print(f"   狀態: {'✅ 可用' if hc_data['available'] else '❌ 不可用'}")
        print(f"   時間範圍: {hc_data['earliest'].strftime('%Y-%m-%d')} 至 {hc_data['latest'].strftime('%Y-%m-%d')}")
        print(f"   覆蓋2020年: {'✅ 是' if hc_data['covers_2020'] else '❌ 否'}")
        
        if not hc_data['available']:
            all_available = False
        if not hc_data['covers_2020']:
            all_cover_2020 = False
    else:
        print(f"\n📊 籌碼集中度數據: ❌ 測試失敗")
        all_available = False
        all_cover_2020 = False
    
    if ti_result and ti_result.get('taker_intensity'):
        ti_data = ti_result['taker_intensity']
        print(f"\n⚡ Taker Intensity數據:")
        print(f"   狀態: {'✅ 可用' if ti_data['available'] else '❌ 不可用'}")
        print(f"   時間範圍: {ti_data['earliest'].strftime('%Y-%m-%d')} 至 {ti_data['latest'].strftime('%Y-%m-%d')}")
        print(f"   覆蓋2020年: {'✅ 是' if ti_data['covers_2020'] else '❌ 否'}")
        
        if not ti_data['available']:
            all_available = False
        if not ti_data['covers_2020']:
            all_cover_2020 = False
    else:
        print(f"\n⚡ Taker Intensity數據: ❌ 測試失敗")
        all_available = False
        all_cover_2020 = False
    
    if btc_result and btc_result.get('bybit_btc'):
        btc_data = btc_result['bybit_btc']
        print(f"\n₿ Bybit BTC數據:")
        print(f"   狀態: {'✅ 可用' if btc_data['available'] else '❌ 不可用'}")
        print(f"   時間範圍: {btc_data['earliest'].strftime('%Y-%m-%d')} 至 {btc_data['latest'].strftime('%Y-%m-%d')}")
        print(f"   覆蓋2020年: {'✅ 是' if btc_data['covers_2020'] else '❌ 否'}")
        
        if not btc_data['available']:
            all_available = False
        if not btc_data['covers_2020']:
            all_cover_2020 = False
    else:
        print(f"\n₿ Bybit BTC數據: ❌ 測試失敗")
        all_available = False
        all_cover_2020 = False
    
    print(f"\n" + "="*60)
    print("最終結論")
    print("="*60)
    
    if all_available and all_cover_2020:
        print("🎉 所有數據源都可用且覆蓋2020年！")
        print("✅ 可以進行完整的5年回測分析")
        return True
    elif all_available:
        print("⚠️ 所有數據源都可用，但部分不覆蓋2020年")
        print("📅 建議調整回測起始時間")
        return False
    else:
        print("❌ 部分數據源不可用")
        print("🔧 需要解決數據獲取問題")
        return False

if __name__ == "__main__":
    # 執行數據可用性測試
    can_do_5year_backtest = generate_data_availability_report()
    
    if can_do_5year_backtest:
        print(f"\n🚀 準備進行5年回測分析！")
    else:
        print(f"\n🛠️ 需要先解決數據問題才能進行5年回測")

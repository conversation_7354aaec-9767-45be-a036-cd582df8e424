"""
籌碼集中帶突破策略 - 最終測試版本
使用合理的模擬數據，加入ATR-based止盈止損機制

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入改進後的策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, ccb_entry_logic_with_atr, 
    optimize_ccb_parameters_comprehensive, calculate_performance_metrics
)

def generate_realistic_btc_data():
    """生成合理的BTC歷史數據"""
    print("生成合理的BTC歷史數據...")
    
    # 生成時間序列（過去2年，4H間隔）
    end_date = datetime.now()
    start_date = end_date - pd.Timedelta(days=730)
    dates = pd.date_range(start=start_date, end=end_date, freq='4H')
    
    np.random.seed(42)  # 確保結果可重現
    
    # 基於真實BTC歷史價格範圍生成數據
    initial_price = 25000  # 合理的起始價格
    n_periods = len(dates)
    
    # 生成更真實的價格走勢
    # 模擬2022-2024年BTC的大致走勢
    price_phases = [
        (0, 0.3, -0.4),      # 2022年熊市下跌
        (0.3, 0.6, 0.2),     # 2023年初反彈
        (0.6, 0.8, 0.6),     # 2023年中大漲
        (0.8, 1.0, 0.1)      # 2024年穩定
    ]
    
    prices = [initial_price]
    
    for i in range(1, n_periods):
        progress = i / n_periods
        
        # 確定當前階段
        current_trend = 0
        for start, end, trend in price_phases:
            if start <= progress < end:
                current_trend = trend
                break
        
        # 基礎波動率
        base_vol = 0.012  # 4H級別基礎波動率
        
        # 添加週期性波動
        cycle_factor = 0.003 * np.sin(2 * np.pi * i / (24*7))  # 週週期
        
        # 生成收益率
        trend_component = current_trend * 0.0001  # 趨勢成分
        random_component = np.random.normal(0, base_vol)
        
        total_return = trend_component + random_component + cycle_factor
        
        # 計算新價格
        new_price = prices[-1] * (1 + total_return)
        
        # 確保價格在合理範圍內
        new_price = max(15000, min(70000, new_price))
        prices.append(new_price)
    
    # 生成高低價和成交量
    highs = []
    lows = []
    volumes = []
    
    for i, price in enumerate(prices):
        # 當日波動率
        daily_vol = abs(np.random.normal(0, 0.008)) + 0.003
        
        high = price * (1 + daily_vol)
        low = price * (1 - daily_vol)
        volume = np.random.uniform(500, 2000)  # 模擬成交量
        
        highs.append(high)
        lows.append(low)
        volumes.append(volume)
    
    # 創建DataFrame
    df = pd.DataFrame({
        'time': dates,
        'open': prices,  # 簡化處理
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes
    })
    
    print(f"BTC數據生成完成：{len(df)}條記錄")
    print(f"時間範圍：{df['time'].min()} 至 {df['time'].max()}")
    print(f"價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    
    return df

def generate_realistic_chip_concentration(price_df):
    """生成合理的籌碼集中度數據"""
    print("生成籌碼集中度數據...")
    
    df = price_df.copy()
    
    # 計算技術指標
    df['returns'] = df['close'].pct_change()
    df['volatility'] = df['returns'].rolling(24).std().fillna(0.015)
    df['rsi'] = calculate_rsi(df['close'], 14)
    df['volume_ma'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    np.random.seed(42)
    
    # 基礎籌碼集中度
    base_concentration = 0.5
    concentration = [base_concentration]
    
    for i in range(1, len(df)):
        prev_conc = concentration[-1]
        
        # 價格變化影響（價格大漲時籌碼分散）
        price_impact = -df.iloc[i]['returns'] * 1.5 if not pd.isna(df.iloc[i]['returns']) else 0
        
        # 波動率影響（高波動時籌碼變化更劇烈）
        vol_impact = df.iloc[i]['volatility'] * 0.8 if not pd.isna(df.iloc[i]['volatility']) else 0
        
        # RSI影響（超買時籌碼集中，超賣時分散）
        rsi_val = df.iloc[i]['rsi']
        if not pd.isna(rsi_val):
            if rsi_val > 70:
                rsi_impact = 0.01  # 超買，籌碼集中
            elif rsi_val < 30:
                rsi_impact = -0.01  # 超賣，籌碼分散
            else:
                rsi_impact = 0
        else:
            rsi_impact = 0
        
        # 成交量影響（大成交量時籌碼變化更大）
        volume_impact = (df.iloc[i]['volume_ratio'] - 1) * 0.005 if not pd.isna(df.iloc[i]['volume_ratio']) else 0
        
        # 隨機噪音
        noise = np.random.normal(0, 0.015)
        
        # 均值回歸（防止極端值）
        mean_reversion = (0.5 - prev_conc) * 0.02
        
        # 計算新的籌碼集中度
        new_conc = prev_conc + price_impact + vol_impact + rsi_impact + volume_impact + noise + mean_reversion
        
        # 限制在合理範圍
        new_conc = np.clip(new_conc, 0.1, 0.9)
        concentration.append(new_conc)
    
    df['concentration'] = concentration
    
    # 添加一些市場事件（極端籌碼集中或分散）
    market_events = np.random.choice(range(100, len(df)-50), size=10, replace=False)
    for event_idx in market_events:
        event_type = np.random.choice(['concentration', 'dispersion'])
        duration = np.random.randint(3, 8)
        
        if event_type == 'concentration':
            # 籌碼集中事件（大戶吸籌）
            target_level = np.random.uniform(0.75, 0.85)
            for j in range(duration):
                if event_idx + j < len(df):
                    df.iloc[event_idx + j, df.columns.get_loc('concentration')] = target_level + np.random.normal(0, 0.02)
        else:
            # 籌碼分散事件（散戶湧入）
            target_level = np.random.uniform(0.15, 0.25)
            for j in range(duration):
                if event_idx + j < len(df):
                    df.iloc[event_idx + j, df.columns.get_loc('concentration')] = target_level + np.random.normal(0, 0.02)
    
    # 再次限制範圍
    df['concentration'] = np.clip(df['concentration'], 0.1, 0.9)
    
    print(f"籌碼集中度範圍：{df['concentration'].min():.3f} - {df['concentration'].max():.3f}")
    print(f"籌碼集中度平均值：{df['concentration'].mean():.3f}")
    
    return df

def calculate_rsi(prices, period=14):
    """計算RSI指標"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def run_final_strategy_test():
    """運行最終策略測試"""
    
    print("="*60)
    print("籌碼集中帶突破策略 - 最終測試")
    print("使用合理的模擬數據 + ATR止盈止損")
    print("="*60)
    
    # 1. 生成合理的市場數據
    print("\n階段1：數據準備")
    print("-" * 30)
    btc_data = generate_realistic_btc_data()
    market_data = generate_realistic_chip_concentration(btc_data)
    market_data.set_index('time', inplace=True)
    
    # 2. 分割數據
    split_point = int(len(market_data) * 0.7)
    train_data = market_data.iloc[:split_point].copy()
    test_data = market_data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄")
    print(f"測試數據：{len(test_data)}條記錄")
    
    # 3. 參數優化
    print("\n階段2：參數優化")
    print("-" * 30)
    
    best_params, optimization_results = optimize_ccb_parameters_comprehensive(
        train_data,
        lookback_range=(10, 30),  # 縮小範圍加快速度
        std_range=(1.5, 2.5),
        atr_range=(0.8, 1.5),
        optimization_metric='calmar'
    )
    
    print(f"\n最優參數：{best_params}")
    
    # 4. 樣本外測試
    print("\n階段3：樣本外測試")
    print("-" * 30)
    
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_entry_logic_with_atr(
        test_df,
        atr_multiplier=best_params['atr_multiplier']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 最終策略績效 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 出場原因分析
    exit_reasons = test_df['Exit_Reason'].value_counts()
    if len(exit_reasons) > 0:
        print(f"\n=== 出場原因分析 ===")
        total_exits = exit_reasons.sum()
        for reason, count in exit_reasons.items():
            if reason:
                percentage = (count / total_exits) * 100
                print(f"{reason}: {count}次 ({percentage:.1f}%)")
    
    # 5. 可視化結果
    print("\n階段4：結果可視化")
    print("-" * 30)
    plot_final_results(test_df, best_params, performance)
    
    return test_df, best_params, performance

def plot_final_results(df, params, performance):
    """繪製最終結果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 價格和籌碼集中帶
    ax1 = axes[0, 0]
    ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--', alpha=0.8)
    ax1_twin.plot(df.index, df['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-', alpha=0.8)
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--', alpha=0.8)
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=30, label='Buy', alpha=0.7)
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=30, label='Sell', alpha=0.7)
    
    ax1.set_title(f'籌碼集中帶策略 (窗口={params["window"]}, 標準差={params["std_dev"]}, ATR={params["atr_multiplier"]})')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 累積收益比較
    axes[0, 1].plot(df.index, df['cumulative_strategy'], label='CCB策略', color='blue', linewidth=2)
    axes[0, 1].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 回撤分析
    strategy_cumulative = df['cumulative_strategy']
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    axes[1, 0].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')
    axes[1, 0].plot(df.index, drawdown, color='red', linewidth=1)
    axes[1, 0].set_title('策略回撤分析')
    axes[1, 0].set_ylabel('回撤幅度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 績效指標總結
    axes[1, 1].axis('off')
    
    # 創建績效指標文本
    perf_text = "=== 策略績效總結 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        perf_text += f"{key}: {value}\n"
    
    axes[1, 1].text(0.1, 0.9, perf_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("圖表已生成完成！")

if __name__ == "__main__":
    # 執行最終策略測試
    results = run_final_strategy_test()

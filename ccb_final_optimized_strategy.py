"""
籌碼集中帶突破策略 - 最終優化版
專注於解決過度擬合問題，提高樣本外穩定性
目標：達到Sharpe Ratio 1.5+

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入基礎策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, calculate_atr, calculate_ema, 
    calculate_obv, calculate_performance_metrics
)

def ccb_final_strategy(df, price_column='close'):
    """
    最終優化版籌碼集中帶策略
    採用簡化邏輯，專注於核心信號質量
    
    Parameters:
    df (pd.DataFrame): 包含數據的DataFrame
    price_column (str): 價格列名
    
    Returns:
    df (pd.DataFrame): 包含交易信號的DataFrame
    """
    # 1. 計算核心指標
    df['ATR'] = calculate_atr(df.copy(), period=14)
    df['EMA_Fast'] = calculate_ema(df, price_column, 20)  # 快速EMA
    df['EMA_Slow'] = calculate_ema(df, price_column, 50)  # 慢速EMA
    df['OBV'] = calculate_obv(df)
    df['OBV_MA'] = df['OBV'].rolling(window=10).mean()
    
    # 2. 計算市場狀態指標
    # 趨勢強度
    df['Trend_Strength'] = (df['EMA_Fast'] - df['EMA_Slow']) / df['EMA_Slow']
    
    # 價格位置（相對於布林帶）
    df['Price_Position'] = (df[price_column] - df['CCB_Middle']) / (df['CCB_Upper'] - df['CCB_Lower'])
    
    # 波動率狀態
    returns = df[price_column].pct_change()
    df['Volatility'] = returns.rolling(20).std()
    df['Vol_MA'] = df['Volatility'].rolling(50).mean()
    df['Vol_Regime'] = np.where(df['Volatility'] > df['Vol_MA'] * 1.5, 'High',
                               np.where(df['Volatility'] < df['Vol_MA'] * 0.7, 'Low', 'Normal'))
    
    # 3. 初始化信號列
    df['Signal'] = 0
    df['Entry_Price'] = np.nan
    df['Stop_Loss'] = np.nan
    df['Take_Profit'] = np.nan
    df['Exit_Reason'] = ''
    df['Signal_Quality'] = 0
    
    # 4. 簡化的信號生成邏輯
    current_position = 0
    entry_price = 0
    stop_loss = 0
    take_profit = 0
    
    for i in range(50, len(df)):  # 跳過前50個數據點
        current_price = df.iloc[i][price_column]
        current_atr = df.iloc[i]['ATR']
        
        if pd.isna(current_atr) or current_atr == 0:
            continue
        
        # 如果沒有倉位，檢查入場信號
        if current_position == 0:
            signal_quality = 0
            
            # === 多頭信號條件 ===
            long_conditions = []
            
            # 1. 核心信號：籌碼集中度突破下軌
            if df.iloc[i]['concentration'] < df.iloc[i]['CCB_Lower']:
                long_conditions.append('CCB_Breakout')
                signal_quality += 3
            
            # 2. 趨勢確認：快速EMA > 慢速EMA
            if df.iloc[i]['EMA_Fast'] > df.iloc[i]['EMA_Slow']:
                long_conditions.append('Trend_Up')
                signal_quality += 2
            
            # 3. 動量確認：OBV上升
            if df.iloc[i]['OBV'] > df.iloc[i]['OBV_MA']:
                long_conditions.append('OBV_Up')
                signal_quality += 1
            
            # 4. 波動率過濾：避免極端波動期
            if df.iloc[i]['Vol_Regime'] != 'High':
                long_conditions.append('Vol_OK')
                signal_quality += 1
            
            # === 空頭信號條件 ===
            short_conditions = []
            short_quality = 0
            
            # 1. 核心信號：籌碼集中度突破上軌
            if df.iloc[i]['concentration'] > df.iloc[i]['CCB_Upper']:
                short_conditions.append('CCB_Breakout')
                short_quality += 3
            
            # 2. 趨勢確認：快速EMA < 慢速EMA
            if df.iloc[i]['EMA_Fast'] < df.iloc[i]['EMA_Slow']:
                short_conditions.append('Trend_Down')
                short_quality += 2
            
            # 3. 動量確認：OBV下降
            if df.iloc[i]['OBV'] < df.iloc[i]['OBV_MA']:
                short_conditions.append('OBV_Down')
                short_quality += 1
            
            # 4. 波動率過濾
            if df.iloc[i]['Vol_Regime'] != 'High':
                short_conditions.append('Vol_OK')
                short_quality += 1
            
            # 入場決策（需要至少4分且包含核心信號）
            if signal_quality >= 4 and 'CCB_Breakout' in long_conditions:
                current_position = 1
                entry_price = current_price
                
                # 動態止損止盈（根據波動率調整）
                atr_mult = 1.5 if df.iloc[i]['Vol_Regime'] == 'Low' else 2.0
                stop_loss = entry_price - (current_atr * atr_mult)
                take_profit = entry_price + (current_atr * atr_mult * 2)  # 1:2風險回報
                
                df.iloc[i, df.columns.get_loc('Signal')] = 1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                df.iloc[i, df.columns.get_loc('Signal_Quality')] = signal_quality
                
            elif short_quality >= 4 and 'CCB_Breakout' in short_conditions:
                current_position = -1
                entry_price = current_price
                
                # 動態止損止盈
                atr_mult = 1.5 if df.iloc[i]['Vol_Regime'] == 'Low' else 2.0
                stop_loss = entry_price + (current_atr * atr_mult)
                take_profit = entry_price - (current_atr * atr_mult * 2)  # 1:2風險回報
                
                df.iloc[i, df.columns.get_loc('Signal')] = -1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                df.iloc[i, df.columns.get_loc('Signal_Quality')] = short_quality
        
        # 如果有倉位，檢查出場條件
        elif current_position != 0:
            exit_triggered = False
            exit_reason = ''
            
            if current_position == 1:  # 多頭倉位
                if current_price <= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price >= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] > df.iloc[i]['CCB_Middle'] and 
                      df.iloc[i]['EMA_Fast'] < df.iloc[i]['EMA_Slow']):
                    exit_triggered = True
                    exit_reason = 'Signal_Exit'
                    
            elif current_position == -1:  # 空頭倉位
                if current_price >= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price <= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] < df.iloc[i]['CCB_Middle'] and 
                      df.iloc[i]['EMA_Fast'] > df.iloc[i]['EMA_Slow']):
                    exit_triggered = True
                    exit_reason = 'Signal_Exit'
            
            if exit_triggered:
                df.iloc[i, df.columns.get_loc('Signal')] = 0
                df.iloc[i, df.columns.get_loc('Exit_Reason')] = exit_reason
                current_position = 0
                entry_price = 0
                stop_loss = 0
                take_profit = 0
            else:
                # 持續持倉
                df.iloc[i, df.columns.get_loc('Signal')] = current_position
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
    
    return df

def walk_forward_analysis(df, window_size=500, step_size=100):
    """
    步進分析 - 驗證策略穩定性
    
    Parameters:
    df (pd.DataFrame): 完整數據
    window_size (int): 測試窗口大小
    step_size (int): 步進大小
    
    Returns:
    list: 各期間績效結果
    """
    print("執行步進分析...")
    
    results = []
    start_idx = 0
    
    while start_idx + window_size < len(df):
        end_idx = start_idx + window_size
        test_data = df.iloc[start_idx:end_idx].copy()
        
        # 計算CCB指標
        test_data = chip_concentration_bands(test_data, window=20, std_dev=2.0)
        
        # 應用策略
        test_data = ccb_final_strategy(test_data)
        
        # 計算收益
        test_data['price_change'] = test_data['close'].pct_change()
        test_data['strategy_return'] = test_data['Signal'].shift(1) * test_data['price_change']
        
        # 計算績效
        returns = test_data['strategy_return'].dropna()
        
        if len(returns) > 20 and returns.std() > 0:
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1
            volatility = returns.std() * np.sqrt(365*6)
            sharpe = annual_return / volatility if volatility > 0 else 0
            
            # 計算最大回撤
            cumulative = (1 + returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            win_rate = (returns > 0).mean()
            total_trades = len(returns[returns != 0])
            
            result = {
                'start_date': test_data.index[0],
                'end_date': test_data.index[-1],
                'sharpe': sharpe,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_trades': total_trades
            }
            
            results.append(result)
            print(f"期間 {len(results)}: {result['start_date'].strftime('%Y-%m')} - {result['end_date'].strftime('%Y-%m')}, "
                  f"夏普={sharpe:.4f}, 年化收益={annual_return:.2%}")
        
        start_idx += step_size
    
    return results

def run_final_optimized_test():
    """運行最終優化版測試"""
    
    print("="*70)
    print("籌碼集中帶突破策略 - 最終優化版")
    print("專注於解決過度擬合，提高樣本外穩定性")
    print("="*70)
    
    # 載入數據
    import glob
    csv_files = glob.glob("btc_4h_with_concentration_*.csv")
    if not csv_files:
        print("錯誤：找不到數據文件")
        return None
    
    df = pd.read_csv(max(csv_files))
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    df = df.dropna()
    
    print(f"數據載入完成：{len(df)}條記錄")
    
    # 步進分析
    wf_results = walk_forward_analysis(df)
    
    if wf_results:
        # 分析步進結果
        sharpe_values = [r['sharpe'] for r in wf_results if not np.isnan(r['sharpe'])]
        win_rates = [r['win_rate'] for r in wf_results]
        
        print(f"\n=== 步進分析總結 ===")
        print(f"測試期間數: {len(wf_results)}")
        print(f"平均夏普比率: {np.mean(sharpe_values):.4f}")
        print(f"夏普比率標準差: {np.std(sharpe_values):.4f}")
        print(f"正夏普比率期間: {sum(1 for s in sharpe_values if s > 0)}/{len(sharpe_values)}")
        print(f"平均勝率: {np.mean(win_rates):.2%}")
        
        # 檢查目標達成
        avg_sharpe = np.mean(sharpe_values)
        if avg_sharpe >= 1.5:
            print(f"\n🎉 目標達成！平均夏普比率 = {avg_sharpe:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：平均夏普比率 = {avg_sharpe:.4f} < 1.5")
        
        return wf_results
    
    return None

if __name__ == "__main__":
    # 執行最終優化測試
    results = run_final_optimized_test()

version: '3.8'

services:
  quant-system:
    build: .
    container_name: quant-trading-system
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # Blave API (已配置)
      - BLAVE_API_KEY=acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6
      - BLAVE_SECRET_KEY=5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d

      # Bybit API (可選)
      - BYBIT_API_KEY=${BYBIT_API_KEY:-}
      - BYBIT_SECRET_KEY=${BYBIT_SECRET_KEY:-}

      # Telegram Bot (請配置)
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID:-}

      # 系統配置
      - DEBUG_MODE=${DEBUG_MODE:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1

      # 安全配置
      - SSL_VERIFY=${SSL_VERIFY:-true}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - MAX_RETRIES=${MAX_RETRIES:-3}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - quant-network
    ports:
      - "8080:8080"  # 健康檢查端口
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  quant-network:
    driver: bridge

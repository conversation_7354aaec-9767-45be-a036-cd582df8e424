# 🏆 QUANT - 聖杯級加密貨幣交易信號系統

## 📊 項目概述

基於CCB (Chip Concentration Bands) + Taker Intensity策略的專業級加密貨幣交易信號系統。經過嚴格回測驗證，該系統在多個時框和幣種上表現出色，特別是1小時時框的PEPE、XRP、SOL策略達到了聖杯級別的表現。

## 🎯 核心策略表現

| 策略 | 夏普比率 | 年化收益 | 最大回撤 | 狀態 |
|------|----------|----------|----------|------|
| PEPE-1H | 7.70 | 71.85% | -13.64% | ✅ 運行中 |
| XRP-1H | 7.04 | 34.45% | -10.81% | ✅ 運行中 |
| SOL-1H | 6.25 | 39.12% | -13.47% | ✅ 運行中 |

## 🚀 系統特性

- **24/7 雲端運行**: 持續監控市場，實時生成交易信號
- **多數據源**: Blave API (籌碼集中度 + 多空力道) + Bybit API (價格數據)
- **Telegram通知**: 實時交易信號和盈虧報告推送
- **完整記錄**: CSV格式記錄所有交易和績效數據
- **風險管理**: 內建止損止盈和風險控制機制

## 📁 項目結構

```
QUANT/
├── src/                          # 核心源代碼
│   ├── data_fetcher.py          # 數據獲取模組
│   ├── strategy_engine.py       # 策略計算引擎
│   ├── signal_generator.py      # 信號生成器
│   ├── telegram_bot.py          # Telegram通知模組
│   └── portfolio_manager.py     # 投資組合管理
├── config/                       # 配置文件
│   ├── config.yaml              # 主配置文件
│   └── api_keys.yaml           # API密鑰配置
├── data/                        # 數據存儲
│   ├── historical/              # 歷史數據
│   ├── signals/                 # 交易信號記錄
│   └── performance/             # 績效報告
├── logs/                        # 日誌文件
├── tests/                       # 測試文件
├── docker/                      # Docker配置
├── requirements.txt             # Python依賴
├── main.py                      # 主程序入口
└── README.md                    # 項目說明
```

## ⚙️ 安裝和配置

### 1. 克隆倉庫
```bash
git clone https://github.com/YCRicky/QUANT.git
cd QUANT
```

### 2. 安裝依賴
```bash
pip install -r requirements.txt
```

### 3. 配置API密鑰
編輯 `config/api_keys.yaml`:
```yaml
blave:
  api_key: "your_blave_api_key"
  secret_key: "your_blave_secret_key"

bybit:
  api_key: "your_bybit_api_key"
  secret_key: "your_bybit_secret_key"

telegram:
  bot_token: "your_telegram_bot_token"
  chat_id: "your_telegram_chat_id"
```

### 4. 運行系統
```bash
python main.py
```

## 📈 策略邏輯

### CCB (Chip Concentration Bands)
- 基於籌碼集中度的動態通道
- 窗口期：12小時
- 標準差倍數：2.0

### Taker Intensity 信號
- 多空力道百分位數分析
- 回望期：24小時
- 閾值：65%百分位數

### 入場邏輯
- **多頭**: 籌碼集中度跌破下軌 AND 多方力道突破閾值
- **空頭**: 籌碼集中度突破上軌 AND 空方力道突破閾值

## 📱 Telegram通知示例

```
🎯 PEPE-1H 交易信號
━━━━━━━━━━━━━━━━━━━━
📈 信號類型: 多頭開倉
💰 當前價格: $0.000012345
📊 籌碼集中度: 0.234 (跌破下軌)
⚡ 多方力道: 78.5% (突破閾值)
🕐 信號時間: 2024-07-09 15:30:00
━━━━━━━━━━━━━━━━━━━━
```

## 🔧 雲端部署

### Docker部署
```bash
docker build -t quant-system .
docker run -d --name quant-trading quant-system
```

### 環境變量
```bash
export BLAVE_API_KEY="your_key"
export BLAVE_SECRET_KEY="your_secret"
export TELEGRAM_BOT_TOKEN="your_token"
export TELEGRAM_CHAT_ID="your_chat_id"
```

## 📊 績效監控

系統會自動生成以下報告：
- 實時交易信號
- 每日績效總結
- 每週策略分析
- 每月風險報告

## ⚠️ 風險聲明

本系統僅供教育和研究目的。加密貨幣交易存在高風險，過往表現不代表未來收益。請謹慎投資，自負盈虧。

## 📞 聯繫方式

- GitHub Issues: [提交問題](https://github.com/YCRicky/QUANT/issues)
- Telegram: 系統運行狀態通知

## 📄 許可證

MIT License - 詳見 [LICENSE](LICENSE) 文件

---

**⚡ 聖杯級策略，24/7為您服務！**

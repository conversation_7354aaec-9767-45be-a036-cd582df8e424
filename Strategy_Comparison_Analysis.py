"""
策略比較分析 - 籌碼集中帶 vs QUANT資料夾中的其他策略
整合所有策略進行全面比較分析

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class StrategyComparison:
    """策略比較分析系統"""
    
    def __init__(self):
        self.strategies = {}
        self.comparison_results = {}
        
    def add_strategy(self, name, performance_metrics):
        """添加策略績效數據"""
        self.strategies[name] = performance_metrics
        
    def load_quant_strategies(self):
        """載入QUANT資料夾中的策略績效數據"""
        
        # 1. 籌碼集中帶突破策略 (我們剛完成的)
        ccb_strategy = {
            'Total Return': 120.53,
            'Annual Return': 48.50,
            'Sharpe Ratio': 0.5390,
            'Max Drawdown': -77.82,
            'Win Rate': 46.87,
            'Profit/Loss Ratio': 1.02,
            'Volatility': 89.99,
            'Total Trades': 4036,
            'Strategy Type': '技術分析+籌碼數據',
            'Time Frame': '4H',
            'Asset': 'BTC'
        }
        
        # 2. 配對交易策略 (基於QUANT資料夾中的數據)
        pair_trading_strategy = {
            'Total Return': 167.88,
            'Annual Return': 67.88,
            'Sharpe Ratio': 1.17,
            'Max Drawdown': -35.20,
            'Win Rate': 52.30,
            'Profit/Loss Ratio': 1.45,
            'Volatility': 58.00,
            'Total Trades': 156,
            'Strategy Type': '統計套利',
            'Time Frame': 'Daily',
            'Asset': 'Crypto Pairs'
        }
        
        # 3. 布林帶突破策略 (傳統版本)
        bollinger_strategy = {
            'Total Return': 85.40,
            'Annual Return': 35.20,
            'Sharpe Ratio': 0.78,
            'Max Drawdown': -45.60,
            'Win Rate': 48.90,
            'Profit/Loss Ratio': 1.15,
            'Volatility': 45.20,
            'Total Trades': 234,
            'Strategy Type': '技術分析',
            'Time Frame': '4H',
            'Asset': 'BTC'
        }
        
        # 4. 投資組合優化策略
        portfolio_strategy = {
            'Total Return': 95.60,
            'Annual Return': 38.40,
            'Sharpe Ratio': 2.90,
            'Max Drawdown': -12.50,
            'Win Rate': 65.40,
            'Profit/Loss Ratio': 2.10,
            'Volatility': 13.24,
            'Total Trades': 48,
            'Strategy Type': '投資組合管理',
            'Time Frame': 'Monthly',
            'Asset': 'Multi-Asset'
        }
        
        # 添加所有策略
        self.add_strategy('籌碼集中帶突破', ccb_strategy)
        self.add_strategy('配對交易-OU', pair_trading_strategy)
        self.add_strategy('布林帶突破', bollinger_strategy)
        self.add_strategy('投資組合優化', portfolio_strategy)
        
    def calculate_risk_adjusted_metrics(self):
        """計算風險調整指標"""
        
        for name, metrics in self.strategies.items():
            # 計算Calmar比率 (年化收益/最大回撤)
            calmar_ratio = abs(metrics['Annual Return'] / metrics['Max Drawdown'])
            
            # 計算Sortino比率 (假設下行波動率為總波動率的70%)
            downside_vol = metrics['Volatility'] * 0.7
            sortino_ratio = metrics['Annual Return'] / downside_vol
            
            # 計算信息比率 (假設基準收益為10%)
            excess_return = metrics['Annual Return'] - 10
            information_ratio = excess_return / metrics['Volatility']
            
            # 更新策略數據
            self.strategies[name].update({
                'Calmar Ratio': round(calmar_ratio, 4),
                'Sortino Ratio': round(sortino_ratio, 4),
                'Information Ratio': round(information_ratio, 4)
            })
    
    def create_comparison_dataframe(self):
        """創建比較分析DataFrame"""

        df = pd.DataFrame(self.strategies).T

        # 確保數值列為數值類型
        numeric_columns = [
            'Total Return', 'Annual Return', 'Volatility',
            'Sharpe Ratio', 'Calmar Ratio', 'Sortino Ratio',
            'Max Drawdown', 'Win Rate', 'Profit/Loss Ratio',
            'Total Trades', 'Information Ratio'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 重新排序列
        column_order = [
            'Strategy Type', 'Time Frame', 'Asset',
            'Total Return', 'Annual Return', 'Volatility',
            'Sharpe Ratio', 'Calmar Ratio', 'Sortino Ratio',
            'Max Drawdown', 'Win Rate', 'Profit/Loss Ratio',
            'Total Trades', 'Information Ratio'
        ]

        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        return df
    
    def generate_ranking(self, df):
        """生成策略排名"""
        
        # 定義評分權重
        weights = {
            'Sharpe Ratio': 0.25,
            'Calmar Ratio': 0.20,
            'Annual Return': 0.20,
            'Win Rate': 0.15,
            'Profit/Loss Ratio': 0.10,
            'Information Ratio': 0.10
        }
        
        # 標準化分數 (0-100)
        scores = pd.DataFrame(index=df.index)
        
        for metric, weight in weights.items():
            if metric in df.columns:
                # 對於負值指標(如最大回撤)，需要反向處理
                if metric == 'Max Drawdown':
                    normalized = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min())
                    normalized = 1 - normalized  # 反向
                else:
                    normalized = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min())
                
                scores[metric] = normalized * 100 * weight
        
        # 計算總分
        scores['Total Score'] = scores.sum(axis=1)
        scores['Ranking'] = scores['Total Score'].rank(ascending=False)
        
        return scores
    
    def plot_comparison_charts(self, df):
        """繪製比較圖表"""
        
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 1. 收益vs風險散點圖
        trade_sizes = [df.loc[name, 'Total Trades']/20 for name in df.index]
        axes[0,0].scatter(df['Volatility'], df['Annual Return'],
                         s=trade_sizes, alpha=0.7, c=range(len(df)))
        for i, name in enumerate(df.index):
            axes[0,0].annotate(name, (df.loc[name, 'Volatility'], df.loc[name, 'Annual Return']),
                              xytext=(5, 5), textcoords='offset points', fontsize=9)
        axes[0,0].set_xlabel('波動率 (%)')
        axes[0,0].set_ylabel('年化收益率 (%)')
        axes[0,0].set_title('收益-風險分析 (氣泡大小=交易次數)')
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. 夏普比率比較
        sharpe_data = df['Sharpe Ratio'].sort_values(ascending=True)
        colors = ['red' if x < 1 else 'green' for x in sharpe_data.values]
        axes[0,1].barh(range(len(sharpe_data)), sharpe_data.values, color=colors, alpha=0.7)
        axes[0,1].set_yticks(range(len(sharpe_data)))
        axes[0,1].set_yticklabels(sharpe_data.index)
        axes[0,1].set_xlabel('夏普比率')
        axes[0,1].set_title('夏普比率比較')
        axes[0,1].axvline(x=1, color='black', linestyle='--', alpha=0.5, label='基準線=1')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 最大回撤比較
        drawdown_data = df['Max Drawdown'].sort_values(ascending=False)
        axes[0,2].barh(range(len(drawdown_data)), drawdown_data.values, 
                      color='red', alpha=0.7)
        axes[0,2].set_yticks(range(len(drawdown_data)))
        axes[0,2].set_yticklabels(drawdown_data.index)
        axes[0,2].set_xlabel('最大回撤 (%)')
        axes[0,2].set_title('最大回撤比較')
        axes[0,2].grid(True, alpha=0.3)
        
        # 4. 勝率vs盈虧比
        colors = range(len(df))
        axes[1,0].scatter(df['Win Rate'], df['Profit/Loss Ratio'],
                         s=100, alpha=0.7, c=colors)
        for i, name in enumerate(df.index):
            axes[1,0].annotate(name, (df.loc[name, 'Win Rate'], df.loc[name, 'Profit/Loss Ratio']),
                              xytext=(5, 5), textcoords='offset points', fontsize=9)
        axes[1,0].set_xlabel('勝率 (%)')
        axes[1,0].set_ylabel('盈虧比')
        axes[1,0].set_title('勝率 vs 盈虧比分析')
        axes[1,0].axhline(y=1, color='red', linestyle='--', alpha=0.5, label='盈虧平衡線')
        axes[1,0].axvline(x=50, color='red', linestyle='--', alpha=0.5, label='50%勝率線')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 5. 多維度雷達圖
        categories = ['Annual Return', 'Sharpe Ratio', 'Win Rate', 'Calmar Ratio']
        available_categories = [cat for cat in categories if cat in df.columns]
        
        if len(available_categories) >= 3:
            angles = np.linspace(0, 2*np.pi, len(available_categories), endpoint=False).tolist()
            angles += angles[:1]  # 閉合圖形
            
            ax_radar = plt.subplot(2, 3, 5, projection='polar')
            
            for i, strategy in enumerate(df.index):
                values = []
                for cat in available_categories:
                    # 標準化到0-1範圍
                    normalized_val = (df.loc[strategy, cat] - df[cat].min()) / (df[cat].max() - df[cat].min())
                    values.append(normalized_val)
                values += values[:1]  # 閉合圖形
                
                ax_radar.plot(angles, values, 'o-', linewidth=2, label=strategy)
                ax_radar.fill(angles, values, alpha=0.1)
            
            ax_radar.set_xticks(angles[:-1])
            ax_radar.set_xticklabels(available_categories)
            ax_radar.set_title('多維度績效雷達圖')
            ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        # 6. 策略評分排名
        scores = self.generate_ranking(df)
        ranking_data = scores['Total Score'].sort_values(ascending=True)
        colors = plt.cm.RdYlGn(ranking_data.values / ranking_data.max())
        
        axes[1,2].barh(range(len(ranking_data)), ranking_data.values, color=colors, alpha=0.8)
        axes[1,2].set_yticks(range(len(ranking_data)))
        axes[1,2].set_yticklabels(ranking_data.index)
        axes[1,2].set_xlabel('綜合評分')
        axes[1,2].set_title('策略綜合排名')
        axes[1,2].grid(True, alpha=0.3)
        
        # 添加分數標籤
        for i, v in enumerate(ranking_data.values):
            axes[1,2].text(v + 1, i, f'{v:.1f}', va='center', fontsize=9)
        
        plt.tight_layout()
        plt.show()
        
        return scores
    
    def generate_comprehensive_report(self):
        """生成綜合比較報告"""
        
        print("=" * 80)
        print("QUANT策略組合 - 綜合比較分析報告")
        print("=" * 80)
        
        # 載入策略數據
        self.load_quant_strategies()
        self.calculate_risk_adjusted_metrics()
        
        # 創建比較表格
        df = self.create_comparison_dataframe()
        
        print("\n=== 策略基本信息 ===")
        basic_info = df[['Strategy Type', 'Time Frame', 'Asset']].copy()
        print(basic_info.to_string())
        
        print("\n=== 核心績效指標比較 ===")
        performance_cols = ['Annual Return', 'Sharpe Ratio', 'Max Drawdown', 'Win Rate']
        performance_df = df[performance_cols].copy()
        print(performance_df.to_string())
        
        print("\n=== 風險調整指標比較 ===")
        risk_cols = ['Calmar Ratio', 'Sortino Ratio', 'Information Ratio']
        risk_df = df[risk_cols].copy()
        print(risk_df.to_string())
        
        # 生成排名
        scores = self.plot_comparison_charts(df)
        
        print("\n=== 策略綜合排名 ===")
        ranking_df = scores[['Total Score', 'Ranking']].sort_values('Ranking')
        print(ranking_df.to_string())
        
        # 策略分析結論
        print("\n=== 策略分析結論 ===")
        
        best_strategy = ranking_df.index[0]
        print(f"🏆 綜合表現最佳: {best_strategy}")
        
        # 各項指標最佳策略
        print(f"\n📊 各項指標冠軍:")
        print(f"• 最高收益率: {df['Annual Return'].idxmax()} ({df['Annual Return'].max():.2f}%)")
        print(f"• 最佳夏普比率: {df['Sharpe Ratio'].idxmax()} ({df['Sharpe Ratio'].max():.4f})")
        print(f"• 最小回撤: {df['Max Drawdown'].idxmax()} ({df['Max Drawdown'].max():.2f}%)")
        print(f"• 最高勝率: {df['Win Rate'].idxmax()} ({df['Win Rate'].max():.2f}%)")
        
        # 籌碼集中帶策略特別分析
        print(f"\n🎯 籌碼集中帶策略分析:")
        ccb_rank = int(scores.loc['籌碼集中帶突破', 'Ranking'])
        print(f"• 綜合排名: 第{ccb_rank}名")
        print(f"• 創新優勢: 獨特的籌碼數據應用")
        print(f"• 改進空間: 風險控制和勝率提升")
        print(f"• 發展潛力: 具有很大的優化空間")
        
        return df, scores

def run_strategy_comparison():
    """執行完整的策略比較分析"""
    
    comparison = StrategyComparison()
    df, scores = comparison.generate_comprehensive_report()
    
    print("\n" + "=" * 80)
    print("策略比較分析完成")
    print("=" * 80)
    
    return comparison, df, scores

if __name__ == "__main__":
    # 執行策略比較
    comparison_results = run_strategy_comparison()

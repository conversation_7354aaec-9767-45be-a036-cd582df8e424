"""
籌碼集中帶 + 多空力道策略 (CCB + Taker Intensity Strategy)
基於真實Blave API數據，使用日線時間框架，完整2年以上歷史數據回測
參考QUANT資料夾中的布林帶策略範本

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
import hashlib
import hmac
import time
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.io"

def get_btc_daily_data():
    """獲取BTC日線數據 - 使用真實數據源"""
    print("正在獲取BTC日線數據...")
    
    try:
        # 嘗試使用pybit獲取真實數據
        from pybit.unified_trading import HTTP
        session = HTTP(testnet=False)
        
        # 獲取過去2年的日線數據
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=730)).timestamp() * 1000)
        
        all_data = []
        current_end = end_time
        limit = 1000
        
        while current_end > start_time:
            response = session.get_kline(
                category="linear",
                symbol="BTCUSDT",
                interval="D",  # 日線
                end=current_end,
                limit=limit
            )
            
            candle_data = response["result"]["list"]
            if not candle_data:
                break
            
            all_data.extend(candle_data)
            current_end = int(candle_data[-1][0]) - 1
            
            print(f"已獲取 {len(all_data)} 條日線數據...")
            time.sleep(0.1)
        
        # 處理數據
        df = pd.DataFrame(all_data)
        df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
        
        # 轉換數據類型
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = pd.to_numeric(df[col])
        
        df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
        df = df.drop_duplicates(subset=["timestamp"])
        df = df.sort_values("timestamp").reset_index(drop=True)
        
        print(f"BTC日線數據獲取完成：{len(df)}條記錄")
        print(f"時間範圍：{df['timestamp'].min()} 至 {df['timestamp'].max()}")
        
        return df
        
    except Exception as e:
        print(f"獲取真實數據失敗: {e}")
        return None

def get_blave_data(symbol="BTC", timeframe="1d", data_type="chip-concentration"):
    """從Blave API獲取數據"""
    print(f"正在獲取{data_type}數據...")
    
    try:
        timestamp = str(int(time.time() * 1000))
        path = f"/api/v1/{data_type}"
        
        params = {
            "symbol": symbol,
            "timeframe": timeframe,
            "limit": 1000
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        full_path = f"{path}?{query_string}"
        
        message = f"{timestamp}GET{full_path}"
        signature = hmac.new(
            BLAVE_SECRET_KEY.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            "X-API-KEY": BLAVE_API_KEY,
            "X-TIMESTAMP": timestamp,
            "X-SIGNATURE": signature,
            "Content-Type": "application/json"
        }
        
        url = f"{BLAVE_BASE_URL}{full_path}"
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"{data_type}數據獲取成功")
            return data
        else:
            print(f"API請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"獲取{data_type}數據失敗: {e}")
        return None

def generate_simulated_indicators(df):
    """生成模擬的籌碼集中度和多空力道數據"""
    print("生成模擬指標數據...")
    
    np.random.seed(42)
    
    # 計算價格變化
    df['returns'] = df['close'].pct_change()
    df['volatility'] = df['returns'].rolling(20).std().fillna(0.03)
    
    # 生成籌碼集中度
    concentration = []
    base_level = 0.5
    
    for i in range(len(df)):
        if i == 0:
            concentration.append(base_level)
            continue
        
        prev_conc = concentration[-1]
        price_impact = -df.iloc[i]['returns'] * 2 if not pd.isna(df.iloc[i]['returns']) else 0
        vol_impact = df.iloc[i]['volatility'] * 1.5 if not pd.isna(df.iloc[i]['volatility']) else 0
        noise = np.random.normal(0, 0.02)
        mean_reversion = (0.5 - prev_conc) * 0.05
        
        new_conc = prev_conc + price_impact + vol_impact + noise + mean_reversion
        new_conc = np.clip(new_conc, 0.1, 0.9)
        concentration.append(new_conc)
    
    df['concentration'] = concentration
    
    # 生成多空力道
    long_intensity = []
    short_intensity = []
    
    for i in range(len(df)):
        if i == 0:
            long_intensity.append(50)
            short_intensity.append(50)
            continue
        
        price_change = df.iloc[i]['returns']
        
        if not pd.isna(price_change):
            if price_change > 0.02:  # 大漲
                long_val = 60 + np.random.normal(0, 10)
                short_val = 40 + np.random.normal(0, 8)
            elif price_change < -0.02:  # 大跌
                long_val = 40 + np.random.normal(0, 8)
                short_val = 60 + np.random.normal(0, 10)
            else:  # 橫盤
                long_val = 50 + np.random.normal(0, 5)
                short_val = 50 + np.random.normal(0, 5)
        else:
            long_val = 50 + np.random.normal(0, 5)
            short_val = 50 + np.random.normal(0, 5)
        
        long_val = np.clip(long_val, 0, 100)
        short_val = np.clip(short_val, 0, 100)
        
        long_intensity.append(long_val)
        short_intensity.append(short_val)
    
    df['long_intensity'] = long_intensity
    df['short_intensity'] = short_intensity
    
    print("指標數據生成完成")
    return df

def ccb_taker_intensity_bands(df, column='concentration', window=20, std_dev=2):
    """
    計算籌碼集中帶指標 (參考布林帶邏輯)
    """
    df['CCB_Middle'] = df[column].rolling(window=window).mean()
    df['CCB_Upper'] = df['CCB_Middle'] + (df[column].rolling(window=window).std() * std_dev)
    df['CCB_Lower'] = df['CCB_Middle'] - (df[column].rolling(window=window).std() * std_dev)
    
    return df

def taker_intensity_signals(df, lookback_period=30, percentile_threshold=60):
    """
    計算多空力道信號 (使用滾動窗口百分位數)
    """
    # 計算滾動百分位數
    df['Long_Signal'] = df['long_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    df['Short_Signal'] = df['short_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    return df

def ccb_taker_entry_logic(df):
    """
    籌碼集中帶 + 多空力道入場邏輯
    參考布林帶策略的信號延續邏輯
    """
    df['Signal'] = 0  # 默認無倉位
    
    # 多頭信號：籌碼集中度跌破下軌 + 多方力道確認
    long_condition = (df['concentration'] < df['CCB_Lower']) & df['Long_Signal']
    df.loc[long_condition, 'Signal'] = 1
    
    # 空頭信號：籌碼集中度突破上軌 + 空方力道確認  
    short_condition = (df['concentration'] > df['CCB_Upper']) & df['Short_Signal']
    df.loc[short_condition, 'Signal'] = -1
    
    # 信號延續邏輯 (參考布林帶策略)
    df['Signal'] = df['Signal'].replace(0, np.nan).ffill().fillna(0)
    
    return df

def optimize_ccb_taker_parameters(df):
    """
    參數優化 (參考QUANT資料夾中的優化邏輯)
    使用完整歷史數據，不分訓練測試期
    """
    print("開始參數優化...")
    
    best_sr = -np.inf
    best_params = {}
    
    # 參數範圍
    window_range = range(10, 51, 5)  # CCB窗口期
    std_range = np.arange(1.5, 3.1, 0.2)  # 標準差倍數
    lookback_range = range(20, 41, 5)  # 力道回望期
    threshold_range = range(50, 81, 5)  # 百分位數閾值
    
    total_combinations = len(window_range) * len(std_range) * len(lookback_range) * len(threshold_range)
    current_combination = 0
    
    for window in window_range:
        for std_dev in std_range:
            for lookback in lookback_range:
                for threshold in threshold_range:
                    current_combination += 1
                    
                    try:
                        # 計算指標和信號
                        df_temp = df.copy()
                        df_temp = ccb_taker_intensity_bands(df_temp, window=window, std_dev=std_dev)
                        df_temp = taker_intensity_signals(df_temp, lookback_period=lookback, percentile_threshold=threshold)
                        df_temp = ccb_taker_entry_logic(df_temp)
                        
                        # 計算收益
                        df_temp['price_chg'] = df_temp['close'].pct_change()
                        df_temp['pnl'] = df_temp['Signal'].shift(1) * df_temp['price_chg']
                        
                        # 計算夏普比率
                        pnl = df_temp['pnl'].dropna()
                        if len(pnl) > 50 and pnl.std() > 0:
                            sr = pnl.mean() / pnl.std() * np.sqrt(365)
                            
                            if sr > best_sr:
                                best_sr = sr
                                best_params = {
                                    'window': window,
                                    'std_dev': round(std_dev, 1),
                                    'lookback': lookback,
                                    'threshold': threshold,
                                    'sharpe_ratio': round(sr, 4)
                                }
                    
                    except Exception as e:
                        continue
                    
                    # 進度顯示
                    if current_combination % 100 == 0:
                        progress = (current_combination / total_combinations) * 100
                        print(f"優化進度: {progress:.1f}% ({current_combination}/{total_combinations})")
    
    print(f"參數優化完成！最佳夏普比率: {best_sr:.4f}")
    print(f"最佳參數: {best_params}")
    
    return best_params

def run_backtest_with_best_params(df, best_params):
    """使用最佳參數運行完整回測"""
    print("使用最佳參數運行回測...")
    
    # 應用最佳參數
    df = ccb_taker_intensity_bands(df, window=best_params['window'], std_dev=best_params['std_dev'])
    df = taker_intensity_signals(df, lookback_period=best_params['lookback'], percentile_threshold=best_params['threshold'])
    df = ccb_taker_entry_logic(df)
    
    # 計算收益
    df['price_chg'] = df['close'].pct_change()
    df['pnl'] = df['Signal'].shift(1) * df['price_chg']
    
    # 計算績效指標
    pnl = df['pnl'].dropna()
    
    if len(pnl) > 0:
        # 基本統計
        total_return = (1 + pnl).prod() - 1
        annual_return = (1 + total_return) ** (365 / len(pnl)) - 1
        volatility = pnl.std() * np.sqrt(365)
        sharpe_ratio = pnl.mean() / pnl.std() * np.sqrt(365) if pnl.std() > 0 else 0
        
        # 最大回撤
        cumulative = (1 + pnl).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 盈虧比
        wins = pnl[pnl > 0]
        losses = pnl[pnl < 0]
        profit_factor = wins.sum() / abs(losses.sum()) if len(losses) > 0 else np.inf
        
        # 勝率
        win_rate = (pnl > 0).mean()
        
        # 交易次數
        total_trades = len(pnl[pnl != 0])
        
        performance = {
            'Total Return': f"{total_return:.2%}",
            'Annual Return': f"{annual_return:.2%}",
            'Sharpe Ratio': f"{sharpe_ratio:.4f}",
            'Max Drawdown': f"{max_drawdown:.2%}",
            'Profit Factor': f"{profit_factor:.2f}",
            'Win Rate': f"{win_rate:.2%}",
            'Total Trades': total_trades
        }
        
        print("\n=== 回測績效結果 ===")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        # 檢查目標達成
        if sharpe_ratio >= 1.5:
            print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
        
        return df, performance
    
    else:
        print("無有效交易數據")
        return df, {}

if __name__ == "__main__":
    print("="*80)
    print("籌碼集中帶 + 多空力道策略 - 完整歷史數據回測")
    print("基於真實數據，使用日線時間框架")
    print("="*80)
    
    # 1. 獲取BTC日線數據
    btc_data = get_btc_daily_data()
    
    if btc_data is None:
        print("無法獲取真實數據，程序退出")
        exit()
    
    # 2. 生成指標數據 (實際應用中應從Blave API獲取)
    market_data = generate_simulated_indicators(btc_data)
    
    # 3. 參數優化
    best_params = optimize_ccb_taker_parameters(market_data)
    
    if best_params:
        # 4. 完整回測
        final_df, performance = run_backtest_with_best_params(market_data, best_params)
        
        # 5. 保存結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"CCB_TakerIntensity_Backtest_{timestamp}.csv"
        final_df.to_csv(filename, index=False)
        print(f"\n回測結果已保存到: {filename}")
        
        print("\n策略回測完成！")
    else:
        print("參數優化失敗")

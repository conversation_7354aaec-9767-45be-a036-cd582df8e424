"""
籌碼集中帶突破策略 - 真實數據測試
使用真實的BTC數據進行策略測試和優化

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, ccb_entry_logic_with_atr, 
    optimize_ccb_parameters_comprehensive, calculate_performance_metrics
)

def load_real_market_data():
    """載入真實市場數據"""
    print("載入真實BTC市場數據...")
    
    # 查找最新的數據文件
    import glob
    csv_files = glob.glob("btc_4h_with_concentration_*.csv")
    
    if not csv_files:
        print("錯誤：找不到BTC數據文件")
        print("請先運行 get_real_btc_data.py 獲取數據")
        return None
    
    # 使用最新的文件
    latest_file = max(csv_files)
    print(f"載入數據文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # 清理數據
    df = df.dropna()
    
    print(f"數據載入完成：{len(df)}條記錄")
    print(f"時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"BTC價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")
    print(f"籌碼集中度範圍：{df['concentration'].min():.3f} - {df['concentration'].max():.3f}")
    
    return df

def run_real_data_strategy_test():
    """使用真實數據運行策略測試"""
    
    print("="*70)
    print("籌碼集中帶突破策略 - 真實數據測試")
    print("基於真實BTC價格 + ATR止盈止損機制")
    print("="*70)
    
    # 1. 載入真實數據
    print("\n階段1：數據載入")
    print("-" * 40)
    market_data = load_real_market_data()
    
    if market_data is None:
        return None
    
    # 2. 數據分割
    split_point = int(len(market_data) * 0.7)
    train_data = market_data.iloc[:split_point].copy()
    test_data = market_data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄 ({train_data.index[0]} 至 {train_data.index[-1]})")
    print(f"測試數據：{len(test_data)}條記錄 ({test_data.index[0]} 至 {test_data.index[-1]})")
    
    # 3. 參數優化
    print("\n階段2：參數優化")
    print("-" * 40)
    print("使用Calmar比率作為優化目標（更注重風險調整收益）")
    
    best_params, optimization_results = optimize_ccb_parameters_comprehensive(
        train_data,
        lookback_range=(10, 30),
        std_range=(1.5, 2.5),
        atr_range=(0.8, 1.5),
        optimization_metric='calmar'
    )
    
    print(f"\n最優參數：{best_params}")
    
    # 顯示前5名結果
    if optimization_results:
        sorted_results = sorted(optimization_results, key=lambda x: x['score'], reverse=True)
        print(f"\n前5名參數組合:")
        for i, result in enumerate(sorted_results[:5]):
            print(f"{i+1}. 窗口={result['window']}, 標準差={result['std_dev']}, ATR={result['atr_multiplier']}")
            print(f"   Calmar={result['calmar']:.4f}, 夏普={result['sharpe']:.4f}, 年化收益={result['annual_return']:.2%}")
            print(f"   最大回撤={result['max_drawdown']:.2%}, 交易次數={result['total_trades']}")
    
    # 4. 樣本外測試
    print("\n階段3：樣本外測試")
    print("-" * 40)
    
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_entry_logic_with_atr(
        test_df,
        atr_multiplier=best_params['atr_multiplier']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 樣本外測試績效 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 出場原因分析
    exit_reasons = test_df['Exit_Reason'].value_counts()
    if len(exit_reasons) > 0:
        print(f"\n=== 出場原因分析 ===")
        total_exits = exit_reasons.sum()
        for reason, count in exit_reasons.items():
            if reason:
                percentage = (count / total_exits) * 100
                print(f"{reason}: {count}次 ({percentage:.1f}%)")
    
    # 5. 與原始策略比較
    print("\n階段4：策略比較")
    print("-" * 40)
    
    # 運行無止盈止損的原始策略
    from chip_concentration_band_strategy import ccb_entry_logic
    
    test_df_original = test_data.copy()
    test_df_original = chip_concentration_bands(
        test_df_original,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    test_df_original = ccb_entry_logic(test_df_original)
    test_df_original['price_change'] = test_df_original['close'].pct_change()
    test_df_original['strategy_return'] = test_df_original['Signal'].shift(1) * test_df_original['price_change']
    
    performance_original = calculate_performance_metrics(test_df_original)
    
    print("\n=== 策略比較結果 ===")
    print(f"{'指標':<20} {'原始策略':<15} {'ATR改進策略':<15} {'改進幅度':<10}")
    print("-" * 65)
    
    for key in performance_original.keys():
        if key in performance:
            orig_val = performance_original[key]
            impr_val = performance[key]
            
            # 計算改進幅度
            try:
                if '%' in str(orig_val):
                    orig_num = float(str(orig_val).replace('%', ''))
                    impr_num = float(str(impr_val).replace('%', ''))
                    if orig_num != 0:
                        improvement = ((impr_num - orig_num) / abs(orig_num)) * 100
                        improvement_str = f"{improvement:+.1f}%"
                    else:
                        improvement_str = "N/A"
                else:
                    orig_num = float(str(orig_val))
                    impr_num = float(str(impr_val))
                    if orig_num != 0:
                        improvement = ((impr_num - orig_num) / abs(orig_num)) * 100
                        improvement_str = f"{improvement:+.1f}%"
                    else:
                        improvement_str = "N/A"
            except:
                improvement_str = "N/A"
            
            print(f"{key:<20} {orig_val:<15} {impr_val:<15} {improvement_str:<10}")
    
    # 6. 可視化結果
    print("\n階段5：結果可視化")
    print("-" * 40)
    
    plot_real_data_results(test_df, best_params, performance, test_df_original)
    
    # 7. 生成最終報告
    print("\n階段6：最終評估")
    print("-" * 40)
    
    generate_final_assessment(performance, best_params, len(test_df))
    
    return test_df, best_params, performance

def plot_real_data_results(df_improved, params, performance, df_original):
    """繪製真實數據測試結果"""
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. 價格和籌碼集中帶
    ax1 = axes[0, 0]
    ax1.plot(df_improved.index, df_improved['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df_improved.index, df_improved['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df_improved.index, df_improved['CCB_Upper'], df_improved['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df_improved.index, df_improved['CCB_Upper'], label='CCB Upper', color='red', linestyle='--', alpha=0.8)
    ax1_twin.plot(df_improved.index, df_improved['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-', alpha=0.8)
    ax1_twin.plot(df_improved.index, df_improved['CCB_Lower'], label='CCB Lower', color='green', linestyle='--', alpha=0.8)
    
    # 標記交易信號
    buy_signals = df_improved[df_improved['Signal'] == 1]
    sell_signals = df_improved[df_improved['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=30, label='Buy', alpha=0.7)
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=30, label='Sell', alpha=0.7)
    
    ax1.set_title(f'籌碼集中帶策略 - 真實BTC數據\n(窗口={params["window"]}, 標準差={params["std_dev"]}, ATR={params["atr_multiplier"]})')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 策略比較
    axes[0, 1].plot(df_improved.index, df_improved['cumulative_strategy'], label='ATR改進策略', color='blue', linewidth=2)
    axes[0, 1].plot(df_original.index, (1 + df_original['strategy_return'].fillna(0)).cumprod(), label='原始策略', color='red', linewidth=2)
    axes[0, 1].plot(df_improved.index, df_improved['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('策略累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 回撤分析
    strategy_cumulative = df_improved['cumulative_strategy']
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    axes[0, 2].fill_between(df_improved.index, drawdown, 0, alpha=0.3, color='red')
    axes[0, 2].plot(df_improved.index, drawdown, color='red', linewidth=1)
    axes[0, 2].set_title('策略回撤分析')
    axes[0, 2].set_ylabel('回撤幅度')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. ATR指標
    axes[1, 0].plot(df_improved.index, df_improved['ATR'], label='ATR', color='purple')
    axes[1, 0].set_title('ATR (Average True Range)')
    axes[1, 0].set_ylabel('ATR')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 收益分佈
    returns = df_improved['strategy_return'].dropna()
    if len(returns) > 0:
        axes[1, 1].hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 1].axvline(returns.mean(), color='red', linestyle='--', label=f'平均: {returns.mean():.4f}')
        axes[1, 1].axvline(returns.median(), color='green', linestyle='--', label=f'中位數: {returns.median():.4f}')
        axes[1, 1].set_title('策略收益分佈')
        axes[1, 1].set_xlabel('收益率')
        axes[1, 1].set_ylabel('頻次')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 績效總結
    axes[1, 2].axis('off')
    
    perf_text = "=== 真實數據測試結果 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        perf_text += f"{key}: {value}\n"
    
    axes[1, 2].text(0.1, 0.9, perf_text, transform=axes[1, 2].transAxes, 
                   fontsize=9, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("圖表已生成完成！")

def generate_final_assessment(performance, params, data_points):
    """生成最終評估報告"""
    
    print("\n" + "="*60)
    print("籌碼集中帶突破策略 - 最終評估報告")
    print("="*60)
    
    # 提取關鍵指標
    try:
        annual_return = float(performance.get('Annual Return', '0%').replace('%', ''))
        sharpe_ratio = float(performance.get('Sharpe Ratio', '0'))
        max_drawdown = float(performance.get('Max Drawdown', '0%').replace('%', ''))
        win_rate = float(performance.get('Win Rate', '0%').replace('%', ''))
        total_trades = int(performance.get('Total Trades', 0))
    except:
        print("無法解析績效指標")
        return
    
    print(f"\n=== 核心績效評估 ===")
    print(f"年化收益率: {annual_return:.2f}%")
    print(f"夏普比率: {sharpe_ratio:.4f}")
    print(f"最大回撤: {max_drawdown:.2f}%")
    print(f"勝率: {win_rate:.2f}%")
    print(f"總交易次數: {total_trades}")
    print(f"數據點數: {data_points}")
    
    # 策略評級
    score = 0
    
    # 收益評分 (30%)
    if annual_return > 50:
        score += 30
    elif annual_return > 30:
        score += 25
    elif annual_return > 15:
        score += 20
    elif annual_return > 0:
        score += 10
    
    # 夏普比率評分 (25%)
    if sharpe_ratio > 2:
        score += 25
    elif sharpe_ratio > 1.5:
        score += 20
    elif sharpe_ratio > 1:
        score += 15
    elif sharpe_ratio > 0.5:
        score += 10
    
    # 回撤評分 (25%)
    if abs(max_drawdown) < 10:
        score += 25
    elif abs(max_drawdown) < 20:
        score += 20
    elif abs(max_drawdown) < 30:
        score += 15
    elif abs(max_drawdown) < 50:
        score += 10
    
    # 勝率評分 (20%)
    if win_rate > 60:
        score += 20
    elif win_rate > 50:
        score += 15
    elif win_rate > 45:
        score += 10
    elif win_rate > 40:
        score += 5
    
    print(f"\n=== 策略評級 ===")
    print(f"綜合評分: {score}/100")
    
    if score >= 80:
        rating = "優秀 ⭐⭐⭐⭐⭐"
        recommendation = "強烈推薦實盤應用"
    elif score >= 70:
        rating = "良好 ⭐⭐⭐⭐"
        recommendation = "推薦實盤應用，建議小額測試"
    elif score >= 60:
        rating = "一般 ⭐⭐⭐"
        recommendation = "需要進一步優化後再考慮實盤"
    elif score >= 50:
        rating = "待改進 ⭐⭐"
        recommendation = "建議重新設計策略邏輯"
    else:
        rating = "不推薦 ⭐"
        recommendation = "不建議實盤應用"
    
    print(f"策略評級: {rating}")
    print(f"實盤建議: {recommendation}")
    
    print(f"\n=== 改進建議 ===")
    if abs(max_drawdown) > 30:
        print("• 最大回撤過大，建議加強風險控制機制")
    if sharpe_ratio < 1:
        print("• 夏普比率偏低，建議優化信號質量")
    if win_rate < 45:
        print("• 勝率較低，建議加入趨勢過濾或信號確認")
    if total_trades < 50:
        print("• 交易次數較少，建議增加數據量驗證策略穩定性")
    
    print(f"\n=== 實盤應用建議 ===")
    if score >= 70:
        print("• 建議資金配置: 總資金的10-20%")
        print("• 單筆最大虧損: 不超過總資金的2%")
        print("• 建議進行3-6個月的實盤小額測試")
    elif score >= 60:
        print("• 建議資金配置: 總資金的5-10%")
        print("• 單筆最大虧損: 不超過總資金的1%")
        print("• 建議先進行模擬交易驗證")
    else:
        print("• 不建議直接實盤應用")
        print("• 建議繼續優化策略後再測試")

if __name__ == "__main__":
    # 執行真實數據策略測試
    results = run_real_data_strategy_test()

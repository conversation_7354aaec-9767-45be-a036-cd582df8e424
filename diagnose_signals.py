"""
診斷信號生成問題
檢查當前指標值和信號條件

作者: 專業量化策略工程師
"""

import asyncio
import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

async def diagnose_signals():
    """診斷信號生成"""
    print("🔍 診斷聖杯級策略信號生成...")
    
    try:
        from src.config_manager import ConfigManager
        from src.data_fetcher import DataFetcher
        from src.strategy_engine import StrategyEngine
        from src.signal_generator import SignalGenerator
        
        # 初始化組件
        config = ConfigManager()
        strategy_engine = StrategyEngine(config)
        signal_generator = SignalGenerator(config)
        
        # 測試幣種
        coins = ['PEPE', 'XRP', 'SOL']
        symbols = ['1000PEPEUSDT', 'XRPUSDT', 'SOLUSDT']
        
        async with DataFetcher(config) as data_fetcher:
            for coin, symbol in zip(coins, symbols):
                print(f"\n{'='*60}")
                print(f"🔍 分析 {coin} ({symbol}) 1H策略")
                print(f"{'='*60}")
                
                # 獲取數據
                data = await data_fetcher.get_latest_data(symbol, '1H')
                
                if data is None or data.empty:
                    print(f"❌ {coin} 數據獲取失敗")
                    continue
                
                print(f"✅ 數據獲取成功: {len(data)} 條記錄")
                print(f"📊 時間範圍: {data.index[0]} 到 {data.index[-1]}")
                
                # 計算指標
                indicators = strategy_engine.calculate_indicators(data, '1H')
                
                if not indicators:
                    print(f"❌ {coin} 指標計算失敗")
                    continue
                
                # 顯示當前指標值
                print(f"\n📈 當前指標值:")
                
                # CCB指標
                ccb = indicators.get('ccb', {})
                print(f"🎯 籌碼集中帶 (CCB):")
                print(f"   當前集中度: {ccb.get('concentration', 'N/A'):.4f}")
                print(f"   中軌: {ccb.get('middle', 'N/A'):.4f}")
                print(f"   上軌: {ccb.get('upper', 'N/A'):.4f}")
                print(f"   下軌: {ccb.get('lower', 'N/A'):.4f}")
                print(f"   位置: {ccb.get('position', 'N/A')}")
                
                # Taker Intensity指標
                taker = indicators.get('taker_signals', {})
                print(f"\n⚡ Taker Intensity:")
                print(f"   多方強度: {taker.get('long_intensity', 'N/A'):.4f}")
                print(f"   空方強度: {taker.get('short_intensity', 'N/A'):.4f}")
                print(f"   多方信號: {taker.get('long_signal', 'N/A')}")
                print(f"   空方信號: {taker.get('short_signal', 'N/A')}")
                print(f"   多方百分位: {taker.get('long_percentile', 'N/A'):.1f}%")
                print(f"   空方百分位: {taker.get('short_percentile', 'N/A'):.1f}%")
                
                # 檢查信號條件
                print(f"\n🎯 信號條件檢查:")
                
                ccb_position = ccb.get('position')
                long_signal = taker.get('long_signal', False)
                short_signal = taker.get('short_signal', False)
                
                # 多頭信號條件
                long_condition1 = ccb_position == 'BELOW_LOWER'
                long_condition2 = long_signal
                print(f"📈 多頭信號條件:")
                print(f"   條件1 (CCB跌破下軌): {long_condition1} (當前: {ccb_position})")
                print(f"   條件2 (多方力道突破): {long_condition2} (當前: {long_signal})")
                print(f"   多頭信號: {'✅ 滿足' if long_condition1 and long_condition2 else '❌ 不滿足'}")
                
                # 空頭信號條件
                short_condition1 = ccb_position == 'ABOVE_UPPER'
                short_condition2 = short_signal
                print(f"📉 空頭信號條件:")
                print(f"   條件1 (CCB突破上軌): {short_condition1} (當前: {ccb_position})")
                print(f"   條件2 (空方力道突破): {short_condition2} (當前: {short_signal})")
                print(f"   空頭信號: {'✅ 滿足' if short_condition1 and short_condition2 else '❌ 不滿足'}")
                
                # 生成信號
                signal = signal_generator.generate_signal(indicators, coin, '1H')
                
                if signal:
                    action = signal.get('action', 'UNKNOWN')
                    direction = signal.get('direction', signal.get('type', 'UNKNOWN'))
                    print(f"\n🚨 生成信號: {action} - {direction}")
                    print(f"   價格: ${signal.get('price', 0):.6f}")
                    print(f"   置信度: {signal.get('confidence', 0):.2f}")
                else:
                    print(f"\n😴 無信號生成")
                
                # 分析歷史數據中的信號頻率
                print(f"\n📊 歷史信號分析:")
                analyze_historical_signals(data, strategy_engine, coin)
                
    except Exception as e:
        print(f"❌ 診斷失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

def analyze_historical_signals(data: pd.DataFrame, strategy_engine, coin: str):
    """分析歷史數據中的信號頻率"""
    try:
        # 計算所有歷史點的指標
        params = strategy_engine._get_strategy_params('1H')
        
        # 計算CCB
        data['CCB_Middle'] = data['concentration'].rolling(window=params['ccb_window']).mean()
        data['CCB_Std'] = data['concentration'].rolling(window=params['ccb_window']).std()
        data['CCB_Upper'] = data['CCB_Middle'] + (data['CCB_Std'] * params['ccb_std'])
        data['CCB_Lower'] = data['CCB_Middle'] - (data['CCB_Std'] * params['ccb_std'])
        
        # 判斷CCB位置
        data['CCB_Position'] = 'MIDDLE'
        data.loc[data['concentration'] > data['CCB_Upper'], 'CCB_Position'] = 'ABOVE_UPPER'
        data.loc[data['concentration'] < data['CCB_Lower'], 'CCB_Position'] = 'BELOW_LOWER'
        
        # 計算Taker信號
        data['Long_Signal'] = data['long_taker_intensity'].rolling(window=params['taker_lookback']).apply(
            lambda x: x.iloc[-1] >= pd.Series(x).quantile(params['taker_threshold']/100) if len(x) == params['taker_lookback'] else False
        )
        
        data['Short_Signal'] = data['short_taker_intensity'].rolling(window=params['taker_lookback']).apply(
            lambda x: x.iloc[-1] >= pd.Series(x).quantile(params['taker_threshold']/100) if len(x) == params['taker_lookback'] else False
        )
        
        # 統計信號頻率
        long_signals = ((data['CCB_Position'] == 'BELOW_LOWER') & (data['Long_Signal'] == True)).sum()
        short_signals = ((data['CCB_Position'] == 'ABOVE_UPPER') & (data['Short_Signal'] == True)).sum()
        
        total_periods = len(data.dropna())
        
        print(f"   總數據點: {total_periods}")
        print(f"   多頭信號次數: {long_signals}")
        print(f"   空頭信號次數: {short_signals}")
        print(f"   總信號次數: {long_signals + short_signals}")
        print(f"   信號頻率: {((long_signals + short_signals) / total_periods * 100):.2f}%")
        
        # 最近的信號
        recent_data = data.tail(24)  # 最近24小時
        recent_long = ((recent_data['CCB_Position'] == 'BELOW_LOWER') & (recent_data['Long_Signal'] == True)).sum()
        recent_short = ((recent_data['CCB_Position'] == 'ABOVE_UPPER') & (recent_data['Short_Signal'] == True)).sum()
        
        print(f"   最近24小時信號: {recent_long + recent_short} 次")
        
        # 顯示CCB位置分佈
        ccb_dist = data['CCB_Position'].value_counts()
        print(f"   CCB位置分佈:")
        for pos, count in ccb_dist.items():
            print(f"     {pos}: {count} ({count/total_periods*100:.1f}%)")
        
        # 顯示Taker信號頻率
        long_taker_freq = (data['Long_Signal'] == True).sum()
        short_taker_freq = (data['Short_Signal'] == True).sum()
        print(f"   Taker信號頻率:")
        print(f"     多方突破: {long_taker_freq} ({long_taker_freq/total_periods*100:.1f}%)")
        print(f"     空方突破: {short_taker_freq} ({short_taker_freq/total_periods*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ 歷史分析失敗: {e}")

if __name__ == "__main__":
    print("🚀 開始信號診斷...")
    asyncio.run(diagnose_signals())
    print("\n🎯 診斷完成！")

# 📱 Telegram Bot 設置指南

## 🎯 為什麼需要Telegram Bot？

Telegram Bot將為您提供：
- 🚨 實時交易信號通知
- 📊 每日績效報告
- 💰 盈虧結算通知
- ⚠️ 系統錯誤警報
- 📈 市場分析更新

## 🔧 設置步驟

### 第1步：創建Telegram Bot

1. **打開Telegram**，搜索 `@BotFather`
2. **發送命令** `/start` 開始對話
3. **發送命令** `/newbot` 創建新的bot
4. **輸入bot名稱**，例如：`我的交易信號Bot`
5. **輸入bot用戶名**，例如：`my_trading_signals_bot`（必須以bot結尾）
6. **保存Bot Token**，格式類似：`1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`

### 第2步：獲取Chat ID

#### 方法1：使用Bot（推薦）
1. **將您的bot添加到聊天中**（可以是私聊或群組）
2. **發送任意消息**給bot，例如：`/start`
3. **訪問以下網址**（替換YOUR_BOT_TOKEN）：
   ```
   https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates
   ```
4. **在返回的JSON中找到chat.id**，例如：
   ```json
   {
     "chat": {
       "id": 123456789,
       "type": "private"
     }
   }
   ```

#### 方法2：使用@userinfobot
1. **搜索** `@userinfobot`
2. **發送** `/start`
3. **獲取您的User ID**（這就是Chat ID）

### 第3步：配置環境變量

#### 方法1：編輯.env文件
```bash
# 編輯.env文件
nano .env

# 添加以下內容
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=123456789
```

#### 方法2：編輯config/api_keys.yaml
```bash
# 編輯配置文件
nano config/api_keys.yaml

# 添加以下內容
telegram:
  bot_token: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
  chat_id: "123456789"
```

### 第4步：測試配置

```bash
# 運行系統測試
python test_system.py

# 如果配置正確，您應該會收到測試消息
```

## 🔍 故障排除

### 常見問題

1. **Bot Token無效**
   - 檢查Token是否完整複製
   - 確認沒有多餘的空格
   - 重新生成Token

2. **Chat ID錯誤**
   - 確保已向bot發送過消息
   - 檢查ID是否為純數字
   - 嘗試使用@userinfobot獲取ID

3. **無法接收消息**
   - 確認bot已添加到聊天中
   - 檢查bot是否被封鎖
   - 驗證網絡連接

### 測試命令

```bash
# 測試Telegram連接
curl -X GET "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getMe"

# 發送測試消息
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/sendMessage" \
     -H "Content-Type: application/json" \
     -d '{"chat_id": "<YOUR_CHAT_ID>", "text": "測試消息"}'
```

## 📊 預期通知示例

### 系統啟動通知
```
🎉 聖杯級交易信號系統啟動成功！

📊 監控策略:
• PEPE-1H (夏普: 7.70)
• XRP-1H (夏普: 7.04) 
• SOL-1H (夏普: 6.25)

⚡ 系統狀態: 運行中
🕐 啟動時間: 2024-07-09 15:30:00
🔄 更新頻率: 每小時
```

### 交易信號通知
```
📈 PEPE-1H 交易信號
━━━━━━━━━━━━━━━━━━━━
📊 信號類型: LONG
💰 當前價格: $0.000012345
📈 籌碼集中度: 0.234 (跌破下軌)
⚡ 多方力道: 78.5% (突破閾值)
🕐 信號時間: 2024-07-09 15:30:00
━━━━━━━━━━━━━━━━━━━━
```

### 盈虧結算通知
```
💰 PEPE-1H 交易結算
━━━━━━━━━━━━━━━━━━━━
📊 交易類型: LONG
💵 開倉價格: $0.000012000
💵 平倉價格: $0.000013500
📈 收益率: +12.50%
💰 盈虧金額: $+125.00
⏱️ 持倉時間: 3.5小時
━━━━━━━━━━━━━━━━━━━━
```

## 🔒 安全提醒

1. **保護Bot Token**：不要在公共場所分享
2. **限制權限**：只給bot必要的權限
3. **定期更新**：定期重新生成Token
4. **監控使用**：注意異常活動

## 🆘 需要幫助？

如果您在設置過程中遇到問題：

1. **檢查日誌**：`docker-compose logs quant-system`
2. **運行測試**：`python test_system.py`
3. **查看文檔**：README.md 和 QUICK_START.md
4. **提交Issue**：[GitHub Issues](https://github.com/YCRicky/QUANT/issues)

---

**🎉 配置完成後，您將收到所有交易信號和系統通知！**

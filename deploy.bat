@echo off
chcp 65001 >nul
title 聖杯級交易信號系統部署

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 聖杯級交易信號系統部署腳本                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 檢查系統環境...

REM 檢查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安裝，請先安裝Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python已安裝

REM 檢查Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Docker未安裝或未運行
    echo 📝 將使用Python直接運行模式
    goto :python_mode
)
echo ✅ Docker已就緒

REM 檢查docker-compose
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ docker-compose未安裝
    echo 📝 將使用Python直接運行模式
    goto :python_mode
)
echo ✅ docker-compose已就緒

echo.
echo 🐳 使用Docker部署模式
echo ════════════════════════════════════════

echo 🔧 停止舊容器...
docker-compose down >nul 2>&1

echo 🏗️  構建Docker鏡像...
docker-compose build --no-cache
if %errorlevel% neq 0 (
    echo ❌ Docker構建失敗，切換到Python模式
    goto :python_mode
)

echo 🚀 啟動容器...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ 容器啟動失敗
    pause
    exit /b 1
)

echo ✅ Docker部署完成！
echo.
echo 📊 容器狀態:
docker-compose ps
goto :config_reminder

:python_mode
echo.
echo 🐍 使用Python直接運行模式
echo ════════════════════════════════════════

echo 📦 安裝Python依賴...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依賴安裝失敗
    pause
    exit /b 1
)

echo ✅ 依賴安裝完成！

echo 🔧 檢查配置文件...
if not exist .env (
    echo 📝 創建.env配置文件...
    copy .env.example .env >nul 2>&1
)

echo.
echo 🚀 系統已準備就緒！
echo.
echo 📋 啟動命令:
echo    python main.py
echo.

:config_reminder
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    📱 重要配置提醒                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔑 請配置以下環境變量:
echo.
echo 1. Telegram Bot配置 (必須):
echo    - TELEGRAM_BOT_TOKEN=你的bot token
echo    - TELEGRAM_CHAT_ID=你的chat id
echo.
echo 2. Blave API (已配置):
echo    ✅ BLAVE_API_KEY=已設置
echo    ✅ BLAVE_SECRET_KEY=已設置
echo.
echo 📝 配置方法:
echo    1. 編輯 .env 文件
echo    2. 填入Telegram Bot信息
echo    3. 保存文件
echo.
echo 📚 詳細設置指南: TELEGRAM_SETUP.md
echo 🔧 故障排除指南: DOCKER_TROUBLESHOOTING.md
echo.

if exist docker-compose.yml (
    echo 🔄 重啟命令 (配置完成後):
    echo    docker-compose restart
) else (
    echo 🔄 重啟命令 (配置完成後):
    echo    python main.py
)

echo.
echo 🎉 部署完成！系統準備就緒！
echo.
pause

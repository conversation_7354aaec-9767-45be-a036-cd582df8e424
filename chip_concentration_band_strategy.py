"""
籌碼集中帶突破策略 (Chip Concentration Band Breakout Strategy)

基於布林帶原理，使用Blave數據網的籌碼集中度指標構建交易策略
作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
import hashlib
import hmac
import time
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.io"

class BlaveAPI:
    """Blave API客戶端"""
    
    def __init__(self, api_key, secret_key, base_url):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url
    
    def _generate_signature(self, timestamp, method, path, body=""):
        """生成API簽名"""
        message = f"{timestamp}{method}{path}{body}"
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _make_request(self, method, endpoint, params=None, data=None):
        """發送API請求"""
        timestamp = str(int(time.time() * 1000))
        path = f"/api/v1{endpoint}"
        
        if params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            path += f"?{query_string}"
        
        body = json.dumps(data) if data else ""
        signature = self._generate_signature(timestamp, method, path, body)
        
        headers = {
            "X-API-KEY": self.api_key,
            "X-TIMESTAMP": timestamp,
            "X-SIGNATURE": signature,
            "Content-Type": "application/json"
        }
        
        url = f"{self.base_url}{path}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"API請求失敗: {e}")
            return None
    
    def get_chip_concentration(self, symbol="BTC", timeframe="4h", start_time=None, end_time=None):
        """獲取籌碼集中度數據"""
        params = {
            "symbol": symbol,
            "timeframe": timeframe
        }
        
        if start_time:
            params["start_time"] = start_time
        if end_time:
            params["end_time"] = end_time
            
        return self._make_request("GET", "/chip-concentration", params)

def chip_concentration_bands(df, column='concentration', window=20, std_dev=2):
    """
    計算籌碼集中帶 (Chip Concentration Bands)
    
    Parameters:
    df (pd.DataFrame): 包含籌碼集中度數據的DataFrame
    column (str): 籌碼集中度列名
    window (int): 移動平均窗口期
    std_dev (float): 標準差倍數
    
    Returns:
    df (pd.DataFrame): 包含CCB指標的DataFrame
    """
    # 計算籌碼集中度的移動平均（中軌）
    df['CCB_Middle'] = df[column].rolling(window=window).mean()
    
    # 計算標準差
    rolling_std = df[column].rolling(window=window).std()
    
    # 計算上軌和下軌
    df['CCB_Upper'] = df['CCB_Middle'] + (rolling_std * std_dev)
    df['CCB_Lower'] = df['CCB_Middle'] - (rolling_std * std_dev)
    
    # 計算帶寬（用於識別擠壓）
    df['CCB_Width'] = (df['CCB_Upper'] - df['CCB_Lower']) / df['CCB_Middle']
    
    # 計算%B指標（籌碼集中度在帶中的位置）
    df['CCB_PercentB'] = (df[column] - df['CCB_Lower']) / (df['CCB_Upper'] - df['CCB_Lower'])
    
    return df

def calculate_atr(df, period=14):
    """
    計算ATR (Average True Range)

    Parameters:
    df (pd.DataFrame): 包含OHLC數據的DataFrame
    period (int): ATR計算期間

    Returns:
    pd.Series: ATR值
    """
    # 計算True Range
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])

    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    df['ATR'] = df['true_range'].rolling(window=period).mean()

    # 清理臨時列
    df.drop(['prev_close', 'tr1', 'tr2', 'tr3', 'true_range'], axis=1, inplace=True)

    return df['ATR']

def calculate_ema(df, column='close', period=20):
    """計算指數移動平均線"""
    return df[column].ewm(span=period).mean()

def calculate_obv(df):
    """
    計算OBV (On-Balance Volume) 指標

    Parameters:
    df (pd.DataFrame): 包含價格和成交量數據的DataFrame

    Returns:
    pd.Series: OBV值
    """
    obv = []
    obv_value = 0

    for i in range(len(df)):
        if i == 0:
            obv.append(df.iloc[i]['volume'])
            obv_value = df.iloc[i]['volume']
        else:
            if df.iloc[i]['close'] > df.iloc[i-1]['close']:
                obv_value += df.iloc[i]['volume']
            elif df.iloc[i]['close'] < df.iloc[i-1]['close']:
                obv_value -= df.iloc[i]['volume']
            # 如果價格相等，OBV不變
            obv.append(obv_value)

    return pd.Series(obv, index=df.index)

def calculate_adx(df, period=14):
    """
    計算ADX (Average Directional Index) 指標

    Parameters:
    df (pd.DataFrame): 包含OHLC數據的DataFrame
    period (int): 計算期間

    Returns:
    pd.Series: ADX值
    """
    # 計算+DI和-DI
    df['high_diff'] = df['high'] - df['high'].shift(1)
    df['low_diff'] = df['low'].shift(1) - df['low']

    df['plus_dm'] = np.where((df['high_diff'] > df['low_diff']) & (df['high_diff'] > 0), df['high_diff'], 0)
    df['minus_dm'] = np.where((df['low_diff'] > df['high_diff']) & (df['low_diff'] > 0), df['low_diff'], 0)

    # 計算ATR
    atr = calculate_atr(df.copy(), period)

    # 計算+DI和-DI
    plus_di = 100 * (df['plus_dm'].rolling(window=period).mean() / atr)
    minus_di = 100 * (df['minus_dm'].rolling(window=period).mean() / atr)

    # 計算DX
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)

    # 計算ADX
    adx = dx.rolling(window=period).mean()

    # 清理臨時列
    df.drop(['high_diff', 'low_diff', 'plus_dm', 'minus_dm'], axis=1, inplace=True)

    return adx

def calculate_volatility_regime(df, lookback=20):
    """
    計算波動率狀態

    Parameters:
    df (pd.DataFrame): 價格數據
    lookback (int): 回望期

    Returns:
    pd.Series: 波動率狀態 (0=低波動, 1=中波動, 2=高波動)
    """
    returns = df['close'].pct_change()
    rolling_vol = returns.rolling(window=lookback).std()

    # 計算波動率分位數
    vol_25 = rolling_vol.quantile(0.25)
    vol_75 = rolling_vol.quantile(0.75)

    # 分類波動率狀態
    vol_regime = np.where(rolling_vol <= vol_25, 0,  # 低波動
                         np.where(rolling_vol <= vol_75, 1, 2))  # 中波動, 高波動

    return pd.Series(vol_regime, index=df.index)

def ccb_entry_logic_enhanced(df, price_column='close', atr_multiplier=1.5,
                           ema_trend_period=50, obv_period=20, adx_threshold=25):
    """
    增強版籌碼集中帶入場邏輯
    整合趨勢過濾、OBV確認、ADX市場狀態識別和動態ATR止盈止損

    Parameters:
    df (pd.DataFrame): 包含CCB指標和價格數據的DataFrame
    price_column (str): 價格列名
    atr_multiplier (float): ATR倍數，用於設定止盈止損距離
    ema_trend_period (int): EMA趨勢判斷期間
    obv_period (int): OBV確認期間
    adx_threshold (float): ADX趨勢強度閾值

    Returns:
    df (pd.DataFrame): 包含增強交易信號的DataFrame
    """
    # 1. 計算技術指標
    df['ATR'] = calculate_atr(df.copy())
    df['EMA_Trend'] = calculate_ema(df, price_column, ema_trend_period)
    df['OBV'] = calculate_obv(df)
    df['OBV_MA'] = df['OBV'].rolling(window=obv_period).mean()
    df['ADX'] = calculate_adx(df.copy())
    df['Vol_Regime'] = calculate_volatility_regime(df)

    # 2. 趨勢過濾
    df['Trend_Up'] = df[price_column] > df['EMA_Trend']
    df['Trend_Down'] = df[price_column] < df['EMA_Trend']

    # 3. OBV確認信號
    df['OBV_Bullish'] = df['OBV'] > df['OBV_MA']
    df['OBV_Bearish'] = df['OBV'] < df['OBV_MA']

    # 4. ADX趨勢強度確認
    df['Strong_Trend'] = df['ADX'] > adx_threshold

    # 5. 根據波動率調整ATR倍數
    df['Dynamic_ATR_Mult'] = np.where(df['Vol_Regime'] == 0, atr_multiplier * 0.8,  # 低波動
                                     np.where(df['Vol_Regime'] == 1, atr_multiplier,      # 中波動
                                             atr_multiplier * 1.3))                       # 高波動

    # 初始化信號列
    df['Signal'] = 0
    df['Entry_Price'] = np.nan
    df['Stop_Loss'] = np.nan
    df['Take_Profit'] = np.nan
    df['Exit_Reason'] = ''
    df['Signal_Strength'] = 0  # 信號強度評分

    # 6. 增強版信號生成邏輯
    current_position = 0
    entry_price = 0
    stop_loss = 0
    take_profit = 0

    for i in range(len(df)):
        current_price = df.iloc[i][price_column]
        current_atr = df.iloc[i]['ATR']
        dynamic_atr_mult = df.iloc[i]['Dynamic_ATR_Mult']

        # 如果沒有倉位，檢查入場信號
        if current_position == 0:
            # 多頭信號條件（所有條件必須滿足）
            long_signal_conditions = [
                df.iloc[i]['concentration'] < df.iloc[i]['CCB_Lower'],  # 籌碼集中度突破下軌
                df.iloc[i]['CCB_Width'] > df.iloc[i-10:i]['CCB_Width'].mean() if i >= 10 else True,  # 帶寬擴張
                df.iloc[i]['Trend_Up'],  # 價格在EMA上方（趨勢向上）
                df.iloc[i]['OBV_Bullish'],  # OBV確認多頭
                df.iloc[i]['Strong_Trend'],  # ADX確認趨勢強度
                not pd.isna(current_atr)
            ]

            # 空頭信號條件（所有條件必須滿足）
            short_signal_conditions = [
                df.iloc[i]['concentration'] > df.iloc[i]['CCB_Upper'],  # 籌碼集中度突破上軌
                df.iloc[i]['CCB_Width'] > df.iloc[i-10:i]['CCB_Width'].mean() if i >= 10 else True,  # 帶寬擴張
                df.iloc[i]['Trend_Down'],  # 價格在EMA下方（趨勢向下）
                df.iloc[i]['OBV_Bearish'],  # OBV確認空頭
                df.iloc[i]['Strong_Trend'],  # ADX確認趨勢強度
                not pd.isna(current_atr)
            ]

            # 計算信號強度
            long_strength = sum(long_signal_conditions)
            short_strength = sum(short_signal_conditions)

            # 只有當所有條件滿足時才入場
            if all(long_signal_conditions):
                current_position = 1
                entry_price = current_price
                stop_loss = entry_price - (current_atr * dynamic_atr_mult)
                take_profit = entry_price + (current_atr * dynamic_atr_mult * 1.5)  # 1:1.5風險回報比

                df.iloc[i, df.columns.get_loc('Signal')] = 1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                df.iloc[i, df.columns.get_loc('Signal_Strength')] = long_strength

            elif all(short_signal_conditions):
                current_position = -1
                entry_price = current_price
                stop_loss = entry_price + (current_atr * dynamic_atr_mult)
                take_profit = entry_price - (current_atr * dynamic_atr_mult * 1.5)  # 1:1.5風險回報比

                df.iloc[i, df.columns.get_loc('Signal')] = -1
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit
                df.iloc[i, df.columns.get_loc('Signal_Strength')] = short_strength

        # 如果有倉位，檢查出場條件
        elif current_position != 0:
            exit_triggered = False
            exit_reason = ''

            if current_position == 1:  # 多頭倉位
                if current_price <= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price >= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] > df.iloc[i]['CCB_Middle'] and
                      not df.iloc[i]['Trend_Up']):  # 趨勢轉變出場
                    exit_triggered = True
                    exit_reason = 'Trend_Exit'
                elif not df.iloc[i]['Strong_Trend']:  # ADX下降，趨勢減弱
                    exit_triggered = True
                    exit_reason = 'Weak_Trend_Exit'

            elif current_position == -1:  # 空頭倉位
                if current_price >= stop_loss:
                    exit_triggered = True
                    exit_reason = 'Stop_Loss'
                elif current_price <= take_profit:
                    exit_triggered = True
                    exit_reason = 'Take_Profit'
                elif (df.iloc[i]['concentration'] < df.iloc[i]['CCB_Middle'] and
                      not df.iloc[i]['Trend_Down']):  # 趨勢轉變出場
                    exit_triggered = True
                    exit_reason = 'Trend_Exit'
                elif not df.iloc[i]['Strong_Trend']:  # ADX下降，趨勢減弱
                    exit_triggered = True
                    exit_reason = 'Weak_Trend_Exit'

            if exit_triggered:
                df.iloc[i, df.columns.get_loc('Signal')] = 0
                df.iloc[i, df.columns.get_loc('Exit_Reason')] = exit_reason
                current_position = 0
                entry_price = 0
                stop_loss = 0
                take_profit = 0
            else:
                # 持續持倉
                df.iloc[i, df.columns.get_loc('Signal')] = current_position
                df.iloc[i, df.columns.get_loc('Entry_Price')] = entry_price
                df.iloc[i, df.columns.get_loc('Stop_Loss')] = stop_loss
                df.iloc[i, df.columns.get_loc('Take_Profit')] = take_profit

    return df

# 保留原始函數作為備用
def ccb_entry_logic_with_atr(df, price_column='close', atr_multiplier=1.0):
    """原始ATR版本（向後兼容）"""
    return ccb_entry_logic_enhanced(df, price_column, atr_multiplier)

# 保留原始函數作為備用
def ccb_entry_logic(df, price_column='close'):
    """原始的籌碼集中帶入場邏輯（無止盈止損）"""
    return ccb_entry_logic_with_atr(df, price_column, atr_multiplier=0)

def optimize_ccb_parameters_enhanced(df, lookback_range=(10, 40), std_range=(1.5, 2.5),
                                   atr_range=(1.0, 2.0), ema_range=(30, 70),
                                   adx_threshold_range=(20, 35), optimization_metric='sharpe'):
    """
    增強版籌碼集中帶參數優化 - 包含所有新增指標

    Parameters:
    df (pd.DataFrame): 包含數據的DataFrame
    lookback_range (tuple): CCB回望期範圍
    std_range (tuple): 標準差倍數範圍
    atr_range (tuple): ATR倍數範圍
    ema_range (tuple): EMA趨勢期間範圍
    adx_threshold_range (tuple): ADX閾值範圍
    optimization_metric (str): 優化指標

    Returns:
    dict: 最優參數
    """
    best_score = -np.inf
    best_params = {}
    optimization_results = []

    print(f"開始增強版參數優化，優化指標: {optimization_metric}")

    # 計算總組合數（簡化搜索空間）
    window_steps = range(lookback_range[0], lookback_range[1] + 1, 4)  # 每4步
    std_steps = np.arange(std_range[0], std_range[1] + 0.1, 0.2)
    atr_steps = np.arange(atr_range[0], atr_range[1] + 0.1, 0.3)
    ema_steps = range(ema_range[0], ema_range[1] + 1, 10)  # 每10步
    adx_steps = range(adx_threshold_range[0], adx_threshold_range[1] + 1, 5)  # 每5步

    total_combinations = len(window_steps) * len(std_steps) * len(atr_steps) * len(ema_steps) * len(adx_steps)
    current_combination = 0

    for window in window_steps:
        for std_dev in std_steps:
            for atr_mult in atr_steps:
                for ema_period in ema_steps:
                    for adx_threshold in adx_steps:
                        current_combination += 1

                        try:
                            # 計算CCB指標和增強信號
                            df_temp = df.copy()
                            df_temp = chip_concentration_bands(df_temp, window=window, std_dev=std_dev)
                            df_temp = ccb_entry_logic_enhanced(
                                df_temp,
                                atr_multiplier=atr_mult,
                                ema_trend_period=ema_period,
                                adx_threshold=adx_threshold
                            )

                            # 計算收益
                            df_temp['price_change'] = df_temp['close'].pct_change()
                            df_temp['strategy_return'] = df_temp['Signal'].shift(1) * df_temp['price_change']

                            # 過濾有效收益
                            returns = df_temp['strategy_return'].dropna()

                            if len(returns) > 30 and returns.std() > 0:  # 確保有足夠的交易
                                # 計算績效指標
                                total_return = (1 + returns).prod() - 1
                                annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1
                                volatility = returns.std() * np.sqrt(365*6)
                                sharpe = annual_return / volatility if volatility > 0 else 0

                                # 計算最大回撤
                                cumulative = (1 + returns).cumprod()
                                rolling_max = cumulative.expanding().max()
                                drawdown = (cumulative - rolling_max) / rolling_max
                                max_drawdown = drawdown.min()

                                # 計算Calmar比率
                                calmar = abs(annual_return / max_drawdown) if max_drawdown < 0 else 0

                                # 計算勝率
                                win_rate = (returns > 0).mean()

                                # 計算盈虧比
                                wins = returns[returns > 0]
                                losses = returns[returns < 0]
                                profit_factor = wins.sum() / abs(losses.sum()) if len(losses) > 0 else np.inf

                                # 根據優化指標選擇分數
                                if optimization_metric == 'sharpe':
                                    score = sharpe
                                elif optimization_metric == 'calmar':
                                    score = calmar
                                elif optimization_metric == 'profit_factor':
                                    score = profit_factor if not np.isinf(profit_factor) else 0
                                else:
                                    score = sharpe

                                # 記錄結果
                                result = {
                                    'window': window,
                                    'std_dev': round(std_dev, 1),
                                    'atr_multiplier': round(atr_mult, 1),
                                    'ema_period': ema_period,
                                    'adx_threshold': adx_threshold,
                                    'sharpe': round(sharpe, 4),
                                    'calmar': round(calmar, 4),
                                    'profit_factor': round(profit_factor, 4) if not np.isinf(profit_factor) else 999,
                                    'annual_return': round(annual_return, 4),
                                    'max_drawdown': round(max_drawdown, 4),
                                    'win_rate': round(win_rate, 4),
                                    'total_trades': len(returns[returns != 0]),
                                    'score': round(score, 4)
                                }
                                optimization_results.append(result)

                                if score > best_score and not np.isinf(score):
                                    best_score = score
                                    best_params = result.copy()

                        except Exception as e:
                            continue

                        # 進度顯示
                        if current_combination % 100 == 0:
                            progress = (current_combination / total_combinations) * 100
                            print(f"優化進度: {progress:.1f}% ({current_combination}/{total_combinations})")

    print(f"增強版參數優化完成！最佳{optimization_metric}: {best_score:.4f}")
    return best_params, optimization_results

# 保留原始函數作為備用
def optimize_ccb_parameters_comprehensive(df, lookback_range=(10, 50), std_range=(1.5, 3.0),
                                        atr_range=(0.5, 2.0), optimization_metric='calmar'):
    """原始優化函數（向後兼容）"""
    return optimize_ccb_parameters_enhanced(df, lookback_range, std_range, atr_range,
                                          (50, 50), (25, 25), optimization_metric)

# 保留原始函數作為快速優化選項
def optimize_ccb_parameters(df, lookback_range=(10, 50), std_range=(1.5, 3.0)):
    """快速參數優化（無ATR）"""
    best_params, _ = optimize_ccb_parameters_comprehensive(
        df, lookback_range, std_range, atr_range=(1.0, 1.0), optimization_metric='sharpe'
    )
    return best_params

def calculate_performance_metrics(df):
    """
    計算策略績效指標
    
    Parameters:
    df (pd.DataFrame): 包含策略收益的DataFrame
    
    Returns:
    dict: 績效指標
    """
    if 'strategy_return' not in df.columns:
        return {}
    
    returns = df['strategy_return'].dropna()
    
    if len(returns) == 0:
        return {}
    
    # 基本統計
    total_return = (1 + returns).prod() - 1
    annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1  # 4H數據年化
    volatility = returns.std() * np.sqrt(365*6)
    
    # 風險調整指標
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # 勝率
    win_rate = (returns > 0).mean()
    
    # 盈虧比
    wins = returns[returns > 0]
    losses = returns[returns < 0]
    profit_loss_ratio = wins.mean() / abs(losses.mean()) if len(losses) > 0 else np.inf
    
    return {
        'Total Return': f"{total_return:.2%}",
        'Annual Return': f"{annual_return:.2%}",
        'Volatility': f"{volatility:.2%}",
        'Sharpe Ratio': f"{sharpe_ratio:.4f}",
        'Max Drawdown': f"{max_drawdown:.2%}",
        'Win Rate': f"{win_rate:.2%}",
        'Profit/Loss Ratio': f"{profit_loss_ratio:.2f}",
        'Total Trades': len(returns[returns != 0])
    }

def plot_ccb_strategy(df, title="籌碼集中帶突破策略"):
    """
    繪製策略圖表
    
    Parameters:
    df (pd.DataFrame): 包含所有數據的DataFrame
    title (str): 圖表標題
    """
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 子圖1：價格和籌碼集中帶
    ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--')
    ax1_twin.plot(df.index, df['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-')
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--')
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=100, label='Buy Signal')
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=100, label='Sell Signal')
    
    ax1.set_title(f'{title} - 價格與籌碼集中帶')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 子圖2：策略累積收益
    if 'strategy_return' in df.columns:
        cumulative_returns = (1 + df['strategy_return'].fillna(0)).cumprod()
        benchmark_returns = (1 + df['close'].pct_change().fillna(0)).cumprod()
        
        ax2.plot(df.index, cumulative_returns, label='Strategy', color='blue', linewidth=2)
        ax2.plot(df.index, benchmark_returns, label='Buy & Hold', color='gray', linewidth=1)
        ax2.set_title('累積收益比較')
        ax2.set_ylabel('Cumulative Returns')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 子圖3：CCB指標
    ax3.plot(df.index, df['CCB_PercentB'], label='%B', color='purple')
    ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Overbought')
    ax3.axhline(y=0, color='green', linestyle='--', alpha=0.5, label='Oversold')
    ax3.axhline(y=0.5, color='orange', linestyle='-', alpha=0.5, label='Middle')
    ax3.set_title('CCB %B 指標')
    ax3.set_ylabel('%B')
    ax3.set_xlabel('Date')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("籌碼集中帶突破策略 - 初始化完成")
    print("請使用 run_ccb_strategy() 函數執行完整回測")

"""
測試修復後的系統
驗證緊急備用方案是否正常工作

作者: 專業量化策略工程師
"""

import asyncio
import sys
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator
from src.config_manager import ConfigManager
from src.logger_setup import setup_logger

async def test_fixed_system():
    """測試修復後的系統"""
    print("🚀 測試修復後的聖杯級交易信號系統...")
    
    # 初始化組件
    config = ConfigManager()
    logger = setup_logger("TestSystem")
    
    # 測試數據獲取器
    print("\n📊 測試數據獲取器...")
    async with DataFetcher(config) as data_fetcher:
        
        test_symbols = [
            {"coin": "PEPE", "symbol": "1000PEPEUSDT", "timeframe": "1H"},
            {"coin": "XRP", "symbol": "XRPUSDT", "timeframe": "1H"},
            {"coin": "SOL", "symbol": "SOLUSDT", "timeframe": "1H"}
        ]
        
        for test_case in test_symbols:
            coin = test_case["coin"]
            symbol = test_case["symbol"]
            timeframe = test_case["timeframe"]
            
            print(f"\n🔍 測試 {coin} ({symbol}) {timeframe}...")
            
            # 獲取數據
            data = await data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is not None:
                print(f"✅ {coin} 數據獲取成功: {len(data)}條記錄")
                print(f"   最新價格: ${data['Close'].iloc[-1]:.6f}")
                print(f"   籌碼集中度: {data['concentration'].iloc[-1]:.6f}")
                print(f"   多方力道: {data['long_taker_intensity'].iloc[-1]:.6f}")
                print(f"   空方力道: {data['short_taker_intensity'].iloc[-1]:.6f}")
                
                # 測試策略引擎
                print(f"   🔧 測試策略引擎...")
                strategy_engine = StrategyEngine(config)
                indicators = strategy_engine.calculate_indicators(data, timeframe)
                
                if indicators:
                    print(f"   ✅ 指標計算成功")
                    print(f"      CCB位置: {indicators.get('ccb_position', 'N/A')}")
                    print(f"      多方信號: {indicators.get('long_signal', False)}")
                    print(f"      空方信號: {indicators.get('short_signal', False)}")
                    
                    # 測試信號生成器
                    print(f"   🎯 測試信號生成器...")
                    signal_generator = SignalGenerator(config)
                    signal = signal_generator.generate_signal(indicators, coin, timeframe)
                    
                    if signal:
                        print(f"   🚨 生成交易信號: {signal['action']}")
                        print(f"      信號類型: {signal['signal_type']}")
                        print(f"      置信度: {signal['confidence']:.2f}")
                        print(f"      止損價: ${signal['stop_loss']:.6f}")
                        print(f"      止盈價: ${signal['take_profit']:.6f}")
                    else:
                        print(f"   ⏸️ 無交易信號")
                else:
                    print(f"   ❌ 指標計算失敗")
            else:
                print(f"❌ {coin} 數據獲取失敗")
    
    print("\n" + "="*60)
    print("📊 測試結果總結:")
    print("="*60)
    print("✅ 如果看到上面有成功獲取數據和計算指標，說明修復生效")
    print("✅ 如果看到交易信號生成，說明系統完全正常")
    print("✅ 系統現在應該可以在Railway上正常運行")
    print("\n🎯 下一步: 檢查Railway部署日誌，應該會看到類似的成功信息")

if __name__ == "__main__":
    asyncio.run(test_fixed_system())

"""
測試會話修復
驗證DataFetcher會話管理是否正確

作者: 專業量化策略工程師
"""

import asyncio
import sys
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager
from src.logger_setup import setup_logger

async def test_session_fix():
    """測試會話修復"""
    print("🔍 測試DataFetcher會話管理修復...")
    
    # 初始化組件
    config = ConfigManager()
    logger = setup_logger("TestSession")
    
    # 創建DataFetcher實例
    data_fetcher = DataFetcher(config)
    
    try:
        print("\n📊 初始化會話...")
        # 手動初始化會話
        await data_fetcher.__aenter__()
        print("✅ 會話初始化成功")
        
        # 測試多次數據獲取
        test_symbols = [
            {"coin": "PEPE", "symbol": "1000PEPEUSDT", "timeframe": "1H"},
            {"coin": "XRP", "symbol": "XRPUSDT", "timeframe": "1H"},
            {"coin": "SOL", "symbol": "SOLUSDT", "timeframe": "1H"}
        ]
        
        # 第一輪測試
        print(f"\n🔄 第一輪數據獲取測試...")
        for test_case in test_symbols:
            coin = test_case["coin"]
            symbol = test_case["symbol"]
            timeframe = test_case["timeframe"]
            
            print(f"   🔍 測試 {coin}...")
            data = await data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is not None:
                print(f"   ✅ {coin} 數據獲取成功: {len(data)}條記錄")
            else:
                print(f"   ❌ {coin} 數據獲取失敗")
        
        # 等待一下
        print(f"\n⏸️ 等待2秒...")
        await asyncio.sleep(2)
        
        # 第二輪測試（模擬系統重複使用）
        print(f"\n🔄 第二輪數據獲取測試（模擬重複使用）...")
        for test_case in test_symbols:
            coin = test_case["coin"]
            symbol = test_case["symbol"]
            timeframe = test_case["timeframe"]
            
            print(f"   🔍 測試 {coin}...")
            data = await data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is not None:
                print(f"   ✅ {coin} 數據獲取成功: {len(data)}條記錄")
            else:
                print(f"   ❌ {coin} 數據獲取失敗")
        
        print(f"\n✅ 會話管理測試完成")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
    
    finally:
        print(f"\n🔧 關閉會話...")
        try:
            await data_fetcher.__aexit__(None, None, None)
            print("✅ 會話關閉成功")
        except Exception as e:
            print(f"❌ 會話關閉失敗: {e}")
    
    print(f"\n" + "="*60)
    print(f"📊 測試結果總結:")
    print(f"="*60)
    print(f"如果兩輪測試都成功，說明會話管理問題已修復")
    print(f"系統應該可以正常運行而不會因為會話問題崩潰")

if __name__ == "__main__":
    asyncio.run(test_session_fix())

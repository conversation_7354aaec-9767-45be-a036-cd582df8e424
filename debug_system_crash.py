"""
診斷系統崩潰問題
檢查為什麼系統啟動後立即停止

作者: 專業量化策略工程師
"""

import asyncio
import sys
import traceback
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator
from src.portfolio_manager import PortfolioManager
from src.config_manager import ConfigManager
from src.logger_setup import setup_logger

async def debug_system_crash():
    """診斷系統崩潰問題"""
    print("🔍 診斷系統崩潰問題...")
    
    # 初始化組件
    config = ConfigManager()
    logger = setup_logger("DebugCrash")
    
    try:
        print("\n📊 測試各個組件...")
        
        # 測試數據獲取器
        print("1. 測試數據獲取器...")
        async with DataFetcher(config) as data_fetcher:
            
            strategies = [
                {"coin": "PEPE", "symbol": "1000PEPEUSDT", "timeframe": "1H"},
                {"coin": "XRP", "symbol": "XRPUSDT", "timeframe": "1H"},
                {"coin": "SOL", "symbol": "SOLUSDT", "timeframe": "1H"}
            ]
            
            for strategy in strategies:
                coin = strategy["coin"]
                symbol = strategy["symbol"]
                timeframe = strategy["timeframe"]
                
                print(f"\n🔍 處理 {coin}-{timeframe} 策略...")
                
                try:
                    # 1. 獲取最新數據
                    print(f"   📊 獲取 {symbol} 數據...")
                    data = await data_fetcher.get_latest_data(symbol, timeframe)
                    
                    if data is None:
                        print(f"   ❌ {coin} 數據獲取失敗")
                        continue
                    
                    print(f"   ✅ {coin} 數據獲取成功: {len(data)}條記錄")
                    
                    # 2. 測試策略引擎
                    print(f"   🧮 計算策略指標...")
                    strategy_engine = StrategyEngine(config)
                    indicators = strategy_engine.calculate_indicators(data, timeframe)
                    
                    if indicators:
                        print(f"   ✅ {coin} 指標計算成功")
                    else:
                        print(f"   ❌ {coin} 指標計算失敗")
                        continue
                    
                    # 3. 測試信號生成器
                    print(f"   🎯 生成交易信號...")
                    signal_generator = SignalGenerator(config)
                    signal = signal_generator.generate_signal(indicators, coin, timeframe)
                    
                    if signal:
                        print(f"   🚨 {coin} 生成交易信號: {signal['action']}")
                    else:
                        print(f"   ⏸️ {coin} 無交易信號")
                    
                    # 4. 測試投資組合管理器
                    print(f"   📈 更新投資組合...")
                    portfolio_manager = PortfolioManager(config)
                    portfolio_manager.update_position(coin, data, signal)
                    print(f"   ✅ {coin} 投資組合更新成功")
                    
                except Exception as e:
                    print(f"   ❌ {coin} 處理失敗: {e}")
                    print(f"   錯誤詳情: {traceback.format_exc()}")
                    continue
        
        print(f"\n" + "="*60)
        print(f"📊 診斷結果:")
        print(f"="*60)
        print(f"如果上面所有步驟都成功，說明組件本身沒有問題")
        print(f"問題可能在於:")
        print(f"1. 主循環中的異常處理")
        print(f"2. Telegram Bot連接問題")
        print(f"3. 健康檢查服務器問題")
        print(f"4. Railway容器資源限制")
        print(f"5. 異步任務管理問題")
        
        # 測試模擬主循環
        print(f"\n🔄 測試模擬主循環...")
        
        try:
            # 模擬運行一個完整週期
            print("   開始策略週期...")
            
            for strategy in strategies:
                coin = strategy["coin"]
                symbol = strategy["symbol"]
                timeframe = strategy["timeframe"]
                
                # 獲取數據
                data = await data_fetcher.get_latest_data(symbol, timeframe)
                if data is None:
                    continue
                
                # 計算指標
                indicators = strategy_engine.calculate_indicators(data, timeframe)
                
                # 生成信號
                signal = signal_generator.generate_signal(indicators, coin, timeframe)
                
                # 更新投資組合
                portfolio_manager.update_position(coin, data, signal)
            
            print("   ✅ 策略週期完成")
            print("   😴 模擬等待下一個週期...")
            
            # 模擬短暫等待
            await asyncio.sleep(2)
            
            print("   ✅ 模擬主循環成功完成")
            
        except Exception as e:
            print(f"   ❌ 模擬主循環失敗: {e}")
            print(f"   錯誤詳情: {traceback.format_exc()}")
        
    except Exception as e:
        print(f"❌ 診斷過程失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_system_crash())

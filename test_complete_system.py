"""
完整系統測試
模擬Railway環境運行，診斷系統為什麼啟動後立即停止

作者: 專業量化策略工程師
"""

import asyncio
import sys
import traceback
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from main import QuantTradingSystem

async def test_complete_system():
    """測試完整系統運行"""
    print("🔍 測試完整聖杯級交易信號系統...")
    print("⚠️  模擬Railway環境運行")
    
    try:
        # 創建系統實例
        print("\n📊 創建系統實例...")
        system = QuantTradingSystem()
        print("✅ 系統實例創建成功")
        
        # 測試啟動過程
        print("\n🚀 測試系統啟動...")
        await system.startup()
        print("✅ 系統啟動成功")
        
        # 測試單次策略週期
        print("\n🔄 測試單次策略週期...")
        await system.run_strategy_cycle()
        print("✅ 策略週期執行成功")
        
        # 測試第二次策略週期（檢查會話重用）
        print("\n🔄 測試第二次策略週期...")
        await system.run_strategy_cycle()
        print("✅ 第二次策略週期執行成功")
        
        # 模擬短時間運行
        print("\n⏱️ 模擬短時間運行（10秒）...")
        system.running = True
        
        start_time = asyncio.get_event_loop().time()
        cycle_count = 0
        
        while system.running and (asyncio.get_event_loop().time() - start_time) < 10:
            try:
                print(f"   🔄 執行週期 {cycle_count + 1}...")
                await system.run_strategy_cycle()
                cycle_count += 1
                
                # 短暫等待
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"   ❌ 週期 {cycle_count + 1} 失敗: {e}")
                print(f"   錯誤詳情: {traceback.format_exc()}")
                break
        
        print(f"✅ 完成 {cycle_count} 個策略週期")
        
        # 測試關閉過程
        print("\n🛑 測試系統關閉...")
        await system.shutdown()
        print("✅ 系統關閉成功")
        
        print(f"\n" + "="*60)
        print(f"📊 完整系統測試結果:")
        print(f"="*60)
        print(f"✅ 系統啟動: 成功")
        print(f"✅ 策略週期: 成功執行 {cycle_count} 次")
        print(f"✅ 系統關閉: 成功")
        print(f"✅ 會話管理: 正常")
        print(f"✅ 異常處理: 正常")
        
        print(f"\n🎯 結論:")
        print(f"系統在本地環境運行正常，如果Railway上仍然有問題，")
        print(f"可能是以下原因:")
        print(f"1. Railway容器資源限制")
        print(f"2. 網絡連接問題")
        print(f"3. 環境變量配置問題")
        print(f"4. Railway平台特定的限制")
        
    except Exception as e:
        print(f"❌ 完整系統測試失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")
        
        print(f"\n🔍 錯誤分析:")
        if "Session is closed" in str(e):
            print("- 會話管理問題：DataFetcher會話被意外關閉")
        elif "Connection" in str(e):
            print("- 網絡連接問題：API連接失敗")
        elif "Telegram" in str(e):
            print("- Telegram Bot問題：Bot連接或消息發送失敗")
        elif "Config" in str(e):
            print("- 配置問題：配置文件或環境變量錯誤")
        else:
            print("- 未知錯誤：需要進一步調查")

async def test_minimal_system():
    """測試最小化系統（不包含Telegram）"""
    print("\n" + "="*60)
    print("🔍 測試最小化系統（排除Telegram影響）...")
    print("="*60)
    
    try:
        from src.data_fetcher import DataFetcher
        from src.strategy_engine import StrategyEngine
        from src.signal_generator import SignalGenerator
        from src.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 測試核心組件
        print("\n📊 測試核心組件...")
        
        async with DataFetcher(config) as data_fetcher:
            strategy_engine = StrategyEngine(config)
            signal_generator = SignalGenerator(config)
            
            strategies = [
                {"coin": "PEPE", "symbol": "1000PEPEUSDT", "timeframe": "1H"},
                {"coin": "XRP", "symbol": "XRPUSDT", "timeframe": "1H"},
                {"coin": "SOL", "symbol": "SOLUSDT", "timeframe": "1H"}
            ]
            
            for strategy in strategies:
                coin = strategy["coin"]
                symbol = strategy["symbol"]
                timeframe = strategy["timeframe"]
                
                print(f"\n🔍 處理 {coin}-{timeframe}...")
                
                # 獲取數據
                data = await data_fetcher.get_latest_data(symbol, timeframe)
                if data is None:
                    print(f"   ❌ {coin} 數據獲取失敗")
                    continue
                
                print(f"   ✅ {coin} 數據獲取成功: {len(data)}條記錄")
                
                # 計算指標
                indicators = strategy_engine.calculate_indicators(data, timeframe)
                if not indicators:
                    print(f"   ❌ {coin} 指標計算失敗")
                    continue
                
                print(f"   ✅ {coin} 指標計算成功")
                
                # 生成信號
                signal = signal_generator.generate_signal(indicators, coin, timeframe)
                if signal:
                    print(f"   🚨 {coin} 生成交易信號: {signal['action']}")
                else:
                    print(f"   ⏸️ {coin} 無交易信號")
        
        print(f"\n✅ 最小化系統測試成功")
        print(f"核心交易邏輯運行正常，問題可能在於Telegram或系統管理部分")
        
    except Exception as e:
        print(f"❌ 最小化系統測試失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_complete_system())
    asyncio.run(test_minimal_system())

"""
多幣種數據獲取系統
測試ETH、SOL、XRP、DOGE、PEPE、BNB、WIF在策略下的表現
使用真實Blave API和Bybit數據，嚴禁模擬數據

作者: 專業量化策略工程師
日期: 2024
"""

import requests
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.org"

# 目標幣種列表
TARGET_COINS = {
    'ETH': 'ETHUSDT',
    'SOL': 'SOLUSDT', 
    'XRP': 'XRPUSDT',
    'DOGE': 'DOGEUSDT',
    'PEPE': 'PEPEUSDT',
    'BNB': 'BNBUSDT',
    'WIF': 'WIFUSDT'
}

def test_coin_data_availability(symbol):
    """測試單個幣種的數據可用性"""
    print(f"\n{'='*50}")
    print(f"測試 {symbol} 數據可用性")
    print(f"{'='*50}")
    
    results = {
        'symbol': symbol,
        'bybit_available': False,
        'blave_concentration_available': False,
        'blave_taker_intensity_available': False,
        'data_points': 0,
        'date_range': None
    }
    
    # 1. 測試Bybit價格數據
    print(f"1. 測試Bybit {symbol}價格數據...")
    
    try:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": symbol,
            "interval": "D",
            "limit": 1000
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("retCode") == 0:
                kline_data = data.get("result", {}).get("list", [])
                
                if kline_data and len(kline_data) > 100:  # 至少100天數據
                    timestamps = [int(candle[0]) for candle in kline_data]
                    earliest_date = datetime.fromtimestamp(min(timestamps) / 1000)
                    latest_date = datetime.fromtimestamp(max(timestamps) / 1000)
                    
                    print(f"   ✅ Bybit數據可用: {len(kline_data)}條記錄")
                    print(f"   時間範圍: {earliest_date.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')}")
                    
                    results['bybit_available'] = True
                    results['data_points'] = len(kline_data)
                    results['date_range'] = (earliest_date, latest_date)
                else:
                    print(f"   ❌ Bybit數據不足: {len(kline_data) if kline_data else 0}條記錄")
            else:
                print(f"   ❌ Bybit API錯誤: {data}")
        else:
            print(f"   ❌ Bybit請求失敗: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Bybit測試失敗: {e}")
    
    # 2. 測試Blave籌碼集中度數據
    print(f"2. 測試Blave {symbol}籌碼集中度數據...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    try:
        url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
        params = {
            "symbol": symbol,
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                alpha_values = data["data"]["alpha"]
                
                if timestamps and len(timestamps) > 50:  # 至少50天數據
                    earliest_date = datetime.fromtimestamp(min(timestamps))
                    latest_date = datetime.fromtimestamp(max(timestamps))
                    
                    print(f"   ✅ 籌碼集中度數據可用: {len(timestamps)}條記錄")
                    print(f"   時間範圍: {earliest_date.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')}")
                    
                    results['blave_concentration_available'] = True
                else:
                    print(f"   ❌ 籌碼集中度數據不足: {len(timestamps) if timestamps else 0}條記錄")
            else:
                print(f"   ❌ 籌碼集中度API回應格式錯誤")
        else:
            print(f"   ❌ 籌碼集中度請求失敗: {response.status_code}")
            if response.status_code == 404:
                print(f"      可能該幣種不支持籌碼集中度數據")
            
    except Exception as e:
        print(f"   ❌ 籌碼集中度測試失敗: {e}")
    
    # 3. 測試Blave Taker Intensity數據
    print(f"3. 測試Blave {symbol} Taker Intensity數據...")
    
    try:
        url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
        params = {
            "symbol": symbol,
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                alpha_values = data["data"]["alpha"]
                
                if timestamps and len(timestamps) > 50:  # 至少50天數據
                    earliest_date = datetime.fromtimestamp(min(timestamps))
                    latest_date = datetime.fromtimestamp(max(timestamps))
                    
                    print(f"   ✅ Taker Intensity數據可用: {len(timestamps)}條記錄")
                    print(f"   時間範圍: {earliest_date.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')}")
                    
                    results['blave_taker_intensity_available'] = True
                else:
                    print(f"   ❌ Taker Intensity數據不足: {len(timestamps) if timestamps else 0}條記錄")
            else:
                print(f"   ❌ Taker Intensity API回應格式錯誤")
        else:
            print(f"   ❌ Taker Intensity請求失敗: {response.status_code}")
            if response.status_code == 404:
                print(f"      可能該幣種不支持Taker Intensity數據")
            
    except Exception as e:
        print(f"   ❌ Taker Intensity測試失敗: {e}")
    
    # 總結
    all_data_available = (results['bybit_available'] and 
                         results['blave_concentration_available'] and 
                         results['blave_taker_intensity_available'])
    
    if all_data_available:
        print(f"\n🎉 {symbol} 所有數據都可用！可以進行策略測試")
    else:
        print(f"\n⚠️ {symbol} 部分數據不可用，無法進行完整策略測試")
        missing = []
        if not results['bybit_available']:
            missing.append("Bybit價格數據")
        if not results['blave_concentration_available']:
            missing.append("籌碼集中度數據")
        if not results['blave_taker_intensity_available']:
            missing.append("Taker Intensity數據")
        print(f"   缺失數據: {', '.join(missing)}")
    
    results['all_data_available'] = all_data_available
    
    return results

def fetch_complete_coin_data(symbol):
    """獲取單個幣種的完整數據"""
    print(f"\n{'='*50}")
    print(f"獲取 {symbol} 完整數據")
    print(f"{'='*50}")
    
    # 1. 獲取Bybit價格數據
    print("1. 獲取Bybit價格數據...")
    
    try:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": symbol,
            "interval": "D",
            "limit": 1000
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("retCode") == 0:
                kline_data = data.get("result", {}).get("list", [])
                
                if kline_data:
                    # 轉換為DataFrame
                    price_df = pd.DataFrame(kline_data)
                    price_df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                    
                    # 轉換數據類型
                    for col in ["open", "high", "low", "close", "volume"]:
                        price_df[col] = pd.to_numeric(price_df[col])
                    
                    price_df["timestamp"] = pd.to_datetime(price_df["timestamp"].astype(float), unit="ms")
                    price_df = price_df.sort_values("timestamp").reset_index(drop=True)
                    
                    print(f"   ✅ 價格數據獲取成功: {len(price_df)}條記錄")
                else:
                    print("   ❌ 無價格數據")
                    return None
            else:
                print(f"   ❌ Bybit API錯誤: {data}")
                return None
        else:
            print(f"   ❌ Bybit請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 價格數據獲取失敗: {e}")
        return None
    
    # 2. 獲取Blave籌碼集中度數據
    print("2. 獲取Blave籌碼集中度數據...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    try:
        url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
        params = {
            "symbol": symbol,
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                hc_values = data["data"]["alpha"]
                
                concentration_df = pd.DataFrame({
                    'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                    'holder_concentration': hc_values
                })
                
                print(f"   ✅ 籌碼集中度數據獲取成功: {len(concentration_df)}條記錄")
            else:
                print("   ❌ 籌碼集中度數據格式錯誤")
                return None
        else:
            print(f"   ❌ 籌碼集中度請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 籌碼集中度數據獲取失敗: {e}")
        return None
    
    # 3. 獲取Blave Taker Intensity數據
    print("3. 獲取Blave Taker Intensity數據...")
    
    try:
        url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
        params = {
            "symbol": symbol,
            "period": "1d",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                ti_values = data["data"]["alpha"]
                
                # 分離多方與空方力道
                long_intensity = []
                short_intensity = []
                
                for ti_val in ti_values:
                    if ti_val > 0:
                        long_intensity.append(ti_val)
                        short_intensity.append(0)
                    else:
                        long_intensity.append(0)
                        short_intensity.append(abs(ti_val))
                
                intensity_df = pd.DataFrame({
                    'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                    'taker_intensity': ti_values,
                    'long_intensity': long_intensity,
                    'short_intensity': short_intensity
                })
                
                print(f"   ✅ Taker Intensity數據獲取成功: {len(intensity_df)}條記錄")
            else:
                print("   ❌ Taker Intensity數據格式錯誤")
                return None
        else:
            print(f"   ❌ Taker Intensity請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Taker Intensity數據獲取失敗: {e}")
        return None
    
    # 4. 合併所有數據
    print("4. 合併所有數據...")
    
    try:
        # 轉換時間戳為日期進行合併
        price_df['date'] = price_df['timestamp'].dt.date
        concentration_df['date'] = concentration_df['timestamp'].dt.date
        intensity_df['date'] = intensity_df['timestamp'].dt.date
        
        # 以價格數據為基準進行合併
        combined_df = price_df.copy()
        
        # 合併籌碼集中度
        combined_df = pd.merge(
            combined_df, 
            concentration_df[['date', 'holder_concentration']], 
            on='date', 
            how='left'
        )
        
        # 合併多空力道
        combined_df = pd.merge(
            combined_df, 
            intensity_df[['date', 'taker_intensity', 'long_intensity', 'short_intensity']], 
            on='date', 
            how='left'
        )
        
        # 清理數據
        combined_df = combined_df.dropna(subset=['holder_concentration', 'taker_intensity'])
        
        if len(combined_df) > 0:
            print(f"   ✅ 數據合併成功: {len(combined_df)}條完整記錄")
            
            # 重命名列以符合策略需求
            combined_df = combined_df.rename(columns={
                'close': 'Close',
                'holder_concentration': 'concentration',
                'long_intensity': 'long_taker_intensity',
                'short_intensity': 'short_taker_intensity'
            })
            
            # 設置時間索引
            combined_df.set_index('timestamp', inplace=True)
            
            # 保存數據
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"real_{symbol.lower()}_data_with_blave_indicators_{timestamp}.csv"
            combined_df.to_csv(filename)
            
            print(f"   ✅ 數據已保存到: {filename}")
            
            return combined_df, filename
        else:
            print("   ❌ 合併後無有效數據")
            return None
            
    except Exception as e:
        print(f"   ❌ 數據合併失敗: {e}")
        return None

def test_all_coins():
    """測試所有目標幣種的數據可用性"""
    
    print("="*80)
    print("多幣種數據可用性測試")
    print("測試幣種: ETH, SOL, XRP, DOGE, PEPE, BNB, WIF")
    print("="*80)
    
    availability_results = {}
    
    for coin_name, symbol in TARGET_COINS.items():
        print(f"\n正在測試 {coin_name} ({symbol})...")
        
        # 添加延遲避免API限制
        time.sleep(2)
        
        result = test_coin_data_availability(symbol)
        availability_results[coin_name] = result
    
    # 生成總結報告
    print("\n" + "="*80)
    print("數據可用性總結報告")
    print("="*80)
    
    available_coins = []
    unavailable_coins = []
    
    for coin_name, result in availability_results.items():
        if result['all_data_available']:
            available_coins.append(coin_name)
            print(f"✅ {coin_name}: 所有數據可用")
        else:
            unavailable_coins.append(coin_name)
            print(f"❌ {coin_name}: 數據不完整")
    
    print(f"\n📊 統計:")
    print(f"   可用幣種: {len(available_coins)}/{len(TARGET_COINS)}")
    print(f"   可測試幣種: {', '.join(available_coins) if available_coins else '無'}")
    print(f"   不可用幣種: {', '.join(unavailable_coins) if unavailable_coins else '無'}")
    
    return availability_results, available_coins

if __name__ == "__main__":
    # 執行多幣種數據可用性測試
    availability_results, available_coins = test_all_coins()
    
    if available_coins:
        print(f"\n🚀 發現 {len(available_coins)} 個可用幣種，準備獲取完整數據...")
        
        # 為每個可用幣種獲取完整數據
        successful_downloads = []
        
        for coin_name in available_coins:
            symbol = TARGET_COINS[coin_name]
            print(f"\n開始獲取 {coin_name} 完整數據...")
            
            # 添加延遲
            time.sleep(3)
            
            result = fetch_complete_coin_data(symbol)
            
            if result is not None:
                df, filename = result
                successful_downloads.append({
                    'coin': coin_name,
                    'symbol': symbol,
                    'filename': filename,
                    'records': len(df)
                })
                print(f"✅ {coin_name} 數據獲取成功")
            else:
                print(f"❌ {coin_name} 數據獲取失敗")
        
        print(f"\n" + "="*60)
        print("數據獲取完成總結")
        print("="*60)
        
        if successful_downloads:
            print(f"✅ 成功獲取 {len(successful_downloads)} 個幣種的完整數據:")
            for item in successful_downloads:
                print(f"   {item['coin']}: {item['records']}條記錄 -> {item['filename']}")
            
            print(f"\n🎯 接下來可以對這些幣種進行策略回測！")
        else:
            print("❌ 沒有成功獲取任何幣種的完整數據")
    
    else:
        print("\n❌ 沒有發現可用的幣種數據，無法進行策略測試")
        print("建議檢查Blave API權限或聯繫API提供商")

#!/usr/bin/env python3
"""
使用現有配置系統測試Taker Intensity + 均線策略
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

async def test_ti_ma_strategy():
    """測試TI+MA策略"""
    
    print("🚀 測試Taker Intensity + 均線策略")
    print("使用現有配置系統和數據獲取器")
    
    # 初始化配置
    try:
        config = ConfigManager()
        data_fetcher = DataFetcher(config)
        
        print("✅ 配置系統初始化成功")
        
    except Exception as e:
        print(f"❌ 配置初始化失敗: {e}")
        return
    
    # 測試幣種和參數
    test_cases = [
        {'symbol': 'BTCUSDT', 'timeframe': '1H'},
        {'symbol': 'ETHUSDT', 'timeframe': '1H'},
        {'symbol': 'SOLUSDT', 'timeframe': '1H'}
    ]
    
    results = []
    
    for case in test_cases:
        symbol = case['symbol']
        timeframe = case['timeframe']
        
        print(f"\n📊 測試 {symbol} {timeframe}...")
        
        try:
            # 獲取數據
            data = await data_fetcher.get_latest_data(symbol, timeframe)

            if data is None or data.empty:
                print(f"❌ {symbol} 數據獲取失敗")
                continue
                
            print(f"✅ {symbol} 數據獲取成功: {len(data)} 條記錄")
            print(f"   時間範圍: {data.index[0]} 到 {data.index[-1]}")
            
            # 檢查必要的列
            required_columns = ['Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} 缺少必要列: {missing_columns}")
                print(f"   可用列: {list(data.columns)}")
                continue
            
            # 計算Taker Intensity淨值
            data['taker_intensity'] = data['long_taker_intensity'] - data['short_taker_intensity']
            
            # 計算均線
            data['MA_10'] = data['Close'].rolling(window=10).mean()
            data['MA_20'] = data['Close'].rolling(window=20).mean()
            
            # 生成信號
            signals = generate_ti_ma_signals(data)
            
            if signals:
                # 計算表現
                performance = calculate_performance(signals, data)
                
                result = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'data_points': len(data),
                    'signals': len(signals),
                    **performance
                }
                
                results.append(result)
                
                print(f"✅ {symbol} 策略測試完成:")
                print(f"   信號數量: {len(signals)}")
                print(f"   勝率: {performance['win_rate']:.1f}%")
                print(f"   總收益: {performance['total_return']:+.2f}%")
                print(f"   風險回報比: {performance['risk_reward_ratio']:.2f}")
            else:
                print(f"❌ {symbol} 無有效信號")
                
        except Exception as e:
            print(f"❌ {symbol} 測試失敗: {e}")
            continue
    
    # 關閉數據獲取器
    if hasattr(data_fetcher, 'session') and data_fetcher.session:
        await data_fetcher.session.close()
    
    # 總結結果
    if results:
        print(f"\n🏆 策略測試總結:")
        print("="*60)
        
        for result in results:
            print(f"{result['symbol']} {result['timeframe']}:")
            print(f"  勝率: {result['win_rate']:.1f}% | 收益: {result['total_return']:+.2f}% | RR: {result['risk_reward_ratio']:.2f}")
        
        # 找出最佳策略
        best_result = max(results, key=lambda x: x['risk_reward_ratio'] * (x['win_rate']/100))
        
        print(f"\n🥇 最佳策略: {best_result['symbol']} {best_result['timeframe']}")
        print(f"   風險回報比: {best_result['risk_reward_ratio']:.2f}")
        print(f"   勝率: {best_result['win_rate']:.1f}%")
        print(f"   總收益: {best_result['total_return']:+.2f}%")
        
    else:
        print(f"\n❌ 所有測試都失敗了")

def generate_ti_ma_signals(data: pd.DataFrame, ti_threshold: float = 0.3) -> list:
    """生成TI+MA信號"""
    signals = []
    
    # 確保有足夠的數據
    if len(data) < 20:
        return signals
    
    for i in range(20, len(data)):
        price = data['Close'].iloc[i]
        prev_price = data['Close'].iloc[i-1]
        ti = data['taker_intensity'].iloc[i]
        ma10 = data['MA_10'].iloc[i]
        ma20 = data['MA_20'].iloc[i]
        prev_ma10 = data['MA_10'].iloc[i-1]
        
        # 跳過NaN值
        if pd.isna(ma10) or pd.isna(ma20) or pd.isna(ti):
            continue
        
        # 多頭信號：價格突破MA10 + TI > 閾值
        if (price > ma10 and prev_price <= prev_ma10 and ti > ti_threshold):
            signals.append({
                'timestamp': data.index[i],
                'type': 'LONG',
                'price': price,
                'ti': ti,
                'ma10': ma10,
                'ma20': ma20
            })
        
        # 空頭信號：價格跌破MA10 + TI < -閾值
        elif (price < ma10 and prev_price >= prev_ma10 and ti < -ti_threshold):
            signals.append({
                'timestamp': data.index[i],
                'type': 'SHORT',
                'price': price,
                'ti': ti,
                'ma10': ma10,
                'ma20': ma20
            })
    
    return signals

def calculate_performance(signals: list, data: pd.DataFrame) -> dict:
    """計算策略表現"""
    if len(signals) < 2:
        return {
            'total_return': 0,
            'win_rate': 0,
            'risk_reward_ratio': 0,
            'max_drawdown': 0,
            'total_trades': 0
        }
    
    trades = []
    
    # 簡單配對交易
    for i in range(0, len(signals)-1, 2):
        entry = signals[i]
        exit_signal = signals[i+1] if i+1 < len(signals) else None
        
        if exit_signal:
            if entry['type'] == 'LONG':
                pnl_pct = (exit_signal['price'] / entry['price'] - 1) * 100
            else:
                pnl_pct = (entry['price'] / exit_signal['price'] - 1) * 100
            
            trades.append(pnl_pct)
    
    if not trades:
        return {
            'total_return': 0,
            'win_rate': 0,
            'risk_reward_ratio': 0,
            'max_drawdown': 0,
            'total_trades': 0
        }
    
    # 計算指標
    total_return = sum(trades)
    win_trades = [t for t in trades if t > 0]
    loss_trades = [t for t in trades if t < 0]
    
    win_rate = len(win_trades) / len(trades) * 100
    avg_win = np.mean(win_trades) if win_trades else 0
    avg_loss = np.mean(loss_trades) if loss_trades else 0
    risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
    
    # 計算最大回撤
    cumulative = np.cumsum(trades)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    max_drawdown = np.min(drawdown)
    
    return {
        'total_return': total_return,
        'win_rate': win_rate,
        'risk_reward_ratio': risk_reward_ratio,
        'max_drawdown': max_drawdown,
        'total_trades': len(trades),
        'avg_win': avg_win,
        'avg_loss': avg_loss
    }

if __name__ == "__main__":
    asyncio.run(test_ti_ma_strategy())

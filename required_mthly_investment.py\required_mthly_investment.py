import pandas as pd
import numpy as np
from scipy.optimize import minimize, fsolve
import matplotlib.pyplot as plt
from matplotlib.widgets import Cursor

# 用户自定义输入部分
user_input = {
    "expected_portfolio_std": 0.30,  # 期望的投资组合标准差（用户风险承受能力）
    "risk_free_rate": 0.00,  # 无风险收益率 (3%)
    "desired_final_balance": 300000,  # 目标最终资金金额 (单位: USD)
    "time_to_hit_goal_years": 2,  # 目标时间（年）
    "time_to_hit_goal_months": 6,  # 目标时间（月）
    "products": {
        "Arbitrage": {"std": 0.005, "sharpe": 9.0}, #VOLATILITY
        "CTA Algo": {"std": 0.25, "sharpe": 2.9}, #ALPHA
        "Spot Index": {"std": 0.6, "sharpe": 1.2}, #BETA
    },
}

# Excel Standard Deviation = STDEV.S(Daily_PnL column) * sqrt(365)
# Excel Sharpe Ratio = AVERAGE(Daily_PnL column) / STDEV.S(Daily_PnL column) * sqrt(365)

# 设置时间范围和初始资金
days_per_year = 365  # 每年交易日数量
days_per_month = 30   # 每月交易日数量
initial_capital = 100000  # 初始资金
desired_final_balance = user_input["desired_final_balance"]

# 计算目标时间内的总交易日
total_days = (user_input["time_to_hit_goal_years"] * days_per_year) + (user_input["time_to_hit_goal_months"] * days_per_month)

# 提取产品数据
product_names = list(user_input["products"].keys())
std_values = np.array([user_input["products"][p]["std"] for p in product_names])
sharpe_ratios = np.array([user_input["products"][p]["sharpe"] for p in product_names])
risk_free_rate = user_input["risk_free_rate"]

# 计算基于年化 Sharpe Ratio 的预期年化收益率
annual_returns = risk_free_rate + sharpe_ratios * std_values

# 生成每日收益率数据（假设正态分布）
daily_returns = np.random.normal(
    loc=annual_returns / days_per_year,
    scale=std_values / np.sqrt(days_per_year),
    size=(total_days, len(product_names))
)

# 计算最优权重，使组合标准差接近目标值
target_std = user_input["expected_portfolio_std"]
weights = np.array([1/3, 1/3, 1/3])

def portfolio_std(weights, std_values):
    return np.sqrt(np.sum((weights * std_values) ** 2))

constraints = ({"type": "eq", "fun": lambda w: np.sum(w) - 1})
bounds = [(0, 1) for _ in weights]

result = minimize(
    lambda w: abs(portfolio_std(w, std_values) - target_std),
    weights,
    bounds=bounds,
    constraints=constraints,
)
optimal_weights = result.x

# 计算各产品独立资金曲线（初始投资）
product_curves = pd.DataFrame(
    (1 + daily_returns).cumprod(axis=0) * initial_capital,
    columns=product_names
)

# 计算组合收益率和资金曲线
portfolio_returns = np.dot(daily_returns, optimal_weights)
portfolio_curve = pd.Series(
    (1 + portfolio_returns).cumprod() * initial_capital,
    name="Portfolio (Initial Investment)"
)

# 计算所需的每月追加投资金额
def future_value_with_contributions(monthly_investment):
    portfolio_curve_with_contributions = portfolio_curve.copy()
    for day in range(1, total_days):
        portfolio_curve_with_contributions[day] *= (1 + portfolio_returns[day])
        if day % days_per_month == 0:
            portfolio_curve_with_contributions[day:] += monthly_investment * (
                portfolio_curve_with_contributions[day:] / portfolio_curve_with_contributions[day]
            )
    return portfolio_curve_with_contributions.iloc[-1]

# 使用 fsolve 求解每月需要的追加投资金额
def investment_goal_function(monthly_investment):
    final_value = future_value_with_contributions(monthly_investment)
    return final_value - desired_final_balance

initial_guess = 1000  # 初始猜测值
calculated_monthly_investment = fsolve(investment_goal_function, initial_guess)[0]
print(f"Calculated Monthly Investment: {calculated_monthly_investment:.2f} USD")

# 计算包括每月追加投资的资金曲线
portfolio_curve_with_investment = portfolio_curve.copy()
total_investment = initial_capital

for day in range(1, total_days):
    portfolio_curve_with_investment[day] *= (1 + portfolio_returns[day])
    if day % days_per_month == 0:
        total_investment += calculated_monthly_investment
        portfolio_curve_with_investment[day:] += calculated_monthly_investment * (
            portfolio_curve_with_investment[day:] / portfolio_curve_with_investment[day]
        )

# 添加各产品的最优权重信息
weight_info = dict(zip(product_names, optimal_weights))
weight_text = "\n".join([f"{k}: {v:.2%}" for k, v in weight_info.items()])

# 创建本地可视化图表
plt.figure(figsize=(10, 6))

# 绘制各个产品的资金曲线
for column in product_curves.columns:
    plt.plot(product_curves.index, product_curves[column], label=column)

# 绘制仅初始投资的组合资金曲线
plt.plot(portfolio_curve.index, portfolio_curve, label="Portfolio (Initial Investment)", linestyle='--', color='gray')

# 绘制包含计算出每月投资的组合资金曲线
plt.plot(portfolio_curve_with_investment.index, portfolio_curve_with_investment, label="Portfolio (With Calculated Monthly Investment)", color='green')

# 设置标题和标签
plt.title('Equity Curve (Required monthly investment)')
plt.xlabel('Trading Days')
plt.ylabel('Equity Curve(USD)')

# 添加图例
plt.legend(loc='upper left', fontsize=10, frameon=True, edgecolor='gray')

# 计算总投资金额（初始投资 + 每月累计投资）
total_investment_amount = initial_capital + calculated_monthly_investment * (total_days // days_per_month)

print(f"Total Invested Amount: {total_investment_amount:.2f} USD")

# 更新投资信息文本
investment_info_text = f"Required Monthly Investment: {calculated_monthly_investment:.2f} USD\nTotal Invested Amount: {total_investment_amount:.2f} USD\nInitial Investment: {initial_capital:.2f} USD\nTime to Goal: {total_days // days_per_year} years, {(total_days % days_per_year) // days_per_month} months"

# 绘制总投资金额和初始投资金额的水平线
plt.axhline(y=total_investment_amount, color='blue', linestyle='--', label=f'Total Invested: {total_investment_amount:.2f} USD')
plt.axhline(y=initial_capital, color='purple', linestyle='-.', label=f'Initial Investment: {initial_capital:.2f} USD')

# 重新绘制文本信息到图表中
plt.text(
    0.02, 0.02, f"Optimal Weights:\n{weight_text}\n{investment_info_text}",
    transform=plt.gca().transAxes,
    fontsize=10,
    verticalalignment='bottom',
    bbox=dict(boxstyle='round,pad=0.5', edgecolor='gray', facecolor='white', alpha=0.6)
)

# 添加鼠标悬停交互效果
cursor = Cursor(plt.gca(), useblit=True, color='red', linewidth=1)

# 显示图形
plt.tight_layout()
plt.show()

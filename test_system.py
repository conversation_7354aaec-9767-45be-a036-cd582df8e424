#!/usr/bin/env python3
"""
聖杯級交易信號系統測試腳本
驗證系統各個組件是否正常工作

作者: 專業量化策略工程師
"""

import asyncio
import sys
from pathlib import Path

# 添加src目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher
from src.strategy_engine import StrategyEngine
from src.signal_generator import SignalGenerator
from src.telegram_bot import TelegramBot
from src.portfolio_manager import PortfolioManager

async def test_system():
    """測試系統各個組件"""
    print("🚀 開始測試聖杯級交易信號系統...")
    
    try:
        # 1. 測試配置管理器
        print("\n📋 測試配置管理器...")
        config = ConfigManager()
        print(f"✅ 系統名稱: {config.get('system.name')}")
        print(f"✅ 系統版本: {config.get('system.version')}")
        
        # 檢查API密鑰
        blave_key = config.get_api_key('blave', 'api_key')
        if blave_key:
            print(f"✅ Blave API密鑰已配置: {blave_key[:20]}...")
        else:
            print("⚠️ Blave API密鑰未配置")
        
        # 2. 測試策略引擎
        print("\n🧮 測試策略引擎...")
        strategy_engine = StrategyEngine(config)
        
        # 測試策略參數
        params_1h = strategy_engine._get_strategy_params('1H')
        print(f"✅ 1H策略參數: CCB窗口={params_1h['ccb_window']}, 標準差={params_1h['ccb_std']}")
        
        # 3. 測試信號生成器
        print("\n🎯 測試信號生成器...")
        signal_generator = SignalGenerator(config)
        positions = signal_generator.get_all_positions()
        print(f"✅ 初始持倉狀態: {list(positions.keys())}")
        
        # 4. 測試投資組合管理器
        print("\n💼 測試投資組合管理器...")
        portfolio_manager = PortfolioManager(config)
        print("✅ 投資組合管理器初始化成功")
        
        # 5. 測試Telegram Bot
        print("\n📱 測試Telegram Bot...")
        telegram_bot = TelegramBot(config)
        await telegram_bot.start()
        
        # 發送測試消息
        test_message = """
🧪 系統測試消息
━━━━━━━━━━━━━━━━━━━━
✅ 聖杯級交易信號系統測試成功！
📊 所有組件運行正常
🕐 測試時間: 現在
━━━━━━━━━━━━━━━━━━━━
        """
        
        success = await telegram_bot.send_message(test_message)
        if success:
            print("✅ Telegram通知測試成功")
        else:
            print("⚠️ Telegram通知測試失敗（可能是配置問題）")
        
        await telegram_bot.stop()
        
        # 6. 測試數據獲取器（簡單測試）
        print("\n📊 測試數據獲取器...")
        data_fetcher = DataFetcher(config)
        print("✅ 數據獲取器初始化成功")
        
        # 注意：實際的數據獲取需要網絡連接，這裡只測試初始化
        
        print("\n🎉 系統測試完成！")
        print("=" * 50)
        print("📋 測試結果總結:")
        print("✅ 配置管理器: 正常")
        print("✅ 策略引擎: 正常")
        print("✅ 信號生成器: 正常")
        print("✅ 投資組合管理器: 正常")
        print("✅ 數據獲取器: 正常")
        print(f"{'✅' if success else '⚠️'} Telegram Bot: {'正常' if success else '需要配置'}")
        print("=" * 50)
        
        if not success:
            print("\n📝 配置提醒:")
            print("1. 請在 config/api_keys.yaml 中配置 Telegram Bot Token 和 Chat ID")
            print("2. 或在 .env 文件中設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID")
            print("3. 配置完成後重新運行測試")
        
        print("\n🚀 系統已準備就緒，可以開始運行！")
        print("運行命令: python main.py")
        
    except Exception as e:
        print(f"❌ 系統測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 聖杯級交易信號系統 - 系統測試")
    print("=" * 50)
    asyncio.run(test_system())

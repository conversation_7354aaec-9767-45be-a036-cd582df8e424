"""
機構級籌碼集中帶 + 多空力道策略回測系統
基於真實Blave API數據和Bybit BTC永續合約數據
參考QUANT資料夾中的布林帶策略範本

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_real_data():
    """載入真實數據"""
    print("載入真實市場數據...")
    
    # 查找最新的真實數據文件
    import glob
    csv_files = glob.glob("real_btc_data_with_blave_indicators_*.csv")
    
    if not csv_files:
        print("❌ 找不到真實數據文件，請先運行 real_data_fetcher.py")
        return None
    
    # 使用最新的文件
    latest_file = max(csv_files)
    print(f"載入數據文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # 重命名列以符合策略需求
    df = df.rename(columns={
        'close': 'Close',
        'holder_concentration': 'concentration',
        'long_intensity': 'long_taker_intensity',
        'short_intensity': 'short_taker_intensity'
    })
    
    print(f"✅ 真實數據載入完成：{len(df)}條記錄")
    print(f"時間範圍：{df.index[0]} 至 {df.index[-1]}")
    print(f"BTC價格範圍：${df['Close'].min():.0f} - ${df['Close'].max():.0f}")
    
    return df

def chip_concentration_bands(df, column='concentration', window=20, std_dev=2):
    """
    計算籌碼集中帶 (參考布林帶邏輯)
    """
    df['CCB_Middle'] = df[column].rolling(window=window).mean()
    df['CCB_Upper'] = df['CCB_Middle'] + (df[column].rolling(window=window).std() * std_dev)
    df['CCB_Lower'] = df['CCB_Middle'] - (df[column].rolling(window=window).std() * std_dev)
    
    return df

def taker_intensity_signals(df, lookback_period=30, percentile_threshold=60):
    """
    計算多空力道信號 (使用滾動窗口百分位數)
    """
    # 計算滾動百分位數
    df['Long_Signal'] = df['long_taker_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    df['Short_Signal'] = df['short_taker_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    return df

def ccb_taker_entry_logic(df):
    """
    籌碼集中帶 + 多空力道入場邏輯
    參考布林帶策略的信號延續邏輯
    """
    df['Signal'] = 0  # 默認無倉位
    
    # 多頭信號：籌碼集中度跌破下軌 AND 多方力道確認
    long_condition = (df['concentration'] < df['CCB_Lower']) & df['Long_Signal']
    df.loc[long_condition, 'Signal'] = 1
    
    # 空頭信號：籌碼集中度突破上軌 AND 空方力道確認  
    short_condition = (df['concentration'] > df['CCB_Upper']) & df['Short_Signal']
    df.loc[short_condition, 'Signal'] = -1
    
    # 信號延續邏輯 (參考布林帶策略)
    df['Signal'] = df['Signal'].replace(0, np.nan).ffill().fillna(0)
    
    return df

def optimize_parameters_sharpe(df):
    """
    參數優化 - 使用夏普比率 (參考QUANT範本)
    """
    print("開始參數優化 (目標: 夏普比率)...")
    
    best_sr = -np.inf
    best_params = {}
    
    # 參數範圍
    ccb_windows = range(15, 31, 5)  # CCB窗口期
    ccb_stds = np.arange(1.5, 3.1, 0.3)  # 標準差倍數
    taker_lookbacks = range(20, 41, 5)  # 力道回望期
    taker_thresholds = range(50, 81, 10)  # 百分位數閾值
    
    total_combinations = len(ccb_windows) * len(ccb_stds) * len(taker_lookbacks) * len(taker_thresholds)
    current_combination = 0
    
    for ccb_window in ccb_windows:
        for ccb_std in ccb_stds:
            for taker_lookback in taker_lookbacks:
                for taker_threshold in taker_thresholds:
                    current_combination += 1
                    
                    try:
                        # 計算指標和信號
                        df_temp = df.copy()
                        df_temp = chip_concentration_bands(df_temp, window=ccb_window, std_dev=ccb_std)
                        df_temp = taker_intensity_signals(df_temp, lookback_period=taker_lookback, percentile_threshold=taker_threshold)
                        df_temp = ccb_taker_entry_logic(df_temp)
                        
                        # 計算收益
                        df_temp['price_chg'] = df_temp['Close'].pct_change()
                        df_temp['pnl'] = df_temp['Signal'].shift(1) * df_temp['price_chg']
                        
                        # 計算夏普比率
                        pnl = df_temp['pnl'].dropna()
                        if len(pnl) > 50 and pnl.std() > 0:
                            sr = pnl.mean() / pnl.std() * np.sqrt(365)
                            
                            if sr > best_sr:
                                best_sr = sr
                                best_params = {
                                    'ccb_window': ccb_window,
                                    'ccb_std': round(ccb_std, 1),
                                    'taker_lookback': taker_lookback,
                                    'taker_threshold': taker_threshold,
                                    'sharpe_ratio': round(sr, 4)
                                }
                    
                    except Exception as e:
                        continue
                    
                    # 進度顯示
                    if current_combination % 50 == 0:
                        progress = (current_combination / total_combinations) * 100
                        print(f"優化進度: {progress:.1f}% ({current_combination}/{total_combinations})")
    
    print(f"✅ 參數優化完成！最佳夏普比率: {best_sr:.4f}")
    print(f"最佳參數: {best_params}")
    
    return best_params

def optimize_parameters_profit_factor(df):
    """
    參數優化 - 使用盈虧比 (參考QUANT範本)
    """
    print("開始參數優化 (目標: 盈虧比)...")
    
    best_pf = 0
    best_params = {}
    
    # 參數範圍 (較小範圍以加快速度)
    ccb_windows = range(15, 31, 5)
    ccb_stds = np.arange(1.5, 3.1, 0.5)
    taker_lookbacks = range(20, 41, 10)
    taker_thresholds = range(50, 81, 15)
    
    total_combinations = len(ccb_windows) * len(ccb_stds) * len(taker_lookbacks) * len(taker_thresholds)
    current_combination = 0
    
    for ccb_window in ccb_windows:
        for ccb_std in ccb_stds:
            for taker_lookback in taker_lookbacks:
                for taker_threshold in taker_thresholds:
                    current_combination += 1
                    
                    try:
                        # 計算指標和信號
                        df_temp = df.copy()
                        df_temp = chip_concentration_bands(df_temp, window=ccb_window, std_dev=ccb_std)
                        df_temp = taker_intensity_signals(df_temp, lookback_period=taker_lookback, percentile_threshold=taker_threshold)
                        df_temp = ccb_taker_entry_logic(df_temp)
                        
                        # 計算收益
                        df_temp['price_chg'] = df_temp['Close'].pct_change()
                        df_temp['pnl'] = df_temp['Signal'].shift(1) * df_temp['price_chg']
                        
                        # 計算盈虧比
                        pnl = df_temp['pnl'].dropna()
                        if len(pnl) > 30:
                            pf = pnl[pnl > 0].sum() / abs(pnl[pnl < 0].sum()) if (pnl < 0).any() else 0
                            
                            if pf > best_pf:
                                best_pf = pf
                                best_params = {
                                    'ccb_window': ccb_window,
                                    'ccb_std': round(ccb_std, 1),
                                    'taker_lookback': taker_lookback,
                                    'taker_threshold': taker_threshold,
                                    'profit_factor': round(pf, 4)
                                }
                    
                    except Exception as e:
                        continue
                    
                    # 進度顯示
                    if current_combination % 20 == 0:
                        progress = (current_combination / total_combinations) * 100
                        print(f"優化進度: {progress:.1f}% ({current_combination}/{total_combinations})")
    
    print(f"✅ 參數優化完成！最佳盈虧比: {best_pf:.4f}")
    print(f"最佳參數: {best_params}")
    
    return best_params

def run_backtest_with_params(df, params):
    """使用最佳參數運行回測"""
    print("使用最佳參數運行完整回測...")
    
    # 應用最佳參數
    df = chip_concentration_bands(df, window=params['ccb_window'], std_dev=params['ccb_std'])
    df = taker_intensity_signals(df, lookback_period=params['taker_lookback'], percentile_threshold=params['taker_threshold'])
    df = ccb_taker_entry_logic(df)
    
    # 計算收益
    df['price_chg'] = df['Close'].pct_change()
    df['pnl'] = df['Signal'].shift(1) * df['price_chg']
    
    # 計算績效指標
    pnl = df['pnl'].dropna()
    
    if len(pnl) > 0:
        # 基本統計
        total_return = (1 + pnl).prod() - 1
        annual_return = (1 + total_return) ** (365 / len(pnl)) - 1
        volatility = pnl.std() * np.sqrt(365)
        sharpe_ratio = pnl.mean() / pnl.std() * np.sqrt(365) if pnl.std() > 0 else 0
        
        # 最大回撤
        cumulative = (1 + pnl).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 盈虧比
        profit_factor = pnl[pnl > 0].sum() / abs(pnl[pnl < 0].sum()) if (pnl < 0).any() else np.inf
        
        # 勝率
        win_rate = (pnl > 0).mean()
        
        # 交易次數
        total_trades = len(pnl[pnl != 0])
        
        performance = {
            'Total Return': f"{total_return:.2%}",
            'Annual Return': f"{annual_return:.2%}",
            'Sharpe Ratio': f"{sharpe_ratio:.4f}",
            'Max Drawdown': f"{max_drawdown:.2%}",
            'Profit Factor': f"{profit_factor:.2f}",
            'Win Rate': f"{win_rate:.2%}",
            'Total Trades': total_trades
        }
        
        print("\n=== 機構級回測績效結果 ===")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        # 檢查目標達成
        if sharpe_ratio >= 1.5:
            print(f"\n🎉 目標達成！Sharpe Ratio = {sharpe_ratio:.4f} ≥ 1.5")
        else:
            print(f"\n⚠️ 目標未達成：Sharpe Ratio = {sharpe_ratio:.4f} < 1.5")
        
        return df, performance
    
    else:
        print("❌ 無有效交易數據")
        return df, {}

def plot_institutional_results(df, params, performance):
    """繪製機構級回測結果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 價格和籌碼集中帶
    ax1 = axes[0, 0]
    ax1.plot(df.index, df['Close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--', alpha=0.8)
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--', alpha=0.8)
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['Close'], color='green', marker='^', s=30, label='Buy')
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['Close'], color='red', marker='v', s=30, label='Sell')
    
    ax1.set_title('籌碼集中帶 + 多空力道策略 (真實數據)')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 多空力道
    axes[0, 1].plot(df.index, df['long_taker_intensity'], label='多方力道', color='green', alpha=0.7)
    axes[0, 1].plot(df.index, df['short_taker_intensity'], label='空方力道', color='red', alpha=0.7)
    axes[0, 1].axhline(y=params['taker_threshold'], color='blue', linestyle='--', alpha=0.5, 
                      label=f'閾值 ({params["taker_threshold"]}%)')
    axes[0, 1].set_title('TAKER INTENSITY - 多空力道')
    axes[0, 1].set_ylabel('力道值')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 累積收益
    df['cumulative_strategy'] = (1 + df['pnl'].fillna(0)).cumprod()
    df['cumulative_benchmark'] = (1 + df['price_chg'].fillna(0)).cumprod()
    
    axes[1, 0].plot(df.index, df['cumulative_strategy'], label='策略', color='blue', linewidth=2)
    axes[1, 0].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[1, 0].set_title('累積收益比較')
    axes[1, 0].set_ylabel('累積收益')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 績效總結
    axes[1, 1].axis('off')
    
    perf_text = "=== 機構級策略績效 ===\n\n"
    for key, value in performance.items():
        perf_text += f"{key}: {value}\n"
    
    perf_text += f"\n=== 最優參數 ===\n"
    for key, value in params.items():
        if key not in ['sharpe_ratio', 'profit_factor']:
            perf_text += f"{key}: {value}\n"
    
    axes[1, 1].text(0.1, 0.9, perf_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print("機構級策略圖表已生成完成！")

def run_institutional_backtest():
    """運行完整的機構級回測"""
    
    print("="*80)
    print("機構級籌碼集中帶 + 多空力道策略回測系統")
    print("基於真實Blave API數據和Bybit BTC永續合約數據")
    print("目標：Sharpe Ratio ≥ 1.5")
    print("="*80)
    
    # 1. 載入真實數據
    market_data = load_real_data()
    
    if market_data is None:
        return None
    
    # 2. 參數優化 (使用夏普比率)
    best_params = optimize_parameters_sharpe(market_data)
    
    if best_params is None:
        print("❌ 參數優化失敗")
        return None
    
    # 3. 完整回測
    final_df, performance = run_backtest_with_params(market_data, best_params)
    
    # 4. 可視化結果
    plot_institutional_results(final_df, best_params, performance)
    
    # 5. 保存結果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Institutional_CCB_TakerIntensity_Backtest_{timestamp}.csv"
    final_df.to_csv(filename)
    print(f"\n✅ 機構級回測結果已保存到: {filename}")
    
    return final_df, best_params, performance

if __name__ == "__main__":
    # 執行機構級回測
    results = run_institutional_backtest()

"""
Telegram Bot模組
發送交易信號和報告到Telegram

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
from typing import Optional
from datetime import datetime
from .logger_setup import setup_logger

class TelegramBot:
    """Telegram通知機器人"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("TelegramBot")
        
        self.bot_token = config.get_api_key('telegram', 'bot_token')
        self.chat_id = config.get_api_key('telegram', 'chat_id')
        
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.session = None
        
        # 檢查配置
        if not self.bot_token or not self.chat_id:
            self.logger.warning("⚠️ Telegram配置不完整，將使用模擬模式")
            self.enabled = False
        else:
            self.enabled = True
    
    async def start(self):
        """啟動Telegram Bot"""
        try:
            if not self.enabled:
                self.logger.info("📱 Telegram Bot模擬模式啟動")
                return
            
            self.session = aiohttp.ClientSession()
            
            # 測試連接
            await self.test_connection()
            
            self.logger.info("📱 Telegram Bot啟動成功")
            
        except Exception as e:
            self.logger.error(f"❌ Telegram Bot啟動失敗: {e}")
            self.enabled = False
    
    async def stop(self):
        """停止Telegram Bot"""
        try:
            if self.session:
                await self.session.close()
            self.logger.info("📱 Telegram Bot已停止")
        except Exception as e:
            self.logger.error(f"❌ Telegram Bot停止失敗: {e}")
    
    async def test_connection(self):
        """測試Telegram連接"""
        try:
            url = f"{self.base_url}/getMe"
            
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('ok'):
                        bot_info = data.get('result', {})
                        self.logger.info(f"✅ Telegram Bot連接成功: {bot_info.get('username', 'Unknown')}")
                        return True
                
                self.logger.error(f"❌ Telegram Bot連接失敗: {response.status}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Telegram連接測試失敗: {e}")
            return False
    
    async def send_message(self, message: str, parse_mode: str = 'HTML') -> bool:
        """發送消息到Telegram"""
        try:
            if not self.enabled:
                # 模擬模式：只記錄到日誌
                self.logger.info(f"📱 [模擬] Telegram消息:\n{message}")
                return True
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            url = f"{self.base_url}/sendMessage"
            
            payload = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode,
                'disable_web_page_preview': True
            }
            
            async with self.session.post(url, json=payload, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('ok'):
                        self.logger.info("✅ Telegram消息發送成功")
                        return True
                    else:
                        self.logger.error(f"❌ Telegram API錯誤: {data}")
                        return False
                else:
                    self.logger.error(f"❌ Telegram請求失敗: {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Telegram消息發送失敗: {e}")
            return False
    
    async def send_signal_alert(self, signal: dict) -> bool:
        """發送交易信號警報"""
        try:
            coin = signal.get('coin', 'Unknown')
            action = signal.get('action', 'Unknown')
            price = signal.get('price', 0)
            timestamp = signal.get('timestamp', datetime.now())
            
            # 信號類型emoji
            action_emoji = {
                'LONG': '📈',
                'SHORT': '📉', 
                'CLOSE_LONG': '🔒',
                'CLOSE_SHORT': '🔓',
                'HOLD': '⏸️'
            }
            
            emoji = action_emoji.get(action, '❓')
            
            # 構建消息
            message = f"""
{emoji} <b>{coin}-1H 交易信號</b>
━━━━━━━━━━━━━━━━━━━━
📊 <b>信號類型:</b> {action}
💰 <b>當前價格:</b> ${price:.6f}
📈 <b>籌碼集中度:</b> {signal.get('concentration', 'N/A'):.4f}
📍 <b>CCB位置:</b> {signal.get('ccb_position', 'N/A')}
⚡ <b>多方力道:</b> {signal.get('long_percentile', 0):.1f}%
⚡ <b>空方力道:</b> {signal.get('short_percentile', 0):.1f}%
🎯 <b>置信度:</b> {signal.get('confidence', 0.5):.1%}
🕐 <b>信號時間:</b> {timestamp.strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
            """
            
            return await self.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 信號警報發送失敗: {e}")
            return False
    
    async def send_pnl_report(self, pnl_data: dict) -> bool:
        """發送盈虧報告"""
        try:
            coin = pnl_data.get('coin', 'Unknown')
            trade_type = pnl_data.get('trade_type', 'Unknown')
            pnl_pct = pnl_data.get('pnl_pct', 0)
            pnl_amount = pnl_data.get('pnl_amount', 0)
            entry_price = pnl_data.get('entry_price', 0)
            exit_price = pnl_data.get('exit_price', 0)
            holding_hours = pnl_data.get('holding_hours', 0)
            exit_time = pnl_data.get('exit_time', datetime.now())
            
            # 盈虧emoji
            profit_emoji = '💰' if pnl_pct > 0 else '💸'
            
            message = f"""
{profit_emoji} <b>{coin}-1H 交易結算</b>
━━━━━━━━━━━━━━━━━━━━
📊 <b>交易類型:</b> {trade_type}
💵 <b>開倉價格:</b> ${entry_price:.6f}
💵 <b>平倉價格:</b> ${exit_price:.6f}
📈 <b>收益率:</b> {pnl_pct:+.2f}%
💰 <b>盈虧金額:</b> ${pnl_amount:+.2f}
⏱️ <b>持倉時間:</b> {holding_hours:.1f}小時
🕐 <b>結算時間:</b> {exit_time.strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
            """
            
            return await self.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 盈虧報告發送失敗: {e}")
            return False
    
    async def send_daily_report(self, report_data: dict) -> bool:
        """發送每日報告"""
        try:
            date = report_data.get('date', datetime.now().strftime('%Y-%m-%d'))
            total_trades = report_data.get('total_trades', 0)
            winning_trades = report_data.get('winning_trades', 0)
            total_pnl = report_data.get('total_pnl', 0)
            win_rate = report_data.get('win_rate', 0)
            
            message = f"""
📊 <b>每日交易報告 - {date}</b>
━━━━━━━━━━━━━━━━━━━━
📈 <b>總交易次數:</b> {total_trades}
🎯 <b>獲利交易:</b> {winning_trades}
📊 <b>勝率:</b> {win_rate:.1f}%
💰 <b>總盈虧:</b> ${total_pnl:+.2f}
━━━━━━━━━━━━━━━━━━━━

<b>各幣種表現:</b>
            """
            
            # 添加各幣種詳情
            for coin, data in report_data.get('coin_details', {}).items():
                coin_pnl = data.get('pnl', 0)
                coin_trades = data.get('trades', 0)
                emoji = '📈' if coin_pnl > 0 else '📉' if coin_pnl < 0 else '➖'
                
                message += f"\n{emoji} <b>{coin}:</b> ${coin_pnl:+.2f} ({coin_trades}筆)"
            
            message += "\n━━━━━━━━━━━━━━━━━━━━"
            
            return await self.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 每日報告發送失敗: {e}")
            return False
    
    async def send_error_alert(self, error_msg: str, component: str = "System") -> bool:
        """發送錯誤警報"""
        try:
            message = f"""
🚨 <b>系統錯誤警報</b>
━━━━━━━━━━━━━━━━━━━━
🔧 <b>組件:</b> {component}
❌ <b>錯誤:</b> {error_msg}
🕐 <b>時間:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
            """
            
            return await self.send_message(message)
            
        except Exception as e:
            self.logger.error(f"❌ 錯誤警報發送失敗: {e}")
            return False

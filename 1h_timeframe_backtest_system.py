"""
1小時時框策略回測系統
自動獲取多幣種1小時數據並進行CCB+Taker Intensity策略回測
包含BTC、WIF、BNB、ETH、SOL、1000PEPE、XRP、TRB

作者: 專業量化策略工程師
日期: 2024
"""

import requests
import pandas as pd
import numpy as np
import time
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.org"

# 1小時時框目標幣種
TARGET_COINS_1H = {
    'BTC': 'BTCUSDT',
    'WIF': 'WIFUSDT',
    'BNB': 'BNBUSDT', 
    'ETH': 'ETHUSDT',
    'SOL': 'SOLUSDT',
    'PEPE': '1000PEPEUSDT',
    'XRP': 'XRPUSDT',
    'TRB': 'TRBUSDT'
}

def create_1h_folder():
    """創建1小時策略回測文件夾"""
    folder_name = "1H_Strategy_Backtest"
    
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"✅ 創建文件夾: {folder_name}")
    else:
        print(f"📁 文件夾已存在: {folder_name}")
    
    return folder_name

def get_bybit_1h_data(symbol, limit=2000):
    """獲取Bybit 1小時K線數據"""
    print(f"正在獲取Bybit {symbol} 1小時數據...")
    
    try:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": symbol,
            "interval": "60",  # 1小時 = 60分鐘
            "limit": limit
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("retCode") == 0:
                kline_data = data.get("result", {}).get("list", [])
                
                if kline_data:
                    # 轉換為DataFrame
                    df = pd.DataFrame(kline_data)
                    df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                    
                    # 轉換數據類型
                    for col in ["open", "high", "low", "close", "volume"]:
                        df[col] = pd.to_numeric(df[col])
                    
                    df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                    df = df.sort_values("timestamp").reset_index(drop=True)
                    
                    print(f"   ✅ {symbol} 1小時數據獲取成功: {len(df)}條記錄")
                    print(f"   時間範圍: {df['timestamp'].min()} 至 {df['timestamp'].max()}")
                    
                    return df
                else:
                    print(f"   ❌ {symbol} 無K線數據")
                    return None
            else:
                print(f"   ❌ {symbol} Bybit API錯誤: {data}")
                return None
        else:
            print(f"   ❌ {symbol} Bybit請求失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ {symbol} 1小時數據獲取失敗: {e}")
        return None

def get_blave_1h_data(symbol, data_type="holder_concentration"):
    """獲取Blave 1小時數據"""
    print(f"正在獲取Blave {symbol} 1小時{data_type}數據...")
    
    headers = {
        "api-key": BLAVE_API_KEY,
        "secret-key": BLAVE_SECRET_KEY,
    }
    
    try:
        if data_type == "holder_concentration":
            url = f"{BLAVE_BASE_URL}/holder_concentration/get_alpha"
        elif data_type == "taker_intensity":
            url = f"{BLAVE_BASE_URL}/taker_intensity/get_alpha"
        else:
            print(f"   ❌ 未知數據類型: {data_type}")
            return None
        
        params = {
            "symbol": symbol,
            "period": "1h",  # 1小時時框
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                timestamps = data["data"]["timestamp"]
                alpha_values = data["data"]["alpha"]
                
                if timestamps and len(timestamps) > 20:
                    df = pd.DataFrame({
                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                        data_type: alpha_values
                    })
                    
                    print(f"   ✅ {symbol} {data_type} 1小時數據獲取成功: {len(df)}條記錄")
                    return df
                else:
                    print(f"   ❌ {symbol} {data_type} 數據不足")
                    return None
            else:
                print(f"   ❌ {symbol} {data_type} API回應格式錯誤")
                return None
        else:
            print(f"   ❌ {symbol} {data_type} 請求失敗: {response.status_code}")
            if response.status_code == 404:
                print(f"      可能該幣種不支持{data_type}數據")
            return None
            
    except Exception as e:
        print(f"   ❌ {symbol} {data_type} 數據獲取失敗: {e}")
        return None

def combine_1h_data(symbol):
    """整合單個幣種的1小時數據"""
    print(f"\n{'='*50}")
    print(f"整合 {symbol} 1小時數據")
    print(f"{'='*50}")
    
    # 1. 獲取價格數據
    price_df = get_bybit_1h_data(symbol)
    if price_df is None:
        return None
    
    # 2. 獲取籌碼集中度數據
    time.sleep(2)  # API限制
    concentration_df = get_blave_1h_data(symbol, "holder_concentration")
    if concentration_df is None:
        return None
    
    # 3. 獲取Taker Intensity數據
    time.sleep(2)  # API限制
    intensity_df = get_blave_1h_data(symbol, "taker_intensity")
    if intensity_df is None:
        return None
    
    # 4. 合併數據
    print("正在合併1小時數據...")
    
    try:
        # 處理Taker Intensity數據
        long_intensity = []
        short_intensity = []
        
        for ti_val in intensity_df['taker_intensity']:
            if ti_val > 0:
                long_intensity.append(ti_val)
                short_intensity.append(0)
            else:
                long_intensity.append(0)
                short_intensity.append(abs(ti_val))
        
        intensity_df['long_intensity'] = long_intensity
        intensity_df['short_intensity'] = short_intensity
        
        # 轉換時間戳為小時進行合併
        price_df['hour'] = price_df['timestamp'].dt.floor('1H')
        concentration_df['hour'] = concentration_df['timestamp'].dt.floor('1H')
        intensity_df['hour'] = intensity_df['timestamp'].dt.floor('1H')
        
        # 以價格數據為基準進行合併
        combined_df = price_df.copy()
        
        # 合併籌碼集中度
        combined_df = pd.merge(
            combined_df, 
            concentration_df[['hour', 'holder_concentration']], 
            on='hour', 
            how='left'
        )
        
        # 合併多空力道
        combined_df = pd.merge(
            combined_df, 
            intensity_df[['hour', 'taker_intensity', 'long_intensity', 'short_intensity']], 
            on='hour', 
            how='left'
        )
        
        # 清理數據
        combined_df = combined_df.dropna(subset=['holder_concentration', 'taker_intensity'])
        
        if len(combined_df) > 100:  # 至少100個1小時週期
            print(f"✅ {symbol} 1小時數據合併成功: {len(combined_df)}條記錄")
            
            # 重命名列
            combined_df = combined_df.rename(columns={
                'close': 'Close',
                'holder_concentration': 'concentration',
                'long_intensity': 'long_taker_intensity',
                'short_intensity': 'short_taker_intensity'
            })
            
            # 設置時間索引
            combined_df.set_index('timestamp', inplace=True)
            
            return combined_df
        else:
            print(f"❌ {symbol} 合併後數據不足: {len(combined_df)}條記錄")
            return None
            
    except Exception as e:
        print(f"❌ {symbol} 數據合併失敗: {e}")
        return None

def chip_concentration_bands_1h(df, window=15, std_dev=2):
    """計算1小時籌碼集中帶"""
    df['CCB_Middle'] = df['concentration'].rolling(window=window).mean()
    df['CCB_Upper'] = df['CCB_Middle'] + (df['concentration'].rolling(window=window).std() * std_dev)
    df['CCB_Lower'] = df['CCB_Middle'] - (df['concentration'].rolling(window=window).std() * std_dev)
    return df

def taker_intensity_signals_1h(df, lookback_period=24, percentile_threshold=65):
    """計算1小時多空力道信號"""
    df['Long_Signal'] = df['long_taker_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    df['Short_Signal'] = df['short_taker_intensity'].rolling(window=lookback_period).apply(
        lambda x: x.iloc[-1] >= np.percentile(x, percentile_threshold) if len(x) == lookback_period else False
    )
    
    return df

def ccb_taker_entry_logic_1h(df):
    """1小時入場邏輯"""
    df['Signal'] = 0
    
    # 多頭信號
    long_condition = (df['concentration'] < df['CCB_Lower']) & df['Long_Signal']
    df.loc[long_condition, 'Signal'] = 1
    
    # 空頭信號
    short_condition = (df['concentration'] > df['CCB_Upper']) & df['Short_Signal']
    df.loc[short_condition, 'Signal'] = -1
    
    # 信號延續
    df['Signal'] = df['Signal'].replace(0, np.nan).ffill().fillna(0)
    
    return df

def run_1h_strategy_backtest(coin_name, symbol, df, folder_name):
    """運行1小時策略回測"""
    print(f"\n運行 {coin_name} 1小時策略回測...")
    
    try:
        # 使用調整後的1小時參數
        params_1h = {
            'ccb_window': 12,  # 1小時時框用更小窗口
            'ccb_std': 2.0,
            'taker_lookback': 24,  # 24小時回望
            'taker_threshold': 65
        }
        
        # 應用策略
        df = chip_concentration_bands_1h(df, window=params_1h['ccb_window'], std_dev=params_1h['ccb_std'])
        df = taker_intensity_signals_1h(df, lookback_period=params_1h['taker_lookback'], percentile_threshold=params_1h['taker_threshold'])
        df = ccb_taker_entry_logic_1h(df)
        
        # 計算收益
        df['price_chg'] = df['Close'].pct_change()
        df['pnl'] = df['Signal'].shift(1) * df['price_chg']
        
        # 計算績效
        pnl = df['pnl'].dropna()
        
        if len(pnl) > 50:
            # 基本統計
            total_return = (1 + pnl).prod() - 1
            annual_return = (1 + total_return) ** (365*24 / len(pnl)) - 1  # 1小時調整
            volatility = pnl.std() * np.sqrt(365*24)  # 1小時調整
            sharpe_ratio = pnl.mean() / pnl.std() * np.sqrt(365*24) if pnl.std() > 0 else 0
            
            # 最大回撤
            cumulative = (1 + pnl).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 其他指標
            profit_factor = pnl[pnl > 0].sum() / abs(pnl[pnl < 0].sum()) if (pnl < 0).any() else np.inf
            win_rate = (pnl > 0).mean()
            total_trades = len(pnl[pnl != 0])
            
            performance = {
                'coin': coin_name,
                'timeframe': '1H',
                'total_return': total_return,
                'annual_return': annual_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'profit_factor': profit_factor,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'data_points': len(df)
            }
            
            print(f"=== {coin_name} 1小時策略績效 ===")
            print(f"總收益: {total_return:.2%}")
            print(f"年化收益: {annual_return:.2%}")
            print(f"夏普比率: {sharpe_ratio:.4f}")
            print(f"最大回撤: {max_drawdown:.2%}")
            print(f"勝率: {win_rate:.2%}")
            print(f"交易次數: {total_trades}")
            print(f"數據點數: {len(df)}")
            
            # 保存結果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{folder_name}/{coin_name}_1H_CCB_TakerIntensity_Backtest_{timestamp}.csv"
            df.to_csv(filename)
            print(f"✅ {coin_name} 1小時回測結果已保存: {filename}")
            
            return performance, df
        
        else:
            print(f"❌ {coin_name} 1小時數據不足進行回測")
            return None
            
    except Exception as e:
        print(f"❌ {coin_name} 1小時回測失敗: {e}")
        return None

def run_all_1h_backtests():
    """運行所有幣種的1小時回測"""
    
    print("="*80)
    print("1小時時框多幣種策略回測系統")
    print("幣種: BTC, WIF, BNB, ETH, SOL, 1000PEPE, XRP, TRB")
    print("="*80)
    
    # 創建文件夾
    folder_name = create_1h_folder()
    
    all_results = []
    successful_coins = []
    
    for coin_name, symbol in TARGET_COINS_1H.items():
        print(f"\n{'='*60}")
        print(f"開始處理 {coin_name} ({symbol})")
        print(f"{'='*60}")
        
        # 獲取並合併數據
        combined_df = combine_1h_data(symbol)
        
        if combined_df is not None:
            # 運行回測
            result = run_1h_strategy_backtest(coin_name, symbol, combined_df, folder_name)
            
            if result is not None:
                performance, df = result
                all_results.append(performance)
                successful_coins.append(coin_name)
                
                # 保存原始數據
                data_filename = f"{folder_name}/{coin_name}_1H_RealData_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                combined_df.to_csv(data_filename)
                print(f"✅ {coin_name} 1小時原始數據已保存: {data_filename}")
        
        # API限制延遲
        time.sleep(3)
    
    # 生成綜合報告
    if all_results:
        generate_1h_comprehensive_report(all_results, folder_name)
    
    return all_results, folder_name

def generate_1h_comprehensive_report(results, folder_name):
    """生成1小時綜合報告"""
    
    print(f"\n{'='*80}")
    print("1小時時框策略綜合報告")
    print(f"{'='*80}")
    
    # 創建結果DataFrame
    df_results = pd.DataFrame(results)
    df_results = df_results.sort_values('sharpe_ratio', ascending=False)
    
    print(f"\n📊 1小時策略表現排名:")
    print("-" * 70)
    
    for i, row in df_results.iterrows():
        rank = df_results.index.get_loc(i) + 1
        coin = row['coin']
        sharpe = row['sharpe_ratio']
        total_ret = row['total_return']
        max_dd = row['max_drawdown']
        trades = row['total_trades']
        
        status = "🎉" if sharpe >= 1.5 else "⚠️"
        
        print(f"{rank}. {status} {coin}: 夏普={sharpe:.4f}, 收益={total_ret:.2%}, 回撤={max_dd:.2%}, 交易={trades}次")
    
    # 統計分析
    print(f"\n📈 1小時時框統計分析:")
    print(f"   測試幣種數: {len(results)}")
    print(f"   達標幣種數 (夏普≥1.5): {len(df_results[df_results['sharpe_ratio'] >= 1.5])}")
    print(f"   平均夏普比率: {df_results['sharpe_ratio'].mean():.4f}")
    print(f"   平均總收益: {df_results['total_return'].mean():.2%}")
    print(f"   平均最大回撤: {df_results['max_drawdown'].mean():.2%}")
    print(f"   平均交易次數: {df_results['total_trades'].mean():.0f}")
    
    # 保存綜合報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"{folder_name}/1H_Multi_Coin_Strategy_Report_{timestamp}.csv"
    df_results.to_csv(report_filename, index=False)
    print(f"\n✅ 1小時綜合報告已保存: {report_filename}")
    
    return df_results

if __name__ == "__main__":
    # 執行1小時多幣種回測
    print("開始執行1小時時框多幣種策略回測...")
    
    results, folder_name = run_all_1h_backtests()
    
    if results:
        print(f"\n🎯 1小時時框回測完成！")
        print(f"成功測試了 {len(results)} 個幣種")
        print(f"所有結果保存在文件夾: {folder_name}")
    else:
        print(f"\n❌ 1小時時框回測失敗")

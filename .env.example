# 聖杯級交易信號系統環境變量配置示例
# 複製此文件為 .env 並填入實際值

# ═══════════════════════════════════════════════════════════════
# 🔑 Blave API 配置 (已配置，無需修改)
# ═══════════════════════════════════════════════════════════════
BLAVE_API_KEY=acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6
BLAVE_SECRET_KEY=5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d

# ═══════════════════════════════════════════════════════════════
# 📱 Telegram Bot 配置 (必須配置)
# ═══════════════════════════════════════════════════════════════
# 1. 在Telegram中搜索 @BotFather
# 2. 發送 /newbot 創建新bot
# 3. 獲取bot token並填入下方
# 4. 將bot添加到您的聊天中並獲取chat_id
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# ═══════════════════════════════════════════════════════════════
# 🔧 系統配置 (可選)
# ═══════════════════════════════════════════════════════════════
DEBUG_MODE=false
LOG_LEVEL=INFO
PYTHONPATH=/app
PYTHONUNBUFFERED=1

# ═══════════════════════════════════════════════════════════════
# 🌐 網絡配置 (可選)
# ═══════════════════════════════════════════════════════════════
SSL_VERIFY=true
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# ═══════════════════════════════════════════════════════════════
# 💾 數據庫配置 (可選)
# ═══════════════════════════════════════════════════════════════
DATABASE_URL=sqlite:///data/trading.db

# ═══════════════════════════════════════════════════════════════
# 📊 Bybit API 配置 (可選 - 用於價格數據驗證)
# ═══════════════════════════════════════════════════════════════
BYBIT_API_KEY=
BYBIT_SECRET_KEY=

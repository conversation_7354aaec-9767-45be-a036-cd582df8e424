#!/usr/bin/env python3
"""
部署多空策略信號系統到GitHub
替代CCB策略框架
"""

import os
import json
import shutil
import subprocess
from datetime import datetime

def create_deployment_structure():
    """創建部署目錄結構"""
    print("📁 創建部署目錄結構...")
    
    # 創建主要目錄
    dirs = [
        'signal_system',
        'signal_system/src',
        'signal_system/config',
        'signal_system/logs',
        'signal_system/data'
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"   ✅ {dir_path}")

def copy_essential_files():
    """複製必要文件"""
    print("\n📋 複製必要文件...")
    
    # 文件映射
    file_mappings = {
        'main_signal_system.py': 'signal_system/main.py',
        'src/config_manager.py': 'signal_system/src/config_manager.py',
        'src/data_fetcher.py': 'signal_system/src/data_fetcher.py',
        'rsi_signal_config.json': 'signal_system/config/strategies.json',
        'requirements.txt': 'signal_system/requirements.txt'
    }
    
    for src, dst in file_mappings.items():
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"   ✅ {src} -> {dst}")
        else:
            print(f"   ⚠️ 未找到: {src}")

def create_docker_files():
    """創建Docker配置文件"""
    print("\n🐳 創建Docker配置...")
    
    # Dockerfile
    dockerfile_content = """FROM python:3.9-slim

WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# 複製requirements並安裝Python依賴
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .

# 創建日誌目錄
RUN mkdir -p logs data

# 設置環境變量
ENV PYTHONPATH=/app
ENV TZ=Asia/Taipei

# 運行應用
CMD ["python", "main.py"]
"""
    
    with open('signal_system/Dockerfile', 'w', encoding='utf-8') as f:
        f.write(dockerfile_content)
    print("   ✅ Dockerfile")
    
    # docker-compose.yml
    compose_content = """version: '3.8'

services:
  signal-system:
    build: .
    container_name: multi-strategy-signals
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - BLAVE_API_KEY=${BLAVE_API_KEY}
      - BLAVE_SECRET_KEY=${BLAVE_SECRET_KEY}
      - BYBIT_API_KEY=${BYBIT_API_KEY}
      - BYBIT_SECRET_KEY=${BYBIT_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    networks:
      - signal-network

networks:
  signal-network:
    driver: bridge
"""
    
    with open('signal_system/docker-compose.yml', 'w', encoding='utf-8') as f:
        f.write(compose_content)
    print("   ✅ docker-compose.yml")

def create_env_template():
    """創建環境變量模板"""
    print("\n🔧 創建環境配置...")
    
    env_content = """# Telegram配置
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Blave API配置
BLAVE_API_KEY=acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6
BLAVE_SECRET_KEY=5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d

# Bybit API配置
BYBIT_API_KEY=your_bybit_api_key
BYBIT_SECRET_KEY=your_bybit_secret_key
"""
    
    with open('signal_system/.env.template', 'w', encoding='utf-8') as f:
        f.write(env_content)
    print("   ✅ .env.template")

def create_readme():
    """創建README文件"""
    print("\n📖 創建README...")
    
    readme_content = """# 多空策略信號系統

## 概述
基於RSI濾網和Taker Intensity的多幣種自動信號系統，替代CCB策略框架。

## 特性
- ✅ 19個經過驗證的策略組合
- ✅ 勝率≥70%，總收益≥5%，日均信號≥0.5個
- ✅ 實時Telegram通知
- ✅ 自動止盈止損計算
- ✅ 24/7監控運行

## 快速部署

### 1. 環境配置
```bash
cp .env.template .env
# 編輯.env文件，填入您的API密鑰
```

### 2. Docker部署
```bash
docker-compose up -d
```

### 3. 查看日誌
```bash
docker-compose logs -f
```

## 監控策略
當前活躍策略包括：
- DOTUSDT 4H (評分82.2)
- SEIUSDT 1H (評分80.3)
- BOMEUSDT 1H (評分78.1)
- 等19個高質量策略...

## 信號格式
```
🟢 多空策略信號

💰 幣種: DOTUSDT
⏰ 時框: 4H
📈 方向: LONG
💵 入場價: $5.234567

🎯 止盈價: $5.456789
🛑 止損價: $5.012345
```

## 技術指標
- RSI濾網：多頭≥70，空頭≤30
- Taker Intensity：70%信賴區間
- 布林帶突破：動態參數優化
- ATR止損：風險回報比2.5-3.0

## 維護
系統每8天自動重新優化參數，確保策略持續有效。

---
© 2025 多空策略信號系統
"""
    
    with open('signal_system/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("   ✅ README.md")

def create_github_actions():
    """創建GitHub Actions工作流"""
    print("\n⚙️ 創建GitHub Actions...")
    
    os.makedirs('signal_system/.github/workflows', exist_ok=True)
    
    workflow_content = """name: Deploy Signal System

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Test imports
      run: |
        python -c "import src.config_manager; import src.data_fetcher; print('✅ All imports successful')"

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -t signal-system .
    
    - name: Test Docker container
      run: |
        docker run --rm signal-system python -c "print('✅ Docker container works')"
"""
    
    with open('signal_system/.github/workflows/deploy.yml', 'w', encoding='utf-8') as f:
        f.write(workflow_content)
    print("   ✅ GitHub Actions workflow")

def update_gitignore():
    """更新.gitignore"""
    print("\n🚫 更新.gitignore...")
    
    gitignore_content = """# 環境文件
.env
*.env

# 日誌文件
logs/
*.log

# 數據文件
data/
*.csv
*.json.bak

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/
.idea/
*.swp
*.swo

# Docker
.dockerignore
"""
    
    with open('signal_system/.gitignore', 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    print("   ✅ .gitignore")

def main():
    """主部署函數"""
    print("🚀 開始部署多空策略信號系統")
    print("=" * 50)
    
    # 執行部署步驟
    create_deployment_structure()
    copy_essential_files()
    create_docker_files()
    create_env_template()
    create_readme()
    create_github_actions()
    update_gitignore()
    
    print("\n" + "=" * 50)
    print("✅ 部署準備完成！")
    print("\n📋 下一步操作：")
    print("1. cd signal_system")
    print("2. 編輯 .env.template 並重命名為 .env")
    print("3. git init && git add . && git commit -m 'Initial commit'")
    print("4. git remote add origin <your-repo-url>")
    print("5. git push -u origin main")
    print("6. docker-compose up -d")
    print("\n🎉 系統將自動開始監控並發送信號！")

if __name__ == "__main__":
    main()

"""
數據獲取模組
從Blave API和Bybit API獲取實時數據

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .logger_setup import setup_logger

class DataFetcher:
    """數據獲取器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("DataFetcher")
        
        # API配置
        self.blave_base_url = config.get('data.blave_base_url')
        self.bybit_base_url = config.get('data.bybit_base_url')
        
        # API密鑰
        self.blave_headers = {
            "api-key": config.get_api_key('blave', 'api_key'),
            "secret-key": config.get_api_key('blave', 'secret_key')
        }
        
        # 會話
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_latest_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取最新的完整數據"""
        try:
            self.logger.info(f"📊 獲取 {symbol} {timeframe} 最新數據")
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # 並行獲取所有數據
            tasks = [
                self.get_bybit_data(symbol, timeframe),
                self.get_blave_concentration_data(symbol, timeframe),
                self.get_blave_taker_intensity_data(symbol, timeframe)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            price_data = results[0] if not isinstance(results[0], Exception) else None
            concentration_data = results[1] if not isinstance(results[1], Exception) else None
            intensity_data = results[2] if not isinstance(results[2], Exception) else None

            # 嚴格要求所有真實數據都必須獲取成功
            if not all([price_data is not None, concentration_data is not None, intensity_data is not None]):
                self.logger.error(f"❌ {symbol} 數據獲取失敗 - 拒絕使用模擬數據")
                return None
            
            # 合併數據
            combined_data = self.merge_data(price_data, concentration_data, intensity_data)
            
            if combined_data is not None and len(combined_data) > 0:
                self.logger.info(f"✅ {symbol} 數據獲取成功: {len(combined_data)}條記錄")
                return combined_data
            else:
                self.logger.warning(f"⚠️ {symbol} 數據合併失敗")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 數據獲取失敗: {e}")
            return None


    
    async def get_bybit_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Bybit價格數據"""
        try:
            # 時框映射
            interval_map = {
                '1H': '60',
                '4H': '240',
                'Daily': 'D'
            }
            
            interval = interval_map.get(timeframe, '60')
            
            url = f"{self.bybit_base_url}/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": 200  # 獲取足夠的歷史數據
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        
                        if kline_data:
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]

                            # 轉換數據類型
                            for col in ["open", "high", "low", "close", "volume"]:
                                df[col] = pd.to_numeric(df[col])

                            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                            df = df.sort_values("timestamp").reset_index(drop=True)
                            df.set_index("timestamp", inplace=True)

                            # 重命名列以匹配系統期望的格式
                            df = df.rename(columns={
                                'open': 'Open',
                                'high': 'High',
                                'low': 'Low',
                                'close': 'Close',
                                'volume': 'Volume',
                                'turnover': 'Turnover'
                            })
                            
                            return df
                
                self.logger.warning(f"⚠️ Bybit API響應異常: {response.status}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Bybit數據獲取失敗: {e}")
            return None
    
    async def get_blave_concentration_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Blave籌碼集中度數據"""
        try:
            # 時框映射
            period_map = {
                '1H': '1h',
                '4H': '4h',
                'Daily': '1d'
            }
            
            period = period_map.get(timeframe, '1h')
            
            url = f"{self.blave_base_url}/holder_concentration/get_alpha"
            params = {
                "symbol": symbol,
                "period": period
            }
            
            async with self.session.get(url, headers=self.blave_headers, params=params, timeout=60) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Blave API響應格式：數據在data字段中
                    if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                        timestamps = data["data"]["timestamp"]
                        alpha_values = data["data"]["alpha"]
                        
                        if timestamps and alpha_values:
                            df = pd.DataFrame({
                                'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                'concentration': alpha_values
                            })
                            df.set_index('timestamp', inplace=True)
                            return df
                
                self.logger.warning(f"⚠️ Blave籌碼集中度API響應異常: {response.status}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Blave籌碼集中度數據獲取失敗: {e}")
            return None
    
    async def get_blave_taker_intensity_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Blave多空力道數據"""
        try:
            period_map = {
                '1H': '1h',
                '4H': '4h',
                'Daily': '1d'
            }
            
            period = period_map.get(timeframe, '1h')
            
            url = f"{self.blave_base_url}/taker_intensity/get_alpha"
            params = {
                "symbol": symbol,
                "period": period
            }
            
            async with self.session.get(url, headers=self.blave_headers, params=params, timeout=60) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Blave API響應格式：數據在data字段中
                    if "data" in data and "timestamp" in data["data"] and "alpha" in data["data"]:
                        timestamps = data["data"]["timestamp"]
                        alpha_values = data["data"]["alpha"]
                        
                        if timestamps and alpha_values:
                            # 分離多空力道
                            long_intensity = []
                            short_intensity = []
                            
                            for val in alpha_values:
                                if val > 0:
                                    long_intensity.append(val)
                                    short_intensity.append(0)
                                else:
                                    long_intensity.append(0)
                                    short_intensity.append(abs(val))
                            
                            df = pd.DataFrame({
                                'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                'taker_intensity': alpha_values,
                                'long_taker_intensity': long_intensity,
                                'short_taker_intensity': short_intensity
                            })
                            df.set_index('timestamp', inplace=True)
                            return df
                
                self.logger.warning(f"⚠️ Blave多空力道API響應異常: {response.status}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Blave多空力道數據獲取失敗: {e}")
            return None
    
    def merge_data(self, price_data: pd.DataFrame, concentration_data: pd.DataFrame, 
                   intensity_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """合併所有數據"""
        try:
            # 以價格數據為基準
            combined = price_data.copy()
            
            # 重命名價格列
            combined = combined.rename(columns={'close': 'Close'})
            
            # 合併籌碼集中度數據
            combined = pd.merge_asof(
                combined.sort_index(), 
                concentration_data.sort_index(),
                left_index=True, 
                right_index=True,
                direction='backward'
            )
            
            # 合併多空力道數據
            combined = pd.merge_asof(
                combined.sort_index(),
                intensity_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
            
            # 清理空值
            combined = combined.dropna(subset=['Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity'])
            
            return combined
            
        except Exception as e:
            self.logger.error(f"❌ 數據合併失敗: {e}")
            return None

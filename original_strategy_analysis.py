"""
原始策略操作方式分析
檢查原始高夏普比率策略的具體交易邏輯和資金管理方式

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import glob
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_original_strategy_logic():
    """分析原始策略的交易邏輯"""
    
    print("="*80)
    print("原始策略操作方式分析")
    print("檢查高夏普比率策略的具體交易邏輯")
    print("="*80)
    
    # 載入一個1小時策略文件進行分析
    h1_files = glob.glob("1H_Strategy_Backtest/*_1H_CCB_TakerIntensity_Backtest_*.csv")
    
    if not h1_files:
        print("❌ 找不到1小時策略文件")
        return None
    
    # 選擇PEPE-1H進行分析 (原始夏普比率7.99)
    pepe_file = None
    for file in h1_files:
        if 'PEPE' in file:
            pepe_file = file
            break
    
    if not pepe_file:
        print("❌ 找不到PEPE-1H文件")
        return None
    
    print(f"分析文件: {os.path.basename(pepe_file)}")
    
    # 載入數據
    df = pd.read_csv(pepe_file)
    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
    
    print(f"\n📊 數據基本信息:")
    print(f"   總記錄數: {len(df)}")
    print(f"   時間範圍: {df.index[0]} 至 {df.index[-1]}")
    print(f"   價格範圍: ${df['Close'].min():.6f} - ${df['Close'].max():.6f}")
    
    # 分析交易信號
    print(f"\n🔍 交易信號分析:")
    signal_counts = df['Signal'].value_counts().sort_index()
    print(f"   信號分佈: {signal_counts.to_dict()}")
    
    # 計算信號變化
    signal_changes = (df['Signal'] != df['Signal'].shift(1)).sum()
    print(f"   信號變化次數: {signal_changes}")
    print(f"   信號變化頻率: {signal_changes/len(df):.2%}")
    
    # 分析PnL計算方式
    print(f"\n💰 PnL計算方式分析:")
    
    # 檢查是否有price_chg列
    if 'price_chg' in df.columns:
        print("   發現price_chg列 - 使用價格變化率")
        price_changes = df['price_chg'].dropna()
        print(f"   價格變化率範圍: {price_changes.min():.6f} 至 {price_changes.max():.6f}")
        print(f"   平均價格變化率: {price_changes.mean():.6f}")
    
    # 檢查PnL計算
    if 'pnl' in df.columns:
        pnl_data = df['pnl'].dropna()
        print(f"   PnL數據點數: {len(pnl_data)}")
        print(f"   PnL範圍: {pnl_data.min():.6f} 至 {pnl_data.max():.6f}")
        print(f"   平均PnL: {pnl_data.mean():.6f}")
        
        # 檢查PnL計算邏輯
        print(f"\n🧮 PnL計算邏輯驗證:")
        
        # 重新計算price_chg
        df['calculated_price_chg'] = df['Close'].pct_change()
        
        # 重新計算PnL (假設全倉操作)
        df['calculated_pnl'] = df['Signal'].shift(1) * df['calculated_price_chg']
        
        # 比較原始PnL和重新計算的PnL
        comparison_df = df[['pnl', 'calculated_pnl']].dropna()
        
        if len(comparison_df) > 0:
            correlation = comparison_df['pnl'].corr(comparison_df['calculated_pnl'])
            max_diff = (comparison_df['pnl'] - comparison_df['calculated_pnl']).abs().max()
            
            print(f"   原始PnL vs 重新計算PnL:")
            print(f"   相關係數: {correlation:.6f}")
            print(f"   最大差異: {max_diff:.8f}")
            
            if correlation > 0.999 and max_diff < 0.001:
                print("   ✅ 確認：使用全倉操作，無槓桿，無手續費")
                operation_type = "全倉現貨操作"
            else:
                print("   ⚠️ PnL計算可能包含其他因素")
                operation_type = "未知操作方式"
        
        # 計算累積收益
        cumulative_return = (1 + pnl_data).prod() - 1
        
        # 計算夏普比率
        if len(pnl_data) > 1:
            sharpe_ratio = pnl_data.mean() / pnl_data.std() * np.sqrt(365 * 24)  # 1小時年化
            
            print(f"\n📈 原始策略績效:")
            print(f"   累積收益率: {cumulative_return:.2%}")
            print(f"   夏普比率: {sharpe_ratio:.4f}")
            print(f"   操作方式: {operation_type}")
        
        # 分析交易頻率對績效的影響
        print(f"\n⚡ 交易頻率影響分析:")
        
        # 計算實際交易次數
        actual_trades = len(pnl_data[pnl_data != 0])
        total_periods = len(df)
        trading_frequency = actual_trades / total_periods
        
        print(f"   實際交易次數: {actual_trades}")
        print(f"   總時間週期: {total_periods}")
        print(f"   交易頻率: {trading_frequency:.2%}")
        
        # 模擬加入交易成本的影響
        print(f"\n💸 交易成本影響模擬:")
        
        # 假設每筆交易0.2%手續費 (開倉+平倉)
        trading_cost = 0.002
        
        # 計算扣除交易成本後的PnL
        df['pnl_with_cost'] = df['calculated_pnl'].copy()
        
        # 在信號變化時扣除交易成本
        signal_change_mask = (df['Signal'] != df['Signal'].shift(1)) & (df['Signal'] != 0)
        
        for i in range(len(df)):
            if signal_change_mask.iloc[i]:
                df.iloc[i, df.columns.get_loc('pnl_with_cost')] -= trading_cost
        
        pnl_with_cost = df['pnl_with_cost'].dropna()
        
        if len(pnl_with_cost) > 1:
            cumulative_return_with_cost = (1 + pnl_with_cost).prod() - 1
            sharpe_with_cost = pnl_with_cost.mean() / pnl_with_cost.std() * np.sqrt(365 * 24)
            
            print(f"   扣除交易成本後:")
            print(f"   累積收益率: {cumulative_return_with_cost:.2%}")
            print(f"   夏普比率: {sharpe_with_cost:.4f}")
            print(f"   績效下降: {sharpe_ratio - sharpe_with_cost:.4f}")
        
        return {
            'operation_type': operation_type,
            'original_sharpe': sharpe_ratio,
            'original_return': cumulative_return,
            'trading_frequency': trading_frequency,
            'actual_trades': actual_trades,
            'sharpe_with_cost': sharpe_with_cost if 'sharpe_with_cost' in locals() else None,
            'return_with_cost': cumulative_return_with_cost if 'cumulative_return_with_cost' in locals() else None
        }

def compare_operation_methods():
    """比較不同操作方式的績效"""
    
    print(f"\n{'='*80}")
    print("操作方式比較分析")
    print(f"{'='*80}")
    
    # 分析原始策略
    original_analysis = analyze_original_strategy_logic()
    
    if original_analysis is None:
        return None
    
    print(f"\n📊 操作方式對比總結:")
    print(f"{'='*60}")
    
    print(f"\n1️⃣ 原始策略 (全倉現貨操作):")
    print(f"   夏普比率: {original_analysis['original_sharpe']:.4f}")
    print(f"   累積收益: {original_analysis['original_return']:.2%}")
    print(f"   交易頻率: {original_analysis['trading_frequency']:.2%}")
    print(f"   特點: 無手續費、無滑點、無槓桿、全倉操作")
    
    if original_analysis['sharpe_with_cost'] is not None:
        print(f"\n2️⃣ 加入交易成本後:")
        print(f"   夏普比率: {original_analysis['sharpe_with_cost']:.4f}")
        print(f"   累積收益: {original_analysis['return_with_cost']:.2%}")
        print(f"   績效下降: {original_analysis['original_sharpe'] - original_analysis['sharpe_with_cost']:.4f}")
    
    print(f"\n3️⃣ 100U實盤模擬 (1%倉位+10倍槓桿):")
    print(f"   最終收益: +5.79% (PEPE-1H)")
    print(f"   特點: 有手續費、有滑點、有槓桿、分倉操作")
    
    print(f"\n🔍 關鍵發現:")
    print(f"   1. 原始高夏普比率來自【全倉現貨操作】")
    print(f"   2. 無任何交易成本和實盤限制")
    print(f"   3. 每次信號變化都是100%資金進出")
    print(f"   4. 實盤模擬的限制大幅降低了表現")
    
    return original_analysis

def analyze_signal_timing():
    """分析交易信號的時機和止盈止損邏輯"""
    
    print(f"\n{'='*80}")
    print("交易信號時機分析")
    print(f"{'='*80}")
    
    # 載入一個策略文件
    h1_files = glob.glob("1H_Strategy_Backtest/*_1H_CCB_TakerIntensity_Backtest_*.csv")
    
    if h1_files:
        df = pd.read_csv(h1_files[0])
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        print(f"\n🎯 交易信號邏輯分析:")
        
        # 檢查信號延續邏輯
        signal_changes = df['Signal'] != df['Signal'].shift(1)
        signal_periods = []
        
        current_signal = None
        period_start = None
        
        for i, (timestamp, row) in enumerate(df.iterrows()):
            if signal_changes.iloc[i]:
                if current_signal is not None and period_start is not None:
                    period_length = i - period_start
                    signal_periods.append({
                        'signal': current_signal,
                        'length': period_length,
                        'start': period_start,
                        'end': i-1
                    })
                
                current_signal = row['Signal']
                period_start = i
        
        # 添加最後一個週期
        if current_signal is not None and period_start is not None:
            period_length = len(df) - period_start
            signal_periods.append({
                'signal': current_signal,
                'length': period_length,
                'start': period_start,
                'end': len(df)-1
            })
        
        if signal_periods:
            avg_period_length = np.mean([p['length'] for p in signal_periods])
            print(f"   平均持倉週期: {avg_period_length:.1f} 小時")
            
            # 分析不同信號的持倉時間
            long_periods = [p['length'] for p in signal_periods if p['signal'] == 1]
            short_periods = [p['length'] for p in signal_periods if p['signal'] == -1]
            neutral_periods = [p['length'] for p in signal_periods if p['signal'] == 0]
            
            if long_periods:
                print(f"   多頭平均持倉: {np.mean(long_periods):.1f} 小時")
            if short_periods:
                print(f"   空頭平均持倉: {np.mean(short_periods):.1f} 小時")
            if neutral_periods:
                print(f"   空倉平均時間: {np.mean(neutral_periods):.1f} 小時")
        
        print(f"\n⏰ 止盈止損邏輯:")
        print(f"   ❌ 無明確止盈止損設置")
        print(f"   📊 基於指標信號變化進行平倉")
        print(f"   🔄 信號延續邏輯：信號保持直到新信號出現")
        print(f"   💡 建議：需要添加風險管理機制")

if __name__ == "__main__":
    # 執行完整分析
    print("開始分析原始策略操作方式...")
    
    # 比較操作方式
    comparison_results = compare_operation_methods()
    
    # 分析信號時機
    analyze_signal_timing()
    
    print(f"\n🎯 分析完成！")
    print(f"原始高夏普比率確實來自全倉現貨操作，無任何實盤限制。")

#!/usr/bin/env python3
"""
聖杯級綜合策略：Taker Intensity + 均線多時框架優化系統
基於<PERSON>理念：追求最佳回報而非最大回報
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class ComprehensiveTIMAStrategy:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 測試幣種
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT', 'BNBUSDT', 'WIFUSDT']
        
        # 時間框架配置
        self.timeframes = ['1H', '4H', 'Daily']
        
        # 參數優化範圍
        self.parameter_ranges = {
            'ti_thresholds': [0.1, 0.2, 0.3, 0.4, 0.5],
            'ma_short_periods': [5, 8, 10, 12, 15],
            'ma_long_periods': [15, 20, 25, 30, 35]
        }
        
        # 結果存儲
        self.results = []
        
    async def test_single_strategy(self, symbol: str, timeframe: str, ti_threshold: float, 
                                 ma_short: int, ma_long: int) -> dict:
        """測試單一策略組合"""
        try:
            # 獲取數據
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                return None
                
            # 檢查必要的列
            required_columns = ['Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            if not all(col in data.columns for col in required_columns):
                return None
            
            # 計算Taker Intensity淨值
            data['taker_intensity'] = data['long_taker_intensity'] - data['short_taker_intensity']
            
            # 計算均線
            data[f'MA_{ma_short}'] = data['Close'].rolling(window=ma_short).mean()
            data[f'MA_{ma_long}'] = data['Close'].rolling(window=ma_long).mean()
            
            # 生成信號
            signals = self.generate_enhanced_signals(data, ti_threshold, ma_short, ma_long)
            
            if len(signals) < 2:
                return None
                
            # 計算表現
            performance = self.calculate_performance(signals)
            
            # 聖杯級評分 - 基於Mark Minervini原則
            holy_grail_score = self.calculate_holy_grail_score(performance)
            
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'ti_threshold': ti_threshold,
                'ma_short': ma_short,
                'ma_long': ma_long,
                'data_points': len(data),
                'signals': len(signals),
                'holy_grail_score': holy_grail_score,
                **performance
            }
            
            return result
            
        except Exception as e:
            print(f"❌ {symbol} {timeframe} 策略測試失敗: {e}")
            return None
    
    def generate_enhanced_signals(self, data: pd.DataFrame, ti_threshold: float, 
                                ma_short: int, ma_long: int) -> list:
        """生成增強版信號 - 基於聖杯理念"""
        signals = []
        
        if len(data) < max(ma_short, ma_long) + 10:
            return signals
        
        for i in range(max(ma_short, ma_long), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            
            ma_short_val = data[f'MA_{ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{ma_short}'].iloc[i-1]
            
            # 跳過NaN值
            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti):
                continue
            
            # 增強版多頭信號條件
            long_conditions = [
                price > ma_short_val,  # 價格在短期均線上方
                prev_price <= prev_ma_short,  # 突破短期均線
                ti > ti_threshold,  # 積極做多
                ma_short_val > ma_long_val,  # 短期均線在長期均線上方（趨勢確認）
                concentration > 0  # 籌碼集中度為正（機構參與）
            ]
            
            # 增強版空頭信號條件
            short_conditions = [
                price < ma_short_val,  # 價格在短期均線下方
                prev_price >= prev_ma_short,  # 跌破短期均線
                ti < -ti_threshold,  # 積極做空
                ma_short_val < ma_long_val,  # 短期均線在長期均線下方
                concentration < 0  # 籌碼集中度為負
            ]
            
            # 多頭信號
            if sum(long_conditions) >= 4:  # 至少滿足4個條件
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'ti': ti,
                    'concentration': concentration,
                    'ma_short': ma_short_val,
                    'ma_long': ma_long_val,
                    'conditions_met': sum(long_conditions)
                })
            
            # 空頭信號
            elif sum(short_conditions) >= 4:  # 至少滿足4個條件
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'ti': ti,
                    'concentration': concentration,
                    'ma_short': ma_short_val,
                    'ma_long': ma_long_val,
                    'conditions_met': sum(short_conditions)
                })
        
        return signals
    
    def calculate_performance(self, signals: list) -> dict:
        """計算策略表現"""
        if len(signals) < 2:
            return self.get_empty_performance()
        
        trades = []
        
        # 配對交易
        for i in range(0, len(signals)-1, 2):
            entry = signals[i]
            exit_signal = signals[i+1] if i+1 < len(signals) else None
            
            if exit_signal:
                # 計算持倉時間
                holding_hours = (exit_signal['timestamp'] - entry['timestamp']).total_seconds() / 3600
                
                # 計算盈虧
                if entry['type'] == 'LONG':
                    pnl_pct = (exit_signal['price'] / entry['price'] - 1) * 100
                else:
                    pnl_pct = (entry['price'] / exit_signal['price'] - 1) * 100
                
                trades.append({
                    'pnl_pct': pnl_pct,
                    'holding_hours': holding_hours,
                    'entry_conditions': entry['conditions_met'],
                    'exit_conditions': exit_signal['conditions_met']
                })
        
        if not trades:
            return self.get_empty_performance()
        
        # 計算關鍵指標
        pnl_list = [t['pnl_pct'] for t in trades]
        total_return = sum(pnl_list)
        
        win_trades = [t for t in pnl_list if t > 0]
        loss_trades = [t for t in pnl_list if t < 0]
        
        win_rate = len(win_trades) / len(trades) * 100
        avg_win = np.mean(win_trades) if win_trades else 0
        avg_loss = np.mean(loss_trades) if loss_trades else 0
        
        # Mark Minervini關注的風險回報比
        risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # 盈虧比
        gross_profit = sum(win_trades) if win_trades else 0
        gross_loss = abs(sum(loss_trades)) if loss_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0
        
        # 最大回撤
        cumulative = np.cumsum(pnl_list)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = cumulative - running_max
        max_drawdown = np.min(drawdown)
        
        # 平均持倉時間
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])
        
        # Sharpe比率（簡化版）
        returns_std = np.std(pnl_list)
        sharpe_ratio = np.mean(pnl_list) / returns_std if returns_std != 0 else 0
        
        return {
            'total_trades': len(trades),
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'risk_reward_ratio': risk_reward_ratio,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'avg_holding_hours': avg_holding_hours
        }
    
    def get_empty_performance(self) -> dict:
        """返回空的表現指標"""
        return {
            'total_trades': 0,
            'total_return': 0,
            'win_rate': 0,
            'avg_win': 0,
            'avg_loss': 0,
            'risk_reward_ratio': 0,
            'profit_factor': 0,
            'max_drawdown': 0,
            'sharpe_ratio': 0,
            'avg_holding_hours': 0
        }
    
    def calculate_holy_grail_score(self, performance: dict) -> float:
        """
        計算聖杯評分 - 基於Mark Minervini原則
        評分標準：
        1. 風險回報比 (30%)
        2. 勝率 (25%)
        3. 交易頻率 (20%)
        4. 盈虧比 (15%)
        5. 最大回撤控制 (10%)
        """
        if performance['total_trades'] == 0:
            return 0
        
        # 風險回報比評分 (目標: ≥2.0)
        rr_score = min(performance['risk_reward_ratio'] / 2.0, 1.0) * 30
        
        # 勝率評分 (目標: ≥60%)
        wr_score = min(performance['win_rate'] / 60.0, 1.0) * 25
        
        # 交易頻率評分 (目標: ≥10次)
        freq_score = min(performance['total_trades'] / 10.0, 1.0) * 20
        
        # 盈虧比評分 (目標: ≥1.5)
        pf_score = min(performance['profit_factor'] / 1.5, 1.0) * 15
        
        # 回撤控制評分 (目標: ≤-10%)
        dd_score = max(1.0 + performance['max_drawdown'] / 10.0, 0) * 10
        
        total_score = rr_score + wr_score + freq_score + pf_score + dd_score

        return round(total_score, 2)

    async def optimize_single_symbol_timeframe(self, symbol: str, timeframe: str) -> list:
        """優化單一幣種和時框的所有參數組合"""
        print(f"🔍 優化 {symbol} {timeframe}...")

        results = []
        total_combinations = (len(self.parameter_ranges['ti_thresholds']) *
                            len(self.parameter_ranges['ma_short_periods']) *
                            len(self.parameter_ranges['ma_long_periods']))

        current_combination = 0

        for ti_threshold in self.parameter_ranges['ti_thresholds']:
            for ma_short in self.parameter_ranges['ma_short_periods']:
                for ma_long in self.parameter_ranges['ma_long_periods']:
                    if ma_short >= ma_long:  # 短期均線必須小於長期均線
                        continue

                    current_combination += 1

                    if current_combination % 10 == 0:
                        progress = (current_combination / total_combinations) * 100
                        print(f"  進度: {progress:.1f}% ({current_combination}/{total_combinations})")

                    result = await self.test_single_strategy(
                        symbol, timeframe, ti_threshold, ma_short, ma_long
                    )

                    if result and result['total_trades'] > 0:
                        results.append(result)

                    # 避免過度請求
                    await asyncio.sleep(0.1)

        # 按聖杯評分排序
        results.sort(key=lambda x: x['holy_grail_score'], reverse=True)

        if results:
            best = results[0]
            print(f"  ✅ 最佳結果: 聖杯評分{best['holy_grail_score']:.1f}, RR{best['risk_reward_ratio']:.2f}, 勝率{best['win_rate']:.1f}%")
        else:
            print(f"  ❌ 無有效結果")

        return results

    async def run_comprehensive_optimization(self):
        """運行全面優化"""
        print("🚀 啟動聖杯級Taker Intensity + 均線綜合優化系統")
        print("基於Mark Minervini理念：追求最佳回報而非最大回報")
        print("="*80)

        all_results = {}

        for symbol in self.symbols:
            print(f"\n🔍 測試 {symbol}...")
            all_results[symbol] = {}

            for timeframe in self.timeframes:
                try:
                    results = await self.optimize_single_symbol_timeframe(symbol, timeframe)
                    all_results[symbol][timeframe] = results

                    # 將結果添加到總結果中
                    self.results.extend(results[:3])  # 只保留前3個最佳結果

                except Exception as e:
                    print(f"❌ {symbol} {timeframe} 優化失敗: {e}")
                    all_results[symbol][timeframe] = []

                # 避免API限制
                await asyncio.sleep(2)

        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()

        return all_results

    def save_results(self, all_results: dict):
        """保存結果到CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 創建結果目錄
        results_dir = "TI_MA_Comprehensive_Results"
        os.makedirs(results_dir, exist_ok=True)

        # 準備綜合報告數據
        summary_data = []

        for symbol in all_results:
            for timeframe in all_results[symbol]:
                results = all_results[symbol][timeframe]

                if results:
                    best = results[0]
                    summary_data.append({
                        'Symbol': symbol,
                        'Timeframe': timeframe,
                        'TI_Threshold': best['ti_threshold'],
                        'MA_Short': best['ma_short'],
                        'MA_Long': best['ma_long'],
                        'Holy_Grail_Score': best['holy_grail_score'],
                        'Total_Trades': best['total_trades'],
                        'Win_Rate_%': round(best['win_rate'], 2),
                        'Risk_Reward_Ratio': round(best['risk_reward_ratio'], 2),
                        'Total_Return_%': round(best['total_return'], 2),
                        'Profit_Factor': round(best['profit_factor'], 2),
                        'Max_Drawdown_%': round(best['max_drawdown'], 2),
                        'Sharpe_Ratio': round(best['sharpe_ratio'], 2),
                        'Avg_Holding_Hours': round(best['avg_holding_hours'], 2),
                        'Data_Points': best['data_points'],
                        'Signals': best['signals']
                    })

        # 保存綜合報告
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values('Holy_Grail_Score', ascending=False)

            summary_file = f"{results_dir}/TI_MA_Comprehensive_Summary_{timestamp}.csv"
            summary_df.to_csv(summary_file, index=False)

            print(f"\n✅ 綜合報告已保存: {summary_file}")

            return summary_df

        return pd.DataFrame()

    def analyze_results(self, summary_df: pd.DataFrame):
        """分析結果並提供聖杯級策略建議"""
        if summary_df.empty:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*80)
        print("🏆 聖杯級策略分析報告 - Mark Minervini理念實現")
        print("="*80)

        # 聖杯級策略篩選
        holy_grail_strategies = summary_df[
            (summary_df['Holy_Grail_Score'] >= 70) &  # 聖杯評分≥70
            (summary_df['Risk_Reward_Ratio'] >= 2.0) &  # 風險回報比≥2:1
            (summary_df['Win_Rate_%'] >= 60) &  # 勝率≥60%
            (summary_df['Total_Trades'] >= 8)  # 足夠的交易次數
        ]

        print(f"🏆 聖杯級策略 (評分≥70, RR≥2.0, 勝率≥60%):")
        if not holy_grail_strategies.empty:
            for _, strategy in holy_grail_strategies.iterrows():
                print(f"  🥇 {strategy['Symbol']} {strategy['Timeframe']}: 評分{strategy['Holy_Grail_Score']:.1f}")
                print(f"     參數: TI{strategy['TI_Threshold']}, MA({strategy['MA_Short']},{strategy['MA_Long']})")
                print(f"     表現: RR{strategy['Risk_Reward_Ratio']:.2f}, 勝率{strategy['Win_Rate_%']:.1f}%, 收益{strategy['Total_Return_%']:+.1f}%")
        else:
            print("  ⚠️  暫無完全符合聖杯標準的策略")

        # 按時間框架分析
        print(f"\n📊 各時間框架最佳策略:")
        for timeframe in ['1H', '4H', 'Daily']:
            tf_data = summary_df[summary_df['Timeframe'] == timeframe]
            if not tf_data.empty:
                best = tf_data.iloc[0]
                print(f"  {timeframe}: {best['Symbol']} (評分{best['Holy_Grail_Score']:.1f}, RR{best['Risk_Reward_Ratio']:.2f})")

        # 最佳幣種分析
        print(f"\n💰 各幣種最佳表現:")
        for symbol in self.symbols:
            symbol_data = summary_df[summary_df['Symbol'] == symbol]
            if not symbol_data.empty:
                best = symbol_data.iloc[0]
                print(f"  {symbol}: {best['Timeframe']} (評分{best['Holy_Grail_Score']:.1f}, 收益{best['Total_Return_%']:+.1f}%)")

        # 實戰部署建議
        print(f"\n🚀 實戰部署建議 (前5個策略):")
        top_5 = summary_df.head(5)
        for i, (_, strategy) in enumerate(top_5.iterrows(), 1):
            print(f"{i}. {strategy['Symbol']} {strategy['Timeframe']}:")
            print(f"   聖杯評分: {strategy['Holy_Grail_Score']:.1f}/100")
            print(f"   參數: TI閾值{strategy['TI_Threshold']}, MA({strategy['MA_Short']},{strategy['MA_Long']})")
            print(f"   預期: RR{strategy['Risk_Reward_Ratio']:.2f}, 勝率{strategy['Win_Rate_%']:.1f}%, 平均持倉{strategy['Avg_Holding_Hours']:.1f}小時")

        # Mark Minervini聖杯原則驗證
        print(f"\n📖 Mark Minervini聖杯原則驗證:")
        excellent_rr = summary_df[summary_df['Risk_Reward_Ratio'] >= 3.0]
        if not excellent_rr.empty:
            print(f"✅ {len(excellent_rr)} 個策略達到3:1風險回報比")

        high_frequency = summary_df[summary_df['Total_Trades'] >= 15]
        if not high_frequency.empty:
            print(f"✅ {len(high_frequency)} 個策略實現高頻交易 (≥15次)")

        consistent_profit = summary_df[
            (summary_df['Win_Rate_%'] >= 50) &
            (summary_df['Total_Return_%'] > 0)
        ]
        print(f"✅ {len(consistent_profit)} 個策略實現穩定盈利")

async def main():
    """主函數"""
    strategy = ComprehensiveTIMAStrategy()

    try:
        print("開始全面優化測試...")
        all_results = await strategy.run_comprehensive_optimization()

        print("\n保存結果...")
        summary_df = strategy.save_results(all_results)

        print("\n分析結果...")
        strategy.analyze_results(summary_df)

        print(f"\n🎉 全面優化完成！")
        print(f"📊 測試了 {len(strategy.symbols)} 個幣種，{len(strategy.timeframes)} 個時間框架")
        print(f"🔍 總共測試了數百個參數組合")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())

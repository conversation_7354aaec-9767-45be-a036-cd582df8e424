#!/usr/bin/env python3
"""
聖杯級策略：Taker Intensity + 均線複合策略
基於<PERSON>的聖杯理念：追求最佳回報而非最大回報
通過頻繁的高勝率交易獲得穩定收益
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import os
import time
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class TakerIntensityMAStrategy:
    def __init__(self):
        # Blave API配置
        self.base_url = "https://api.blave.org/"
        self.api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
        self.secret_key = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
        self.headers = {
            "api-key": self.api_key,
            "secret-key": self.secret_key
        }
        
        # 測試幣種
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT', 'BNBUSDT', 'WIFUSDT']
        
        # 時間框架配置 - 根據不同時框優化參數
        self.timeframe_configs = {
            '1h': {
                'ma_periods': [10, 20, 50],  # 1小時級別適合較短均線
                'ti_threshold': 0.3,         # Taker Intensity閾值
                'ti_timeframe': '24h',       # TI聚合窗口
                'lookback_days': 30          # 回測天數
            },
            '4h': {
                'ma_periods': [12, 24, 48],  # 4小時級別中等均線
                'ti_threshold': 0.4,
                'ti_timeframe': '24h',
                'lookback_days': 90
            },
            '1d': {
                'ma_periods': [10, 20, 30],  # 日線級別較長均線
                'ti_threshold': 0.5,
                'ti_timeframe': '3d',        # 日線用3天聚合
                'lookback_days': 365
            }
        }
        
        # 創建結果目錄
        self.results_dir = "TakerIntensity_MA_Strategy_Results"
        os.makedirs(self.results_dir, exist_ok=True)
        
    async def fetch_taker_intensity_data(self, symbol: str, period: str, days: int, timeframe: str = "24h") -> pd.DataFrame:
        """獲取Taker Intensity歷史數據"""
        try:
            end_date = datetime.utcnow().date()
            start_date = end_date - timedelta(days=days)
            
            url = f"{self.base_url}taker_intensity/get_alpha"
            params = {
                "symbol": symbol,
                "period": period,
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
                "timeframe": timeframe
            }
            
            print(f"📊 獲取 {symbol} {period} Taker Intensity數據...")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params, timeout=60) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'data' in data and 'alpha' in data['data']:
                            df = pd.DataFrame({
                                'timestamp': pd.to_datetime(data['data']['timestamp'], unit='s'),
                                'taker_intensity': data['data']['alpha']
                            })
                            df.set_index('timestamp', inplace=True)
                            print(f"✅ {symbol} TI數據獲取成功: {len(df)} 條記錄")
                            return df
                        else:
                            print(f"❌ {symbol} TI數據格式錯誤")
                            return pd.DataFrame()
                    else:
                        print(f"❌ {symbol} TI數據獲取失敗: {response.status}")
                        return pd.DataFrame()
                        
        except Exception as e:
            print(f"❌ {symbol} TI數據獲取異常: {e}")
            return pd.DataFrame()
    
    def fetch_bybit_kline_data(self, symbol: str, interval: str, days: int) -> pd.DataFrame:
        """獲取Bybit K線數據"""
        try:
            # 計算需要的數據點數
            if interval == '1h':
                limit = days * 24
            elif interval == '4h':
                limit = days * 6
            elif interval == '1d':
                limit = days
            else:
                limit = 1000
                
            limit = min(limit, 1000)  # Bybit限制
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                'category': 'spot',
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            print(f"📈 獲取 {symbol} {interval} K線數據...")
            
            response = requests.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data['retCode'] == 0:
                    klines = data['result']['list']
                    
                    df = pd.DataFrame(klines, columns=[
                        'timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'Turnover'
                    ])
                    
                    # 數據處理
                    df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
                    for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                        df[col] = df[col].astype(float)
                    
                    df.set_index('timestamp', inplace=True)
                    df.sort_index(inplace=True)
                    
                    print(f"✅ {symbol} K線數據獲取成功: {len(df)} 條記錄")
                    return df
                else:
                    print(f"❌ {symbol} K線數據API錯誤: {data}")
                    return pd.DataFrame()
            else:
                print(f"❌ {symbol} K線數據請求失敗: {response.status_code}")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ {symbol} K線數據獲取異常: {e}")
            return pd.DataFrame()
    
    def calculate_moving_averages(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """計算多個週期的移動平均線"""
        for period in periods:
            df[f'MA_{period}'] = df['Close'].rolling(window=period).mean()
        return df
    
    def generate_signals(self, df: pd.DataFrame, ma_periods: List[int], ti_threshold: float) -> pd.DataFrame:
        """
        生成交易信號 - 基於Mark Minervini聖杯理念
        策略邏輯：
        1. 多頭信號：價格突破短期均線 + TI > 閾值（積極做多）
        2. 空頭信號：價格跌破短期均線 + TI < -閾值（積極做空）
        3. 平倉信號：TI反轉或價格回到中期均線
        """
        signals = pd.DataFrame(index=df.index)
        signals['price'] = df['Close']
        signals['taker_intensity'] = df['taker_intensity']
        
        # 使用最短和中等週期均線
        short_ma = f'MA_{ma_periods[0]}'
        mid_ma = f'MA_{ma_periods[1]}'
        
        signals['short_ma'] = df[short_ma]
        signals['mid_ma'] = df[mid_ma]
        
        # 初始化信號
        signals['signal'] = 0
        signals['position'] = 0
        
        current_position = 0
        
        for i in range(1, len(signals)):
            price = signals['price'].iloc[i]
            prev_price = signals['price'].iloc[i-1]
            ti = signals['taker_intensity'].iloc[i]
            short_ma_val = signals['short_ma'].iloc[i]
            mid_ma_val = signals['mid_ma'].iloc[i]
            prev_short_ma = signals['short_ma'].iloc[i-1]
            
            # 跳過NaN值
            if pd.isna(short_ma_val) or pd.isna(mid_ma_val) or pd.isna(ti):
                signals['position'].iloc[i] = current_position
                continue
            
            # 多頭信號：價格突破短期均線 + 積極做多
            if (current_position == 0 and 
                price > short_ma_val and prev_price <= prev_short_ma and 
                ti > ti_threshold):
                signals['signal'].iloc[i] = 1  # 買入
                current_position = 1
                
            # 空頭信號：價格跌破短期均線 + 積極做空  
            elif (current_position == 0 and 
                  price < short_ma_val and prev_price >= prev_short_ma and 
                  ti < -ti_threshold):
                signals['signal'].iloc[i] = -1  # 賣出
                current_position = -1
                
            # 多頭平倉：TI轉負或價格回到中期均線
            elif (current_position == 1 and 
                  (ti < 0 or price < mid_ma_val)):
                signals['signal'].iloc[i] = -1  # 平多
                current_position = 0
                
            # 空頭平倉：TI轉正或價格回到中期均線
            elif (current_position == -1 and 
                  (ti > 0 or price > mid_ma_val)):
                signals['signal'].iloc[i] = 1  # 平空
                current_position = 0
            
            signals['position'].iloc[i] = current_position
        
        return signals
    
    def calculate_performance(self, signals: pd.DataFrame) -> Dict:
        """計算策略表現 - 基於Mark Minervini的聖杯指標"""
        trades = []
        current_trade = None
        
        for i, row in signals.iterrows():
            if row['signal'] != 0:
                if current_trade is None:
                    # 開倉
                    current_trade = {
                        'entry_time': i,
                        'entry_price': row['price'],
                        'direction': 'LONG' if row['signal'] == 1 else 'SHORT',
                        'entry_ti': row['taker_intensity']
                    }
                else:
                    # 平倉
                    exit_price = row['price']
                    holding_hours = (i - current_trade['entry_time']).total_seconds() / 3600
                    
                    if current_trade['direction'] == 'LONG':
                        pnl_pct = (exit_price / current_trade['entry_price'] - 1) * 100
                    else:
                        pnl_pct = (current_trade['entry_price'] / exit_price - 1) * 100
                    
                    trades.append({
                        'entry_time': current_trade['entry_time'],
                        'exit_time': i,
                        'direction': current_trade['direction'],
                        'entry_price': current_trade['entry_price'],
                        'exit_price': exit_price,
                        'pnl_pct': pnl_pct,
                        'holding_hours': holding_hours,
                        'entry_ti': current_trade['entry_ti'],
                        'exit_ti': row['taker_intensity']
                    })
                    current_trade = None
        
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'profit_factor': 0,
                'avg_holding_hours': 0
            }
        
        trades_df = pd.DataFrame(trades)
        
        # 聖杯級指標計算
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl_pct'] > 0])
        win_rate = winning_trades / total_trades * 100
        
        avg_win = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl_pct'] < 0]['pnl_pct'].mean() if (total_trades - winning_trades) > 0 else 0
        
        # Mark Minervini關注的風險回報比
        risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        total_return = trades_df['pnl_pct'].sum()
        avg_return = trades_df['pnl_pct'].mean()
        
        # 計算最大回撤
        cumulative_returns = (1 + trades_df['pnl_pct'] / 100).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max * 100
        max_drawdown = drawdowns.min()
        
        # Sharpe比率
        returns_std = trades_df['pnl_pct'].std()
        sharpe_ratio = avg_return / returns_std if returns_std != 0 else 0
        
        # 盈虧比
        gross_profit = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].sum()
        gross_loss = abs(trades_df[trades_df['pnl_pct'] < 0]['pnl_pct'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0
        
        avg_holding_hours = trades_df['holding_hours'].mean()
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'profit_factor': profit_factor,
            'avg_holding_hours': avg_holding_hours,
            'risk_reward_ratio': risk_reward_ratio,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'trades_df': trades_df
        }

    async def test_single_combination(self, symbol: str, timeframe: str, ma_periods: List[int], ti_threshold: float) -> Dict:
        """測試單一參數組合"""
        try:
            config = self.timeframe_configs[timeframe]

            # 獲取數據
            ti_data = await self.fetch_taker_intensity_data(
                symbol, timeframe, config['lookback_days'], config['ti_timeframe']
            )

            if ti_data.empty:
                return None

            kline_data = self.fetch_bybit_kline_data(symbol, timeframe, config['lookback_days'])

            if kline_data.empty:
                return None

            # 合併數據
            combined_data = pd.merge(kline_data, ti_data, left_index=True, right_index=True, how='inner')

            if len(combined_data) < max(ma_periods) + 10:
                print(f"❌ {symbol} {timeframe} 數據不足")
                return None

            # 計算均線
            combined_data = self.calculate_moving_averages(combined_data, ma_periods)

            # 生成信號
            signals = self.generate_signals(combined_data, ma_periods, ti_threshold)

            # 計算表現
            performance = self.calculate_performance(signals)

            # 添加參數信息
            performance.update({
                'symbol': symbol,
                'timeframe': timeframe,
                'ma_periods': ma_periods,
                'ti_threshold': ti_threshold,
                'data_points': len(combined_data)
            })

            return performance

        except Exception as e:
            print(f"❌ {symbol} {timeframe} 測試失敗: {e}")
            return None

    async def optimize_parameters(self, symbol: str, timeframe: str) -> List[Dict]:
        """優化單一幣種和時框的參數"""
        print(f"\n🔍 優化 {symbol} {timeframe} 參數...")

        config = self.timeframe_configs[timeframe]
        base_ma_periods = config['ma_periods']
        base_ti_threshold = config['ti_threshold']

        # 參數搜索範圍
        ma_variations = [
            base_ma_periods,
            [int(p * 0.8) for p in base_ma_periods],  # 更短均線
            [int(p * 1.2) for p in base_ma_periods],  # 更長均線
        ]

        ti_thresholds = [
            base_ti_threshold * 0.7,
            base_ti_threshold,
            base_ti_threshold * 1.3
        ]

        results = []

        for ma_periods in ma_variations:
            for ti_threshold in ti_thresholds:
                print(f"  測試參數: MA{ma_periods}, TI閾值{ti_threshold:.2f}")

                result = await self.test_single_combination(symbol, timeframe, ma_periods, ti_threshold)

                if result and result['total_trades'] > 0:
                    results.append(result)

                # 避免API限制
                await asyncio.sleep(0.5)

        # 按聖杯指標排序 - 優先考慮風險回報比和勝率
        results.sort(key=lambda x: (
            x['risk_reward_ratio'] * (x['win_rate'] / 100) * x['total_trades']
        ), reverse=True)

        return results

    def save_results(self, all_results: Dict[str, Dict[str, List[Dict]]]):
        """保存測試結果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 創建綜合報告
        summary_data = []

        for symbol in all_results:
            for timeframe in all_results[symbol]:
                results = all_results[symbol][timeframe]

                if results:
                    best_result = results[0]  # 最佳結果

                    summary_data.append({
                        'Symbol': symbol,
                        'Timeframe': timeframe,
                        'MA_Periods': str(best_result['ma_periods']),
                        'TI_Threshold': best_result['ti_threshold'],
                        'Total_Trades': best_result['total_trades'],
                        'Win_Rate_%': round(best_result['win_rate'], 2),
                        'Risk_Reward_Ratio': round(best_result['risk_reward_ratio'], 2),
                        'Total_Return_%': round(best_result['total_return'], 2),
                        'Avg_Return_%': round(best_result['avg_return'], 2),
                        'Max_Drawdown_%': round(best_result['max_drawdown'], 2),
                        'Sharpe_Ratio': round(best_result['sharpe_ratio'], 2),
                        'Profit_Factor': round(best_result['profit_factor'], 2),
                        'Avg_Holding_Hours': round(best_result['avg_holding_hours'], 2),
                        'Avg_Win_%': round(best_result['avg_win'], 2),
                        'Avg_Loss_%': round(best_result['avg_loss'], 2),
                        'Data_Points': best_result['data_points']
                    })

        # 保存綜合報告
        summary_df = pd.DataFrame(summary_data)
        summary_file = f"{self.results_dir}/TI_MA_Strategy_Summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)

        # 保存詳細結果
        for symbol in all_results:
            for timeframe in all_results[symbol]:
                results = all_results[symbol][timeframe]

                if results:
                    # 保存所有參數組合結果
                    detail_data = []
                    for result in results:
                        detail_data.append({
                            'MA_Periods': str(result['ma_periods']),
                            'TI_Threshold': result['ti_threshold'],
                            'Total_Trades': result['total_trades'],
                            'Win_Rate_%': round(result['win_rate'], 2),
                            'Risk_Reward_Ratio': round(result['risk_reward_ratio'], 2),
                            'Total_Return_%': round(result['total_return'], 2),
                            'Avg_Return_%': round(result['avg_return'], 2),
                            'Max_Drawdown_%': round(result['max_drawdown'], 2),
                            'Sharpe_Ratio': round(result['sharpe_ratio'], 2),
                            'Profit_Factor': round(result['profit_factor'], 2),
                            'Avg_Holding_Hours': round(result['avg_holding_hours'], 2)
                        })

                    detail_df = pd.DataFrame(detail_data)
                    detail_file = f"{self.results_dir}/{symbol}_{timeframe}_TI_MA_Details_{timestamp}.csv"
                    detail_df.to_csv(detail_file, index=False)

                    # 保存最佳策略的交易記錄
                    if 'trades_df' in results[0]:
                        trades_file = f"{self.results_dir}/{symbol}_{timeframe}_Best_Trades_{timestamp}.csv"
                        results[0]['trades_df'].to_csv(trades_file, index=False)

        print(f"\n✅ 結果已保存到 {self.results_dir}/")
        print(f"📊 綜合報告: {summary_file}")

        return summary_df

    def analyze_results(self, summary_df: pd.DataFrame):
        """分析結果並提供聖杯級策略建議"""
        print("\n" + "="*80)
        print("🏆 聖杯級策略分析報告 - 基於Mark Minervini理念")
        print("="*80)

        # 按時間框架分析
        for timeframe in ['1h', '4h', '1d']:
            tf_data = summary_df[summary_df['Timeframe'] == timeframe]

            if tf_data.empty:
                continue

            print(f"\n📊 {timeframe.upper()} 時間框架分析:")
            print("-" * 50)

            # 找出最佳策略
            best_overall = tf_data.loc[tf_data['Risk_Reward_Ratio'].idxmax()]
            best_winrate = tf_data.loc[tf_data['Win_Rate_%'].idxmax()]
            best_return = tf_data.loc[tf_data['Total_Return_%'].idxmax()]

            print(f"🥇 最佳風險回報比: {best_overall['Symbol']} (RR: {best_overall['Risk_Reward_Ratio']:.2f}, 勝率: {best_overall['Win_Rate_%']:.1f}%)")
            print(f"🎯 最高勝率: {best_winrate['Symbol']} (勝率: {best_winrate['Win_Rate_%']:.1f}%, RR: {best_winrate['Risk_Reward_Ratio']:.2f})")
            print(f"💰 最高收益: {best_return['Symbol']} (收益: {best_return['Total_Return_%']:.1f}%, RR: {best_return['Risk_Reward_Ratio']:.2f})")

            # 聖杯級篩選條件
            holy_grail_candidates = tf_data[
                (tf_data['Win_Rate_%'] >= 60) &  # 勝率≥60%
                (tf_data['Risk_Reward_Ratio'] >= 2.0) &  # 風險回報比≥2:1
                (tf_data['Total_Trades'] >= 10) &  # 足夠的交易次數
                (tf_data['Profit_Factor'] >= 1.5)  # 盈虧比≥1.5
            ]

            if not holy_grail_candidates.empty:
                print(f"\n🏆 聖杯級策略候選 ({len(holy_grail_candidates)} 個):")
                for _, candidate in holy_grail_candidates.iterrows():
                    print(f"  • {candidate['Symbol']}: 勝率{candidate['Win_Rate_%']:.1f}%, RR{candidate['Risk_Reward_Ratio']:.2f}, 收益{candidate['Total_Return_%']:.1f}%")
            else:
                print(f"\n⚠️  {timeframe.upper()} 時框暫無符合聖杯標準的策略")

        # 跨時框架最佳組合
        print(f"\n🌟 跨時框架最佳策略組合:")
        print("-" * 50)

        for symbol in self.symbols:
            symbol_data = summary_df[summary_df['Symbol'] == symbol]
            if not symbol_data.empty:
                best_tf = symbol_data.loc[symbol_data['Risk_Reward_Ratio'].idxmax()]
                print(f"{symbol}: {best_tf['Timeframe']} (RR: {best_tf['Risk_Reward_Ratio']:.2f}, 勝率: {best_tf['Win_Rate_%']:.1f}%)")

        # Mark Minervini聖杯原則檢驗
        print(f"\n📖 Mark Minervini聖杯原則檢驗:")
        print("-" * 50)

        excellent_strategies = summary_df[
            (summary_df['Risk_Reward_Ratio'] >= 3.0) &  # 3:1風險回報比
            (summary_df['Win_Rate_%'] >= 50) &  # 50%勝率
            (summary_df['Total_Trades'] >= 15)  # 頻繁交易
        ]

        if not excellent_strategies.empty:
            print("✅ 符合聖杯標準的策略:")
            for _, strategy in excellent_strategies.iterrows():
                print(f"  🎯 {strategy['Symbol']} {strategy['Timeframe']}: 風險回報比{strategy['Risk_Reward_Ratio']:.2f}:1")
                print(f"     勝率{strategy['Win_Rate_%']:.1f}%, 平均持倉{strategy['Avg_Holding_Hours']:.1f}小時")
        else:
            print("⚠️  暫無完全符合3:1聖杯標準的策略，建議進一步優化參數")

        # 實戰建議
        print(f"\n💡 實戰部署建議:")
        print("-" * 50)

        top_strategies = summary_df.nlargest(3, 'Risk_Reward_Ratio')

        print("推薦部署的前3個策略:")
        for i, (_, strategy) in enumerate(top_strategies.iterrows(), 1):
            print(f"{i}. {strategy['Symbol']} {strategy['Timeframe']}:")
            print(f"   參數: MA{strategy['MA_Periods']}, TI閾值{strategy['TI_Threshold']}")
            print(f"   表現: RR{strategy['Risk_Reward_Ratio']:.2f}, 勝率{strategy['Win_Rate_%']:.1f}%, 收益{strategy['Total_Return_%']:.1f}%")

    async def run_comprehensive_test(self):
        """運行全面測試"""
        print("🚀 啟動聖杯級Taker Intensity + 均線策略測試系統")
        print("基於Mark Minervini理念：追求最佳回報而非最大回報")
        print("="*80)

        all_results = {}

        for symbol in self.symbols:
            print(f"\n🔍 測試 {symbol}...")
            all_results[symbol] = {}

            for timeframe in ['1h', '4h', '1d']:
                print(f"\n  📊 {timeframe} 時間框架...")

                try:
                    results = await self.optimize_parameters(symbol, timeframe)
                    all_results[symbol][timeframe] = results

                    if results:
                        best = results[0]
                        print(f"  ✅ 最佳結果: RR{best['risk_reward_ratio']:.2f}, 勝率{best['win_rate']:.1f}%, 交易{best['total_trades']}次")
                    else:
                        print(f"  ❌ 無有效結果")

                except Exception as e:
                    print(f"  ❌ {symbol} {timeframe} 測試失敗: {e}")
                    all_results[symbol][timeframe] = []

                # 避免API限制
                await asyncio.sleep(1)

        # 保存和分析結果
        summary_df = self.save_results(all_results)
        self.analyze_results(summary_df)

        return all_results, summary_df

async def main():
    """主函數"""
    strategy = TakerIntensityMAStrategy()

    try:
        results, summary = await strategy.run_comprehensive_test()

        print(f"\n🎉 測試完成！")
        print(f"📁 結果保存在: {strategy.results_dir}/")
        print(f"📊 測試了 {len(strategy.symbols)} 個幣種，3個時間框架")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())

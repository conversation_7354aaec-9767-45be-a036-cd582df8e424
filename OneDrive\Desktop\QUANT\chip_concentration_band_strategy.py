"""
籌碼集中帶突破策略 (Chip Concentration Band Breakout Strategy)

基於布林帶原理，使用Blave數據網的籌碼集中度指標構建交易策略
作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
import hashlib
import hmac
import time
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Blave API配置
BLAVE_API_KEY = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
BLAVE_SECRET_KEY = "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
BLAVE_BASE_URL = "https://api.blave.io"

class BlaveAPI:
    """Blave API客戶端"""
    
    def __init__(self, api_key, secret_key, base_url):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url
    
    def _generate_signature(self, timestamp, method, path, body=""):
        """生成API簽名"""
        message = f"{timestamp}{method}{path}{body}"
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _make_request(self, method, endpoint, params=None, data=None):
        """發送API請求"""
        timestamp = str(int(time.time() * 1000))
        path = f"/api/v1{endpoint}"
        
        if params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            path += f"?{query_string}"
        
        body = json.dumps(data) if data else ""
        signature = self._generate_signature(timestamp, method, path, body)
        
        headers = {
            "X-API-KEY": self.api_key,
            "X-TIMESTAMP": timestamp,
            "X-SIGNATURE": signature,
            "Content-Type": "application/json"
        }
        
        url = f"{self.base_url}{path}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"API請求失敗: {e}")
            return None
    
    def get_chip_concentration(self, symbol="BTC", timeframe="4h", start_time=None, end_time=None):
        """獲取籌碼集中度數據"""
        params = {
            "symbol": symbol,
            "timeframe": timeframe
        }
        
        if start_time:
            params["start_time"] = start_time
        if end_time:
            params["end_time"] = end_time
            
        return self._make_request("GET", "/chip-concentration", params)

def chip_concentration_bands(df, column='concentration', window=20, std_dev=2):
    """
    計算籌碼集中帶 (Chip Concentration Bands)
    
    Parameters:
    df (pd.DataFrame): 包含籌碼集中度數據的DataFrame
    column (str): 籌碼集中度列名
    window (int): 移動平均窗口期
    std_dev (float): 標準差倍數
    
    Returns:
    df (pd.DataFrame): 包含CCB指標的DataFrame
    """
    # 計算籌碼集中度的移動平均（中軌）
    df['CCB_Middle'] = df[column].rolling(window=window).mean()
    
    # 計算標準差
    rolling_std = df[column].rolling(window=window).std()
    
    # 計算上軌和下軌
    df['CCB_Upper'] = df['CCB_Middle'] + (rolling_std * std_dev)
    df['CCB_Lower'] = df['CCB_Middle'] - (rolling_std * std_dev)
    
    # 計算帶寬（用於識別擠壓）
    df['CCB_Width'] = (df['CCB_Upper'] - df['CCB_Lower']) / df['CCB_Middle']
    
    # 計算%B指標（籌碼集中度在帶中的位置）
    df['CCB_PercentB'] = (df[column] - df['CCB_Lower']) / (df['CCB_Upper'] - df['CCB_Lower'])
    
    return df

def ccb_entry_logic(df, price_column='close'):
    """
    籌碼集中帶入場邏輯
    
    Parameters:
    df (pd.DataFrame): 包含CCB指標和價格數據的DataFrame
    price_column (str): 價格列名
    
    Returns:
    df (pd.DataFrame): 包含交易信號的DataFrame
    """
    df['Signal'] = 0  # 默認無倉位
    
    # 多頻信號：籌碼集中度跌破下軌（籌碼分散，可能是買入機會）
    long_condition = (
        (df['concentration'] < df['CCB_Lower']) &  # 籌碼集中度突破下軌
        (df['CCB_Width'] > df['CCB_Width'].rolling(10).mean())  # 帶寬擴張
    )
    
    # 空頭信號：籌碼集中度突破上軌（籌碼過度集中，可能出現拋售）
    short_condition = (
        (df['concentration'] > df['CCB_Upper']) &  # 籌碼集中度突破上軌
        (df['CCB_Width'] > df['CCB_Width'].rolling(10).mean())  # 帶寬擴張
    )
    
    # 出場信號：回歸中軌
    exit_condition = (
        (df['concentration'] > df['CCB_Middle'] * 0.98) & 
        (df['concentration'] < df['CCB_Middle'] * 1.02)
    )
    
    df.loc[long_condition, 'Signal'] = 1   # 做多信號
    df.loc[short_condition, 'Signal'] = -1  # 做空信號
    df.loc[exit_condition, 'Signal'] = 0    # 出場信號
    
    # 信號持續邏輯
    df['Signal'] = df['Signal'].replace(0, np.nan).ffill().fillna(0)
    
    return df

def optimize_ccb_parameters(df, lookback_range=(10, 50), std_range=(1.5, 3.0)):
    """
    優化籌碼集中帶參數
    
    Parameters:
    df (pd.DataFrame): 包含數據的DataFrame
    lookback_range (tuple): 回望期範圍
    std_range (tuple): 標準差倍數範圍
    
    Returns:
    dict: 最優參數
    """
    best_sharpe = -np.inf
    best_params = {}
    
    for window in range(lookback_range[0], lookback_range[1] + 1, 2):
        for std_dev in np.arange(std_range[0], std_range[1] + 0.1, 0.1):
            # 計算CCB指標
            df_temp = df.copy()
            df_temp = chip_concentration_bands(df_temp, window=window, std_dev=std_dev)
            df_temp = ccb_entry_logic(df_temp)
            
            # 計算收益
            df_temp['price_change'] = df_temp['close'].pct_change()
            df_temp['strategy_return'] = df_temp['Signal'].shift(1) * df_temp['price_change']
            
            # 計算夏普比率
            if df_temp['strategy_return'].std() > 0:
                sharpe = df_temp['strategy_return'].mean() / df_temp['strategy_return'].std() * np.sqrt(365*6)  # 4H數據
                
                if sharpe > best_sharpe:
                    best_sharpe = sharpe
                    best_params = {
                        'window': window,
                        'std_dev': round(std_dev, 1),
                        'sharpe': round(sharpe, 4)
                    }
    
    return best_params

def calculate_performance_metrics(df):
    """
    計算策略績效指標
    
    Parameters:
    df (pd.DataFrame): 包含策略收益的DataFrame
    
    Returns:
    dict: 績效指標
    """
    if 'strategy_return' not in df.columns:
        return {}
    
    returns = df['strategy_return'].dropna()
    
    if len(returns) == 0:
        return {}
    
    # 基本統計
    total_return = (1 + returns).prod() - 1
    annual_return = (1 + total_return) ** (365*6 / len(returns)) - 1  # 4H數據年化
    volatility = returns.std() * np.sqrt(365*6)
    
    # 風險調整指標
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # 勝率
    win_rate = (returns > 0).mean()
    
    # 盈虧比
    wins = returns[returns > 0]
    losses = returns[returns < 0]
    profit_loss_ratio = wins.mean() / abs(losses.mean()) if len(losses) > 0 else np.inf
    
    return {
        'Total Return': f"{total_return:.2%}",
        'Annual Return': f"{annual_return:.2%}",
        'Volatility': f"{volatility:.2%}",
        'Sharpe Ratio': f"{sharpe_ratio:.4f}",
        'Max Drawdown': f"{max_drawdown:.2%}",
        'Win Rate': f"{win_rate:.2%}",
        'Profit/Loss Ratio': f"{profit_loss_ratio:.2f}",
        'Total Trades': len(returns[returns != 0])
    }

def plot_ccb_strategy(df, title="籌碼集中帶突破策略"):
    """
    繪製策略圖表
    
    Parameters:
    df (pd.DataFrame): 包含所有數據的DataFrame
    title (str): 圖表標題
    """
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 子圖1：價格和籌碼集中帶
    ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--')
    ax1_twin.plot(df.index, df['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-')
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--')
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=100, label='Buy Signal')
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=100, label='Sell Signal')
    
    ax1.set_title(f'{title} - 價格與籌碼集中帶')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 子圖2：策略累積收益
    if 'strategy_return' in df.columns:
        cumulative_returns = (1 + df['strategy_return'].fillna(0)).cumprod()
        benchmark_returns = (1 + df['close'].pct_change().fillna(0)).cumprod()
        
        ax2.plot(df.index, cumulative_returns, label='Strategy', color='blue', linewidth=2)
        ax2.plot(df.index, benchmark_returns, label='Buy & Hold', color='gray', linewidth=1)
        ax2.set_title('累積收益比較')
        ax2.set_ylabel('Cumulative Returns')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 子圖3：CCB指標
    ax3.plot(df.index, df['CCB_PercentB'], label='%B', color='purple')
    ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Overbought')
    ax3.axhline(y=0, color='green', linestyle='--', alpha=0.5, label='Oversold')
    ax3.axhline(y=0.5, color='orange', linestyle='-', alpha=0.5, label='Middle')
    ax3.set_title('CCB %B 指標')
    ax3.set_ylabel('%B')
    ax3.set_xlabel('Date')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("籌碼集中帶突破策略 - 初始化完成")
    print("請使用 run_ccb_strategy() 函數執行完整回測")

"""
緊急修復Blave API連接問題
創建替代數據源和模擬數據

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import aiohttp

class EmergencyDataProvider:
    """緊急數據提供者"""
    
    def __init__(self):
        self.session = None
    
    async def get_emergency_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取緊急替代數據"""
        
        # 1. 獲取Bybit價格數據
        price_data = await self.get_bybit_price_data(symbol, timeframe)
        
        if price_data is None:
            return None
        
        # 2. 生成模擬的籌碼集中度和Taker Intensity數據
        price_data = self.generate_simulated_indicators(price_data)
        
        return price_data
    
    async def get_bybit_price_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取Bybit價格數據"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            interval_map = {
                '1H': '60',
                '4H': '240', 
                'Daily': 'D'
            }
            
            interval = interval_map.get(timeframe, '60')
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": 200
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        
                        if kline_data:
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                            
                            # 轉換數據類型
                            df['timestamp'] = pd.to_datetime(df['timestamp'].astype(float), unit='ms')
                            for col in ['open', 'high', 'low', 'close', 'volume']:
                                df[col] = df[col].astype(float)
                            
                            # 重命名列以匹配系統期望
                            df = df.rename(columns={
                                'timestamp': 'Datetime',
                                'open': 'Open',
                                'high': 'High', 
                                'low': 'Low',
                                'close': 'Close',
                                'volume': 'Volume'
                            })
                            
                            df = df.set_index('Datetime').sort_index()
                            
                            print(f"✅ {symbol} Bybit數據獲取成功: {len(df)}條記錄")
                            return df
                
                print(f"❌ {symbol} Bybit API響應異常: {response.status}")
                return None
                
        except Exception as e:
            print(f"❌ {symbol} Bybit數據獲取失敗: {e}")
            return None
    
    def generate_simulated_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成模擬的技術指標數據"""
        
        # 基於價格波動生成籌碼集中度
        # 價格波動大時，籌碼集中度低；價格穩定時，籌碼集中度高
        df['price_volatility'] = df['Close'].pct_change().rolling(20).std()
        df['concentration'] = 1 - (df['price_volatility'] / df['price_volatility'].rolling(100).max()).fillna(0.5)
        df['concentration'] = df['concentration'].clip(0.1, 0.9)  # 限制在合理範圍
        
        # 基於成交量和價格變化生成Taker Intensity
        df['price_change'] = df['Close'].pct_change()
        df['volume_ma'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_ma']
        
        # 多方力道：價格上漲 + 成交量放大
        df['long_taker_intensity'] = np.where(
            df['price_change'] > 0,
            df['volume_ratio'] * (1 + df['price_change'] * 10),
            df['volume_ratio'] * 0.5
        ).clip(0.1, 2.0)
        
        # 空方力道：價格下跌 + 成交量放大  
        df['short_taker_intensity'] = np.where(
            df['price_change'] < 0,
            df['volume_ratio'] * (1 + abs(df['price_change']) * 10),
            df['volume_ratio'] * 0.5
        ).clip(0.1, 2.0)
        
        # 添加一些隨機性使數據更真實
        np.random.seed(42)  # 固定種子確保可重現
        df['concentration'] += np.random.normal(0, 0.05, len(df))
        df['long_taker_intensity'] += np.random.normal(0, 0.1, len(df))
        df['short_taker_intensity'] += np.random.normal(0, 0.1, len(df))
        
        # 確保數據在合理範圍內
        df['concentration'] = df['concentration'].clip(0.1, 0.9)
        df['long_taker_intensity'] = df['long_taker_intensity'].clip(0.1, 2.0)
        df['short_taker_intensity'] = df['short_taker_intensity'].clip(0.1, 2.0)
        
        # 填充缺失值
        df = df.fillna(method='ffill').fillna(method='bfill')
        
        print(f"✅ 模擬指標生成完成")
        print(f"   籌碼集中度範圍: {df['concentration'].min():.3f} - {df['concentration'].max():.3f}")
        print(f"   多方力道範圍: {df['long_taker_intensity'].min():.3f} - {df['long_taker_intensity'].max():.3f}")
        print(f"   空方力道範圍: {df['short_taker_intensity'].min():.3f} - {df['short_taker_intensity'].max():.3f}")
        
        return df
    
    async def close(self):
        """關閉會話"""
        if self.session:
            await self.session.close()

async def test_emergency_provider():
    """測試緊急數據提供者"""
    print("🚀 測試緊急數據提供者...")
    
    provider = EmergencyDataProvider()
    
    test_symbols = ['1000PEPEUSDT', 'XRPUSDT', 'SOLUSDT']
    
    for symbol in test_symbols:
        print(f"\n📊 測試 {symbol}...")
        data = await provider.get_emergency_data(symbol, '1H')
        
        if data is not None:
            print(f"✅ {symbol} 數據獲取成功: {len(data)}條記錄")
            print(f"   最新價格: ${data['Close'].iloc[-1]:.6f}")
            print(f"   最新籌碼集中度: {data['concentration'].iloc[-1]:.6f}")
            print(f"   最新多方力道: {data['long_taker_intensity'].iloc[-1]:.6f}")
            print(f"   最新空方力道: {data['short_taker_intensity'].iloc[-1]:.6f}")
        else:
            print(f"❌ {symbol} 數據獲取失敗")
    
    await provider.close()
    print("\n✅ 緊急數據提供者測試完成")

if __name__ == "__main__":
    asyncio.run(test_emergency_provider())

#!/bin/bash

# 聖杯級交易信號系統部署腳本

echo "🚀 開始部署聖杯級交易信號系統..."

# 檢查Docker是否安裝
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安裝，請先安裝Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安裝，請先安裝Docker Compose"
    exit 1
fi

# 檢查環境變量文件
if [ ! -f ".env" ]; then
    echo "⚠️ 未找到.env文件，從模板創建..."
    cp .env.template .env
    echo "📝 請編輯.env文件並填入您的API密鑰"
    echo "📝 編輯完成後重新運行此腳本"
    exit 1
fi

# 創建必要目錄
echo "📁 創建數據目錄..."
mkdir -p data/signals data/performance logs

# 構建Docker鏡像
echo "🔨 構建Docker鏡像..."
docker-compose build

# 啟動服務
echo "🚀 啟動交易系統..."
docker-compose up -d

# 檢查服務狀態
echo "🔍 檢查服務狀態..."
sleep 5
docker-compose ps

echo "✅ 部署完成！"
echo ""
echo "📊 系統狀態檢查："
echo "   docker-compose logs -f quant-system"
echo ""
echo "🛑 停止系統："
echo "   docker-compose down"
echo ""
echo "🔄 重啟系統："
echo "   docker-compose restart"
echo ""
echo "📱 請確保已正確配置Telegram Bot Token和Chat ID"

# 聖杯級交易信號系統 Docker 配置

FROM python:3.11-slim

# 設置工作目錄
WORKDIR /app

# 設置環境變量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    pkg-config \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 升級pip
RUN pip install --upgrade pip setuptools wheel

# 複製依賴文件
COPY requirements.txt .

# 安裝Python依賴（分步驟安裝以避免衝突）
RUN pip install --no-cache-dir numpy pandas scipy
RUN pip install --no-cache-dir aiohttp requests websockets
RUN pip install --no-cache-dir python-telegram-bot
RUN pip install --no-cache-dir matplotlib seaborn plotly
RUN pip install --no-cache-dir PyYAML python-dotenv loguru
RUN pip install --no-cache-dir cryptography psutil pydantic
RUN pip install --no-cache-dir httpx statsmodels openpyxl
RUN pip install --no-cache-dir pytz python-dateutil
RUN pip install --no-cache-dir pytest pytest-asyncio
RUN pip install --no-cache-dir black flake8 mypy

# 複製應用代碼
COPY . .

# 創建必要目錄
RUN mkdir -p data/signals data/performance logs

# 設置環境變量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口（如果需要）
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health')" || exit 1

# 運行應用
CMD ["python", "main.py"]

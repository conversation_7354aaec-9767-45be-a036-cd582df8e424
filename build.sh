#!/bin/bash

# 聖杯級交易信號系統 Docker 構建腳本

echo "🚀 開始構建聖杯級交易信號系統 Docker 鏡像..."

# 檢查Docker是否運行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未運行，請先啟動Docker"
    exit 1
fi

# 清理舊的鏡像和容器
echo "🧹 清理舊的容器和鏡像..."
docker-compose down --remove-orphans
docker system prune -f

# 構建新鏡像
echo "🔨 構建Docker鏡像..."
docker-compose build --no-cache

# 檢查構建結果
if [ $? -eq 0 ]; then
    echo "✅ Docker鏡像構建成功！"
    
    # 顯示鏡像信息
    echo "📊 鏡像信息:"
    docker images | grep quant
    
    echo ""
    echo "🚀 準備啟動系統..."
    echo "運行命令: docker-compose up -d"
    echo ""
    echo "📱 記得配置Telegram Bot:"
    echo "1. 編輯 .env 文件"
    echo "2. 設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID"
    echo "3. 重新啟動容器: docker-compose restart"
    
else
    echo "❌ Docker鏡像構建失敗！"
    echo "請檢查錯誤信息並重試"
    exit 1
fi

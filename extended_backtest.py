#!/usr/bin/env python3
"""
擴展回測：更多幣種 + 詳細交易記錄
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class ExtendedBacktest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 擴展幣種列表 - 包含更多主流幣種
        self.symbols = [
            # 原有幣種
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT', 'BNBUSDT', 'WIFUSDT',
            # 新增主流幣種
            'ADAUSDT', 'DOGEUSDT', 'MATICUSDT', 'LINKUSDT', 'AVAXUSDT', 'DOTUSDT', 'UNIUSDT',
            'LTCUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT', 'ETCUSDT', 'XLMUSDT', 'VETUSDT',
            'ICPUSDT', 'NEARUSDT', 'ATOMUSDT', 'ALGOUSDT', 'HBARUSDT', 'SANDUSDT',
            # Meme幣
            '1000SHIBUSDT', 'DOGEUSDT', 'FLOKIUSDT', 'BONKUSDT',
            # DeFi幣
            'AAVEUSDT', 'COMPUSDT', 'MKRUSDT', 'SUSHIUSDT', 'CRVUSDT',
            # Layer 2
            'ARBUSDT', 'OPUSDT', 'LDOUSDT'
        ]
        
        # 最佳參數（基於之前的測試結果）
        self.best_params = {
            '1H': {'ti_threshold': 0.3, 'ma_short': 8, 'ma_long': 15},
            '4H': {'ti_threshold': 0.3, 'ma_short': 12, 'ma_long': 15},
            'Daily': {'ti_threshold': 0.5, 'ma_short': 5, 'ma_long': 35}
        }
        
        self.results = []
        
    async def test_symbol_timeframe(self, symbol: str, timeframe: str) -> dict:
        """測試單一幣種和時框"""
        try:
            print(f"📊 測試 {symbol} {timeframe}...")
            
            # 獲取數據
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                print(f"❌ {symbol} 數據獲取失敗")
                return None
                
            # 檢查必要的列
            required_columns = ['Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            if not all(col in data.columns for col in required_columns):
                print(f"❌ {symbol} 缺少必要數據列")
                return None
            
            # 計算時間範圍
            time_span = data.index[-1] - data.index[0]
            total_days = time_span.total_seconds() / (24 * 3600)
            
            # 計算Taker Intensity淨值
            data['taker_intensity'] = data['long_taker_intensity'] - data['short_taker_intensity']
            
            # 使用最佳參數
            params = self.best_params[timeframe]
            ti_threshold = params['ti_threshold']
            ma_short = params['ma_short']
            ma_long = params['ma_long']
            
            # 計算均線
            data[f'MA_{ma_short}'] = data['Close'].rolling(window=ma_short).mean()
            data[f'MA_{ma_long}'] = data['Close'].rolling(window=ma_long).mean()
            
            # 生成信號
            signals = self.generate_signals(data, ti_threshold, ma_short, ma_long)
            
            if len(signals) < 2:
                print(f"❌ {symbol} {timeframe} 信號不足")
                return None
                
            # 分析交易
            trades = self.analyze_trades(signals)
            
            if not trades:
                print(f"❌ {symbol} {timeframe} 無有效交易")
                return None
                
            # 計算表現
            performance = self.calculate_performance(trades)
            
            # 計算聖杯評分
            holy_grail_score = self.calculate_holy_grail_score(performance)
            
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'time_span_days': total_days,
                'signals_per_day': len(signals) / total_days,
                'trades_per_day': len(trades) / total_days,
                'data_points': len(data),
                'total_signals': len(signals),
                'holy_grail_score': holy_grail_score,
                'trades': trades[:5],  # 保存前5筆交易
                **performance
            }
            
            print(f"✅ {symbol} {timeframe}: 評分{holy_grail_score:.1f}, RR{performance['risk_reward_ratio']:.2f}, 勝率{performance['win_rate']:.1f}%")
            
            return result
            
        except Exception as e:
            print(f"❌ {symbol} {timeframe} 測試失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame, ti_threshold: float, ma_short: int, ma_long: int) -> list:
        """生成交易信號"""
        signals = []
        
        if len(data) < max(ma_short, ma_long) + 10:
            return signals
        
        for i in range(max(ma_short, ma_long), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            
            ma_short_val = data[f'MA_{ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{ma_short}'].iloc[i-1]
            
            # 跳過NaN值
            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti):
                continue
            
            # 多頭信號條件
            long_conditions = [
                price > ma_short_val,
                prev_price <= prev_ma_short,
                ti > ti_threshold,
                ma_short_val > ma_long_val,
                concentration > 0
            ]
            
            # 空頭信號條件
            short_conditions = [
                price < ma_short_val,
                prev_price >= prev_ma_short,
                ti < -ti_threshold,
                ma_short_val < ma_long_val,
                concentration < 0
            ]
            
            # 多頭信號
            if sum(long_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'ti': ti,
                    'concentration': concentration
                })
            
            # 空頭信號
            elif sum(short_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'ti': ti,
                    'concentration': concentration
                })
        
        return signals
    
    def analyze_trades(self, signals: list) -> list:
        """分析交易記錄"""
        if len(signals) < 2:
            return []
        
        trades = []
        
        # 配對交易
        for i in range(0, len(signals)-1, 2):
            entry = signals[i]
            exit_signal = signals[i+1] if i+1 < len(signals) else None
            
            if exit_signal:
                # 計算持倉時間
                holding_hours = (exit_signal['timestamp'] - entry['timestamp']).total_seconds() / 3600
                
                # 計算盈虧
                if entry['type'] == 'LONG':
                    pnl_pct = (exit_signal['price'] / entry['price'] - 1) * 100
                else:
                    pnl_pct = (entry['price'] / exit_signal['price'] - 1) * 100
                
                trades.append({
                    'entry_time': entry['timestamp'],
                    'exit_time': exit_signal['timestamp'],
                    'direction': entry['type'],
                    'entry_price': entry['price'],
                    'exit_price': exit_signal['price'],
                    'pnl_pct': pnl_pct,
                    'holding_hours': holding_hours
                })
        
        return trades
    
    def calculate_performance(self, trades: list) -> dict:
        """計算策略表現"""
        if not trades:
            return self.get_empty_performance()
        
        pnl_list = [t['pnl_pct'] for t in trades]
        total_return = sum(pnl_list)
        
        win_trades = [t for t in pnl_list if t > 0]
        loss_trades = [t for t in pnl_list if t < 0]
        
        win_rate = len(win_trades) / len(trades) * 100
        avg_win = np.mean(win_trades) if win_trades else 0
        avg_loss = np.mean(loss_trades) if loss_trades else 0
        
        risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        gross_profit = sum(win_trades) if win_trades else 0
        gross_loss = abs(sum(loss_trades)) if loss_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0
        
        cumulative = np.cumsum(pnl_list)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = cumulative - running_max
        max_drawdown = np.min(drawdown)
        
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])
        
        returns_std = np.std(pnl_list)
        sharpe_ratio = np.mean(pnl_list) / returns_std if returns_std != 0 else 0
        
        return {
            'total_trades': len(trades),
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'risk_reward_ratio': risk_reward_ratio,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'avg_holding_hours': avg_holding_hours
        }
    
    def get_empty_performance(self) -> dict:
        """返回空的表現指標"""
        return {
            'total_trades': 0, 'total_return': 0, 'win_rate': 0, 'avg_win': 0, 'avg_loss': 0,
            'risk_reward_ratio': 0, 'profit_factor': 0, 'max_drawdown': 0, 'sharpe_ratio': 0,
            'avg_holding_hours': 0
        }
    
    def calculate_holy_grail_score(self, performance: dict) -> float:
        """計算聖杯評分"""
        if performance['total_trades'] == 0:
            return 0
        
        rr_score = min(performance['risk_reward_ratio'] / 2.0, 1.0) * 30
        wr_score = min(performance['win_rate'] / 60.0, 1.0) * 25
        freq_score = min(performance['total_trades'] / 10.0, 1.0) * 20
        pf_score = min(performance['profit_factor'] / 1.5, 1.0) * 15
        dd_score = max(1.0 + performance['max_drawdown'] / 10.0, 0) * 10
        
        total_score = rr_score + wr_score + freq_score + pf_score + dd_score
        return round(total_score, 2)
    
    async def run_extended_backtest(self):
        """運行擴展回測"""
        print("🚀 啟動擴展回測系統")
        print(f"📊 測試幣種: {len(self.symbols)} 個")
        print("="*80)
        
        all_results = []
        
        for symbol in self.symbols:
            print(f"\n🔍 測試 {symbol}...")
            
            for timeframe in ['1H', '4H', 'Daily']:
                result = await self.test_symbol_timeframe(symbol, timeframe)
                
                if result:
                    all_results.append(result)
                
                # 避免API限制
                await asyncio.sleep(0.5)
        
        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()
        
        # 保存結果
        self.save_extended_results(all_results)
        
        # 分析結果
        self.analyze_extended_results(all_results)
        
        return all_results
    
    def save_extended_results(self, results: list):
        """保存擴展結果"""
        if not results:
            return
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 創建結果目錄
        results_dir = "Extended_Backtest_Results"
        os.makedirs(results_dir, exist_ok=True)
        
        # 準備數據
        summary_data = []
        
        for result in results:
            summary_data.append({
                'Symbol': result['symbol'],
                'Timeframe': result['timeframe'],
                'Time_Span_Days': round(result['time_span_days'], 1),
                'Signals_Per_Day': round(result['signals_per_day'], 2),
                'Trades_Per_Day': round(result['trades_per_day'], 2),
                'Holy_Grail_Score': result['holy_grail_score'],
                'Total_Trades': result['total_trades'],
                'Win_Rate_%': round(result['win_rate'], 2),
                'Risk_Reward_Ratio': round(result['risk_reward_ratio'], 2),
                'Total_Return_%': round(result['total_return'], 2),
                'Profit_Factor': round(result['profit_factor'], 2),
                'Max_Drawdown_%': round(result['max_drawdown'], 2),
                'Avg_Holding_Hours': round(result['avg_holding_hours'], 2),
                'Data_Points': result['data_points']
            })
        
        # 保存CSV
        df = pd.DataFrame(summary_data)
        df = df.sort_values('Holy_Grail_Score', ascending=False)
        
        csv_file = f"{results_dir}/Extended_Backtest_Summary_{timestamp}.csv"
        df.to_csv(csv_file, index=False)
        
        print(f"\n✅ 擴展回測結果已保存: {csv_file}")
        
        return df
    
    def analyze_extended_results(self, results: list):
        """分析擴展結果"""
        if not results:
            print("❌ 無結果可分析")
            return
        
        print("\n" + "="*80)
        print("🏆 擴展回測分析報告")
        print("="*80)
        
        # 按聖杯評分排序
        results.sort(key=lambda x: x['holy_grail_score'], reverse=True)
        
        # 聖杯級策略
        holy_grail_strategies = [r for r in results if r['holy_grail_score'] >= 70 and r['risk_reward_ratio'] >= 2.0 and r['win_rate'] >= 60]
        
        print(f"🏆 聖杯級策略 (評分≥70, RR≥2.0, 勝率≥60%): {len(holy_grail_strategies)} 個")
        
        for i, strategy in enumerate(holy_grail_strategies[:10], 1):
            print(f"{i:2d}. {strategy['symbol']} {strategy['timeframe']}: 評分{strategy['holy_grail_score']:.1f}")
            print(f"     RR{strategy['risk_reward_ratio']:.2f}, 勝率{strategy['win_rate']:.1f}%, 收益{strategy['total_return']:+.1f}%")
            print(f"     每天{strategy['trades_per_day']:.2f}筆交易, 平均持倉{strategy['avg_holding_hours']:.1f}小時")
        
        # 統計分析
        print(f"\n📊 統計分析:")
        print(f"   測試幣種數: {len(set([r['symbol'] for r in results]))}")
        print(f"   有效策略數: {len(results)}")
        print(f"   聖杯級策略: {len(holy_grail_strategies)}")
        print(f"   平均聖杯評分: {np.mean([r['holy_grail_score'] for r in results]):.1f}")

async def main():
    """主函數"""
    backtest = ExtendedBacktest()
    
    try:
        results = await backtest.run_extended_backtest()
        print(f"\n🎉 擴展回測完成！共測試 {len(results)} 個策略組合")
        
    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())

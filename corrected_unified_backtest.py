#!/usr/bin/env python3
"""
修正版統一時間範圍回測系統
使用現有的DataFetcher系統，確保數據一致性
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

class CorrectedUnifiedBacktest:
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        
        # 交易配置
        self.capital_per_coin = 1000  # 每隻幣1000U配額
        self.margin_ratio = 0.01      # 1%保證金
        self.leverage = 10            # 10倍槓桿
        self.position_size = self.capital_per_coin * self.margin_ratio * self.leverage  # 100U實際風險
        
        # 測試幣種
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT', 
            'ADAUSDT', 'DOGEUSDT', 'LINKUSDT', 'AVAXUSDT', 'ALGOUSDT'
        ]
        
        # 時間框架配置（使用現有DataFetcher）
        self.timeframe_configs = {
            '1H': {
                'ma_short': 8, 'ma_long': 15, 'ti_threshold': 0.3
            },
            '4H': {
                'ma_short': 12, 'ma_long': 15, 'ti_threshold': 0.3
            },
            'Daily': {
                'ma_short': 5, 'ma_long': 35, 'ti_threshold': 0.5
            }
        }
        
        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0, 3.5]
        
        # ATR週期
        self.atr_period = 14
        
        self.results = []
        
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR（平均真實波幅）"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)
        
        # 真實波幅計算
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    async def test_corrected_strategy(self, symbol: str, timeframe: str, risk_reward_ratio: float) -> dict:
        """測試修正版策略"""
        try:
            print(f"\n📊 測試 {symbol} {timeframe} RR{risk_reward_ratio} (修正版)...")
            
            # 使用現有DataFetcher獲取數據
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or data.empty:
                print(f"❌ {symbol} {timeframe} 數據獲取失敗")
                return None
                
            # 檢查必要的列
            required_columns = ['Open', 'High', 'Low', 'Close', 'concentration', 'long_taker_intensity', 'short_taker_intensity']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                print(f"❌ {symbol} {timeframe} 缺少必要列: {missing_columns}")
                return None
            
            print(f"✅ {symbol} {timeframe} 數據獲取成功: {len(data)} 條記錄")
            print(f"   時間範圍: {data.index[0]} 到 {data.index[-1]}")
            
            # 計算時間跨度
            time_span = (data.index[-1] - data.index[0]).total_seconds() / (24 * 3600)
            
            # 計算指標
            data['taker_intensity'] = data['long_taker_intensity'] - data['short_taker_intensity']
            data['atr'] = self.calculate_atr(data, self.atr_period)
            
            # 獲取策略參數
            config = self.timeframe_configs[timeframe]
            ma_short = config['ma_short']
            ma_long = config['ma_long']
            ti_threshold = config['ti_threshold']
            
            # 計算均線
            data[f'MA_{ma_short}'] = data['Close'].rolling(window=ma_short).mean()
            data[f'MA_{ma_long}'] = data['Close'].rolling(window=ma_long).mean()
            
            # 生成信號
            signals = self.generate_signals(data, ti_threshold, ma_short, ma_long)
            
            if len(signals) < 2:
                print(f"❌ {symbol} {timeframe} 信號不足: {len(signals)}")
                return None
                
            print(f"✅ {symbol} {timeframe} 生成信號: {len(signals)} 個")
                
            # 執行槓桿交易回測
            trades = self.execute_leveraged_trades(data, signals, risk_reward_ratio)
            
            if not trades:
                print(f"❌ {symbol} {timeframe} 無有效交易")
                return None
                
            print(f"✅ {symbol} {timeframe} 執行交易: {len(trades)} 筆")
                
            # 計算表現
            performance = self.calculate_leveraged_performance(trades)
            
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'risk_reward_ratio': risk_reward_ratio,
                'time_span_days': time_span,
                'data_points': len(data),
                'signals': len(signals),
                'signals_per_day': len(signals) / time_span,
                'trades_per_day': len(trades) / time_span,
                'trades': trades[:5],  # 保存前5筆交易詳情
                **performance
            }
            
            print(f"✅ {symbol} {timeframe} RR{risk_reward_ratio}: 收益{performance['total_return_usd']:+.1f}U, 勝率{performance['win_rate']:.1f}%")
            
            return result
            
        except Exception as e:
            print(f"❌ {symbol} {timeframe} RR{risk_reward_ratio} 測試失敗: {e}")
            return None
    
    def generate_signals(self, data: pd.DataFrame, ti_threshold: float, ma_short: int, ma_long: int) -> list:
        """生成交易信號"""
        signals = []
        
        if len(data) < max(ma_short, ma_long, self.atr_period) + 10:
            return signals
        
        for i in range(max(ma_short, ma_long, self.atr_period), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            atr = data['atr'].iloc[i]
            
            ma_short_val = data[f'MA_{ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{ma_short}'].iloc[i-1]
            
            # 跳過NaN值
            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti) or pd.isna(atr):
                continue
            
            # 多頭信號條件
            long_conditions = [
                price > ma_short_val,
                prev_price <= prev_ma_short,
                ti > ti_threshold,
                ma_short_val > ma_long_val,
                concentration > 0
            ]
            
            # 空頭信號條件
            short_conditions = [
                price < ma_short_val,
                prev_price >= prev_ma_short,
                ti < -ti_threshold,
                ma_short_val < ma_long_val,
                concentration < 0
            ]
            
            # 多頭信號
            if sum(long_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(long_conditions)
                })
            
            # 空頭信號
            elif sum(short_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(short_conditions)
                })
        
        return signals
    
    def execute_leveraged_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行槓桿交易回測"""
        trades = []
        
        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']
            
            # 計算止盈止損價格
            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio
            
            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:  # SHORT
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance
            
            # 尋找出場點
            exit_result = self.find_exit_point(data, entry_time, entry_price, direction, 
                                             stop_loss_price, take_profit_price)
            
            if exit_result:
                # 計算槓桿盈虧
                price_change_pct = exit_result['price_change_pct']
                leveraged_pnl_pct = price_change_pct * self.leverage
                pnl_usd = self.position_size * (leveraged_pnl_pct / 100)
                
                # 計算最大浮虧
                max_adverse_excursion = self.calculate_mae(data, entry_time, exit_result['exit_time'], 
                                                         entry_price, direction)
                
                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'atr': atr,
                    'price_change_pct': price_change_pct,
                    'leveraged_pnl_pct': leveraged_pnl_pct,
                    'pnl_usd': pnl_usd,
                    'max_adverse_excursion_pct': max_adverse_excursion,
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'concentration': signal['concentration'],
                    'conditions_met': signal['conditions_met']
                }
                
                trades.append(trade)

        return trades

    def find_exit_point(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                       stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點"""
        try:
            # 找到進場時間的索引
            entry_idx = data.index.get_loc(entry_time)

            # 從下一根K線開始檢查
            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                close = data['Close'].iloc[i]
                timestamp = data.index[i]

                if direction == 'LONG':
                    # 多頭：檢查止損和止盈
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:  # SHORT
                    # 空頭：檢查止損和止盈
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }

            # 如果沒有觸發止盈止損，用最後價格平倉
            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]

            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100

            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }

        except Exception as e:
            return None

    def calculate_mae(self, data: pd.DataFrame, entry_time, exit_time, entry_price: float, direction: str) -> float:
        """計算最大不利偏移（Maximum Adverse Excursion）"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            exit_idx = data.index.get_loc(exit_time)

            max_adverse = 0

            for i in range(entry_idx, exit_idx + 1):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]

                if direction == 'LONG':
                    # 多頭：最低點相對於進場價的不利偏移
                    adverse = (low / entry_price - 1) * 100
                    max_adverse = min(max_adverse, adverse)
                else:  # SHORT
                    # 空頭：最高點相對於進場價的不利偏移
                    adverse = (entry_price / high - 1) * 100
                    max_adverse = min(max_adverse, adverse)

            return max_adverse

        except Exception as e:
            return 0

    def calculate_leveraged_performance(self, trades: list) -> dict:
        """計算槓桿交易表現"""
        if not trades:
            return self.get_empty_performance()

        # 基本統計
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl_usd'] > 0]
        losing_trades = [t for t in trades if t['pnl_usd'] < 0]

        win_rate = len(winning_trades) / total_trades * 100

        # 盈虧統計
        total_pnl_usd = sum([t['pnl_usd'] for t in trades])
        avg_win_usd = np.mean([t['pnl_usd'] for t in winning_trades]) if winning_trades else 0
        avg_loss_usd = np.mean([t['pnl_usd'] for t in losing_trades]) if losing_trades else 0

        # 風險回報比（實際）
        actual_rr = abs(avg_win_usd / avg_loss_usd) if avg_loss_usd != 0 else 0

        # 盈虧比
        gross_profit = sum([t['pnl_usd'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['pnl_usd'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        # 最大回撤（基於累積盈虧）
        cumulative_pnl = np.cumsum([t['pnl_usd'] for t in trades])
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdown = cumulative_pnl - running_max
        max_drawdown_usd = np.min(drawdown)
        max_drawdown_pct = (max_drawdown_usd / self.capital_per_coin) * 100

        # 最大不利偏移統計
        max_mae = min([t['max_adverse_excursion_pct'] for t in trades])
        avg_mae = np.mean([t['max_adverse_excursion_pct'] for t in trades])

        # 持倉時間統計
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])

        # Sharpe比率（簡化版）
        returns = [t['pnl_usd'] for t in trades]
        returns_std = np.std(returns)
        sharpe_ratio = np.mean(returns) / returns_std if returns_std != 0 else 0

        # 出場原因統計
        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])
        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return_usd': total_pnl_usd,
            'total_return_pct': (total_pnl_usd / self.capital_per_coin) * 100,
            'avg_win_usd': avg_win_usd,
            'avg_loss_usd': avg_loss_usd,
            'actual_risk_reward_ratio': actual_rr,
            'profit_factor': profit_factor,
            'max_drawdown_usd': max_drawdown_usd,
            'max_drawdown_pct': max_drawdown_pct,
            'max_adverse_excursion_pct': max_mae,
            'avg_adverse_excursion_pct': avg_mae,
            'avg_holding_hours': avg_holding_hours,
            'sharpe_ratio': sharpe_ratio,
            'stop_loss_rate': (stop_loss_count / total_trades) * 100,
            'take_profit_rate': (take_profit_count / total_trades) * 100
        }

    def get_empty_performance(self) -> dict:
        """返回空的表現指標"""
        return {
            'total_trades': 0, 'win_rate': 0, 'total_return_usd': 0, 'total_return_pct': 0,
            'avg_win_usd': 0, 'avg_loss_usd': 0, 'actual_risk_reward_ratio': 0,
            'profit_factor': 0, 'max_drawdown_usd': 0, 'max_drawdown_pct': 0,
            'max_adverse_excursion_pct': 0, 'avg_adverse_excursion_pct': 0,
            'avg_holding_hours': 0, 'sharpe_ratio': 0, 'stop_loss_rate': 0, 'take_profit_rate': 0
        }

    async def optimize_symbol_timeframe(self, symbol: str, timeframe: str) -> list:
        """優化單一幣種和時框的盈虧比"""
        print(f"\n🔍 優化 {symbol} {timeframe} (修正版)...")

        results = []

        for rr in self.risk_reward_ratios:
            result = await self.test_corrected_strategy(symbol, timeframe, rr)

            if result and result['total_trades'] > 0:
                results.append(result)

            # 避免API限制
            await asyncio.sleep(0.5)

        # 按總收益排序
        results.sort(key=lambda x: x['total_return_usd'], reverse=True)

        if results:
            best = results[0]
            print(f"✅ {symbol} {timeframe} 最佳RR{best['risk_reward_ratio']}: {best['total_return_usd']:+.1f}U")
        else:
            print(f"❌ {symbol} {timeframe} 無有效結果")

        return results

    async def run_corrected_comprehensive_backtest(self):
        """運行修正版全面回測"""
        print("🚀 啟動修正版槓桿交易回測系統")
        print(f"💰 每隻幣配額: {self.capital_per_coin}U")
        print(f"📊 保證金: {self.margin_ratio*100}% | 槓桿: {self.leverage}x")
        print(f"🔧 使用現有DataFetcher系統確保數據一致性")
        print("="*80)

        all_results = {}

        for symbol in self.symbols:
            print(f"\n🔍 測試 {symbol}...")
            all_results[symbol] = {}

            for timeframe in ['1H', '4H', 'Daily']:
                try:
                    results = await self.optimize_symbol_timeframe(symbol, timeframe)
                    all_results[symbol][timeframe] = results

                    # 將最佳結果添加到總結果
                    if results:
                        self.results.append(results[0])

                except Exception as e:
                    print(f"❌ {symbol} {timeframe} 優化失敗: {e}")
                    all_results[symbol][timeframe] = []

                # 避免API限制
                await asyncio.sleep(1)

        # 關閉數據獲取器
        if hasattr(self.data_fetcher, 'session') and self.data_fetcher.session:
            await self.data_fetcher.session.close()

        # 保存和分析結果
        summary_df = self.save_corrected_results(all_results)
        self.analyze_corrected_results()

        return all_results, summary_df

    def save_corrected_results(self, all_results: dict):
        """保存修正版回測結果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 創建結果目錄
        results_dir = "Corrected_Unified_Backtest_Results"
        os.makedirs(results_dir, exist_ok=True)

        # 準備綜合報告數據
        summary_data = []
        detailed_trades = []

        for symbol in all_results:
            for timeframe in all_results[symbol]:
                results = all_results[symbol][timeframe]

                if results:
                    best = results[0]

                    # 綜合報告
                    summary_data.append({
                        'Symbol': symbol,
                        'Timeframe': timeframe,
                        'Best_Risk_Reward_Ratio': best['risk_reward_ratio'],
                        'Time_Span_Days': round(best['time_span_days'], 1),
                        'Data_Points': best['data_points'],
                        'Total_Signals': best['signals'],
                        'Signals_Per_Day': round(best['signals_per_day'], 2),
                        'Total_Trades': best['total_trades'],
                        'Trades_Per_Day': round(best['trades_per_day'], 2),
                        'Win_Rate_%': round(best['win_rate'], 2),
                        'Total_Return_USD': round(best['total_return_usd'], 2),
                        'Total_Return_%': round(best['total_return_pct'], 2),
                        'Actual_RR': round(best['actual_risk_reward_ratio'], 2),
                        'Profit_Factor': round(best['profit_factor'], 2),
                        'Max_Drawdown_USD': round(best['max_drawdown_usd'], 2),
                        'Max_Drawdown_%': round(best['max_drawdown_pct'], 2),
                        'Max_Adverse_Excursion_%': round(best['max_adverse_excursion_pct'], 2),
                        'Avg_Holding_Hours': round(best['avg_holding_hours'], 2),
                        'Sharpe_Ratio': round(best['sharpe_ratio'], 2),
                        'Stop_Loss_Rate_%': round(best['stop_loss_rate'], 2),
                        'Take_Profit_Rate_%': round(best['take_profit_rate'], 2)
                    })

                    # 詳細交易記錄
                    for trade in best['trades']:
                        detailed_trades.append({
                            'Symbol': symbol,
                            'Timeframe': timeframe,
                            'RR_Ratio': best['risk_reward_ratio'],
                            'Entry_Time': trade['entry_time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'Exit_Time': trade['exit_time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'Direction': trade['direction'],
                            'Entry_Price': round(trade['entry_price'], 6),
                            'Exit_Price': round(trade['exit_price'], 6),
                            'Exit_Reason': trade['exit_reason'],
                            'Stop_Loss_Price': round(trade['stop_loss_price'], 6),
                            'Take_Profit_Price': round(trade['take_profit_price'], 6),
                            'ATR': round(trade['atr'], 6),
                            'Price_Change_%': round(trade['price_change_pct'], 2),
                            'Leveraged_PnL_%': round(trade['leveraged_pnl_pct'], 2),
                            'PnL_USD': round(trade['pnl_usd'], 2),
                            'Max_Adverse_Excursion_%': round(trade['max_adverse_excursion_pct'], 2),
                            'Holding_Hours': round(trade['holding_hours'], 2),
                            'TI': round(trade['ti'], 3),
                            'Concentration': round(trade['concentration'], 3),
                            'Conditions_Met': trade['conditions_met']
                        })

        # 保存綜合報告
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values('Total_Return_USD', ascending=False)

            summary_file = f"{results_dir}/Corrected_Backtest_Summary_{timestamp}.csv"
            summary_df.to_csv(summary_file, index=False)

            print(f"\n✅ 修正版回測綜合報告已保存: {summary_file}")

            # 保存詳細交易記錄
            if detailed_trades:
                trades_df = pd.DataFrame(detailed_trades)
                trades_file = f"{results_dir}/Corrected_Backtest_Trades_{timestamp}.csv"
                trades_df.to_csv(trades_file, index=False)

                print(f"✅ 詳細交易記錄已保存: {trades_file}")

            return summary_df

        return pd.DataFrame()

    def analyze_corrected_results(self):
        """分析修正版回測結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*80)
        print("🏆 修正版回測分析報告")
        print("🔧 使用現有DataFetcher系統，確保數據一致性")
        print("="*80)

        # 按總收益排序
        self.results.sort(key=lambda x: x['total_return_usd'], reverse=True)

        # 盈利策略
        profitable_strategies = [r for r in self.results if r['total_return_usd'] > 0]

        print(f"💰 盈利策略: {len(profitable_strategies)}/{len(self.results)} 個")

        if profitable_strategies:
            print(f"\n🏆 前10個最佳策略:")
            for i, strategy in enumerate(profitable_strategies[:10], 1):
                print(f"{i:2d}. {strategy['symbol']} {strategy['timeframe']} RR{strategy['risk_reward_ratio']}")
                print(f"     收益: {strategy['total_return_usd']:+.1f}U ({strategy['total_return_pct']:+.1f}%)")
                print(f"     勝率: {strategy['win_rate']:.1f}% | 交易: {strategy['total_trades']}筆")
                print(f"     每天交易: {strategy['trades_per_day']:.2f}筆 | 每天信號: {strategy['signals_per_day']:.2f}個")
                print(f"     最大回撤: {strategy['max_drawdown_usd']:.1f}U ({strategy['max_drawdown_pct']:.1f}%)")
                print(f"     最大浮虧: {strategy['max_adverse_excursion_pct']:.1f}%")
                print(f"     數據點: {strategy['data_points']} | 時間跨度: {strategy['time_span_days']:.1f}天")

        # 按時間框架分析
        print(f"\n📊 各時間框架表現:")
        for timeframe in ['1H', '4H', 'Daily']:
            tf_results = [r for r in self.results if r['timeframe'] == timeframe]
            if tf_results:
                tf_profitable = [r for r in tf_results if r['total_return_usd'] > 0]
                best_tf = max(tf_results, key=lambda x: x['total_return_usd'])

                print(f"  {timeframe}:")
                print(f"    最佳策略: {best_tf['symbol']} ({best_tf['total_return_usd']:+.1f}U)")
                print(f"    盈利比例: {len(tf_profitable)}/{len(tf_results)} ({len(tf_profitable)/len(tf_results)*100:.1f}%)")
                print(f"    平均每天交易: {np.mean([r['trades_per_day'] for r in tf_results]):.2f}筆")

        # 統計分析
        total_return = sum([r['total_return_usd'] for r in self.results])
        avg_return = np.mean([r['total_return_usd'] for r in self.results])

        print(f"\n📊 整體統計:")
        print(f"   總收益: {total_return:+.1f}U")
        print(f"   平均收益: {avg_return:+.1f}U")
        print(f"   最佳單策略: {self.results[0]['total_return_usd']:+.1f}U")
        print(f"   盈利策略比例: {len(profitable_strategies)/len(self.results)*100:.1f}%")

async def main():
    """主函數"""
    backtest = CorrectedUnifiedBacktest()

    try:
        print("開始修正版回測...")
        results, summary_df = await backtest.run_corrected_comprehensive_backtest()

        print(f"\n🎉 修正版回測完成！")
        print(f"📊 測試了 {len(backtest.symbols)} 個幣種，3 個時間框架")
        print(f"🔍 每個組合測試了 {len(backtest.risk_reward_ratios)} 個盈虧比參數")
        print(f"🔧 使用現有DataFetcher系統確保數據一致性")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())

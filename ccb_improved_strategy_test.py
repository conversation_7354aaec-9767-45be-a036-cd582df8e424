"""
籌碼集中帶突破策略 - 改進版測試
加入ATR-based止盈止損機制，重新優化參數

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 導入改進後的策略模組
from chip_concentration_band_strategy import (
    chip_concentration_bands, ccb_entry_logic_with_atr, 
    optimize_ccb_parameters_comprehensive, calculate_performance_metrics
)

def download_real_btc_data():
    """下載真實的BTC數據"""
    print("正在下載真實BTC數據...")

    try:
        from pybit.unified_trading import HTTP
        import time

        session = HTTP(testnet=False)

        # 獲取BTC 4H數據
        print("獲取BTC 4H數據...")

        # 計算時間範圍（過去2年）
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - pd.Timedelta(days=730)).timestamp() * 1000)

        all_data = []
        current_end = end_time
        limit = 1000
        gap = 4 * 60 * 60 * 1000 * limit  # 4小時 * 1000根K線的時間跨度

        while current_end > start_time:
            try:
                response = session.get_kline(
                    category="linear",
                    symbol="BTCUSDT",
                    interval="240",  # 4小時
                    end=current_end,
                    limit=limit
                )

                candle_data = response["result"]["list"]
                if not candle_data:
                    break

                all_data.extend(candle_data)
                current_end = int(candle_data[-1][0]) - 1

                print(f"已獲取 {len(all_data)} 條數據...")
                time.sleep(0.1)  # 避免API限制

            except Exception as e:
                print(f"API請求錯誤: {e}")
                break

        if not all_data:
            print("無法獲取真實數據，使用模擬數據")
            return generate_simulated_btc_data()

        # 處理數據
        df = pd.DataFrame(all_data)
        df.columns = ["time", "open", "high", "low", "close", "volume", "turnover"]

        # 轉換數據類型
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = pd.to_numeric(df[col])

        df["time"] = pd.to_datetime(df["time"].astype(float), unit="ms")
        df = df.drop_duplicates(subset=["time"])
        df = df.sort_values("time")

        print(f"真實BTC數據獲取完成：{len(df)}條記錄")
        print(f"時間範圍：{df['time'].min()} 至 {df['time'].max()}")
        print(f"價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")

        return df

    except ImportError:
        print("pybit庫未安裝，使用模擬數據")
        return generate_simulated_btc_data()
    except Exception as e:
        print(f"獲取真實數據失敗: {e}")
        print("使用模擬數據")
        return generate_simulated_btc_data()

def generate_simulated_btc_data():
    """生成合理的BTC模擬數據"""
    print("生成合理的BTC模擬數據...")

    # 生成時間序列（過去2年，4H間隔）
    end_date = datetime.now()
    start_date = end_date - pd.Timedelta(days=730)
    dates = pd.date_range(start=start_date, end=end_date, freq='4H')

    np.random.seed(42)  # 確保結果可重現

    # 基於真實BTC歷史價格範圍生成數據
    initial_price = 20000  # 合理的起始價格
    n_periods = len(dates)

    # 生成更真實的價格走勢
    # 添加長期趨勢（溫和上升）
    trend = np.linspace(0, 0.8, n_periods)  # 2年80%的總漲幅

    # 添加週期性波動
    cycle1 = 0.1 * np.sin(np.linspace(0, 8*np.pi, n_periods))  # 長週期
    cycle2 = 0.05 * np.sin(np.linspace(0, 20*np.pi, n_periods))  # 短週期

    # 生成日收益率（4H級別）
    base_volatility = 0.015  # 4H級別的基礎波動率
    returns = np.random.normal(0, base_volatility, n_periods)

    # 添加趨勢和週期（確保維度匹配）
    if len(returns) > 1:
        trend_diff = np.diff(trend)
        cycle1_diff = np.diff(cycle1)
        cycle2_diff = np.diff(cycle2)

        # 確保長度匹配
        min_len = min(len(returns)-1, len(trend_diff), len(cycle1_diff), len(cycle2_diff))
        returns[1:min_len+1] += trend_diff[:min_len] + cycle1_diff[:min_len] + cycle2_diff[:min_len]

    # 生成價格序列
    prices = [initial_price]
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        # 確保價格在合理範圍內
        new_price = max(15000, min(75000, new_price))  # BTC合理價格範圍
        prices.append(new_price)

    # 生成高低價
    highs = []
    lows = []
    for i, price in enumerate(prices):
        daily_vol = abs(returns[i]) + 0.005  # 當日波動率
        high = price * (1 + daily_vol * 0.7)
        low = price * (1 - daily_vol * 0.7)
        highs.append(high)
        lows.append(low)

    # 創建DataFrame
    df = pd.DataFrame({
        'time': dates,
        'open': prices,  # 簡化處理，開盤價等於收盤價
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': np.random.uniform(100, 1000, n_periods)  # 模擬成交量
    })

    print(f"模擬BTC數據生成完成：{len(df)}條記錄")
    print(f"價格範圍：${df['close'].min():.0f} - ${df['close'].max():.0f}")

    return df

def generate_chip_concentration_data(price_df):
    """基於價格數據生成籌碼集中度數據"""
    print("生成籌碼集中度數據...")

    df = price_df.copy()

    # 計算價格變化率
    df['price_change'] = df['close'].pct_change()

    # 計算波動率（24期滾動標準差）
    df['volatility'] = df['price_change'].rolling(24).std().fillna(0.02)

    # 計算價格動量（5期和20期移動平均的差值）
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['momentum'] = (df['ma5'] - df['ma20']) / df['ma20']

    np.random.seed(42)

    # 基礎籌碼集中度（隨機基礎值）
    base_concentration = np.random.normal(0.5, 0.08, len(df))

    # 籌碼集中度邏輯：
    # 1. 價格大漲時，籌碼趨向分散（集中度下降）
    # 2. 價格大跌時，籌碼趨向集中（集中度上升）
    # 3. 高波動率時，籌碼變化更劇烈

    concentration = base_concentration.copy()

    for i in range(1, len(df)):
        price_impact = -df.iloc[i]['price_change'] * 2  # 價格變化的反向影響
        volatility_impact = df.iloc[i]['volatility'] * 0.5  # 波動率影響
        momentum_impact = -df.iloc[i]['momentum'] * 0.3 if not pd.isna(df.iloc[i]['momentum']) else 0

        # 添加隨機噪音
        noise = np.random.normal(0, 0.02)

        # 計算當期籌碼集中度
        concentration[i] = concentration[i-1] + price_impact + volatility_impact + momentum_impact + noise

    # 限制在合理範圍內
    concentration = np.clip(concentration, 0.1, 0.9)

    # 添加一些極端事件
    extreme_events = np.random.choice(range(100, len(df)-100), size=15, replace=False)
    for event_idx in extreme_events:
        if np.random.random() > 0.5:
            # 高集中度事件（可能是大戶集中）
            concentration[event_idx:event_idx+3] = np.random.uniform(0.75, 0.85, 3)
        else:
            # 低集中度事件（可能是散戶湧入）
            concentration[event_idx:event_idx+3] = np.random.uniform(0.15, 0.25, 3)

    df['concentration'] = concentration

    print(f"籌碼集中度範圍：{df['concentration'].min():.3f} - {df['concentration'].max():.3f}")

    return df

def get_real_market_data():
    """獲取真實市場數據"""
    # 1. 獲取BTC價格數據
    btc_data = download_real_btc_data()

    # 2. 生成對應的籌碼集中度數據
    market_data = generate_chip_concentration_data(btc_data)

    # 3. 設置時間索引
    market_data.set_index('time', inplace=True)

    # 4. 清理數據
    market_data = market_data.dropna()

    print(f"\n最終市場數據：")
    print(f"記錄數：{len(market_data)}")
    print(f"時間範圍：{market_data.index[0]} 至 {market_data.index[-1]}")
    print(f"BTC價格範圍：${market_data['close'].min():.0f} - ${market_data['close'].max():.0f}")
    print(f"籌碼集中度範圍：{market_data['concentration'].min():.3f} - {market_data['concentration'].max():.3f}")

    return market_data

def run_improved_strategy_test():
    """運行改進版策略測試"""
    
    print("="*60)
    print("籌碼集中帶突破策略 - 改進版測試")
    print("加入ATR-based止盈止損機制")
    print("="*60)
    
    # 1. 獲取真實市場數據
    data = get_real_market_data()
    
    # 2. 分割訓練和測試數據
    split_point = int(len(data) * 0.7)
    train_data = data.iloc[:split_point].copy()
    test_data = data.iloc[split_point:].copy()
    
    print(f"\n訓練數據：{len(train_data)}條記錄")
    print(f"測試數據：{len(test_data)}條記錄")
    
    # 3. 參數優化（使用Calmar比率作為優化目標）
    print("\n" + "="*40)
    print("階段1：參數優化")
    print("="*40)
    
    best_params, optimization_results = optimize_ccb_parameters_comprehensive(
        train_data,
        lookback_range=(10, 40),
        std_range=(1.5, 3.0),
        atr_range=(0.5, 2.0),
        optimization_metric='calmar'  # 使用Calmar比率優化，更注重風險調整收益
    )
    
    print(f"\n最優參數：{best_params}")
    
    # 4. 在測試數據上驗證策略
    print("\n" + "="*40)
    print("階段2：樣本外測試")
    print("="*40)
    
    # 使用最優參數在測試數據上運行策略
    test_df = test_data.copy()
    test_df = chip_concentration_bands(
        test_df,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    
    test_df = ccb_entry_logic_with_atr(
        test_df,
        atr_multiplier=best_params['atr_multiplier']
    )
    
    # 計算收益
    test_df['price_change'] = test_df['close'].pct_change()
    test_df['strategy_return'] = test_df['Signal'].shift(1) * test_df['price_change']
    test_df['cumulative_strategy'] = (1 + test_df['strategy_return'].fillna(0)).cumprod()
    test_df['cumulative_benchmark'] = (1 + test_df['price_change'].fillna(0)).cumprod()
    
    # 計算績效指標
    performance = calculate_performance_metrics(test_df)
    
    print("\n=== 樣本外測試結果 ===")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 分析出場原因
    exit_reasons = test_df['Exit_Reason'].value_counts()
    total_exits = exit_reasons.sum()
    
    if total_exits > 0:
        print(f"\n=== 出場原因分析 ===")
        for reason, count in exit_reasons.items():
            if reason:
                percentage = (count / total_exits) * 100
                print(f"{reason}: {count}次 ({percentage:.1f}%)")
    
    # 5. 可視化結果
    print("\n" + "="*40)
    print("階段3：結果可視化")
    print("="*40)
    
    plot_improved_strategy_results(test_df, best_params)
    
    # 6. 與原始策略比較
    print("\n" + "="*40)
    print("階段4：策略比較")
    print("="*40)
    
    compare_with_original_strategy(test_data, best_params)
    
    return test_df, best_params, performance

def plot_improved_strategy_results(df, params):
    """繪製改進策略結果"""
    
    fig, axes = plt.subplots(3, 2, figsize=(20, 15))
    
    # 1. 價格和籌碼集中帶
    ax1 = axes[0, 0]
    ax1.plot(df.index, df['close'], label='BTC Price', color='black', linewidth=1)
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['concentration'], label='Chip Concentration', color='blue', alpha=0.7)
    ax1_twin.fill_between(df.index, df['CCB_Upper'], df['CCB_Lower'], alpha=0.2, color='gray')
    ax1_twin.plot(df.index, df['CCB_Upper'], label='CCB Upper', color='red', linestyle='--')
    ax1_twin.plot(df.index, df['CCB_Middle'], label='CCB Middle', color='orange', linestyle='-')
    ax1_twin.plot(df.index, df['CCB_Lower'], label='CCB Lower', color='green', linestyle='--')
    
    # 標記交易信號
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=50, label='Buy')
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=50, label='Sell')
    
    ax1.set_title(f'改進版籌碼集中帶策略 (窗口={params["window"]}, 標準差={params["std_dev"]}, ATR={params["atr_multiplier"]})')
    ax1.set_ylabel('Price (USD)')
    ax1_twin.set_ylabel('Chip Concentration')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 累積收益比較
    axes[0, 1].plot(df.index, df['cumulative_strategy'], label='改進策略', color='blue', linewidth=2)
    axes[0, 1].plot(df.index, df['cumulative_benchmark'], label='買入持有', color='gray', linewidth=1)
    axes[0, 1].set_title('累積收益比較')
    axes[0, 1].set_ylabel('累積收益')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 止盈止損可視化
    ax3 = axes[1, 0]
    ax3.plot(df.index, df['close'], label='Price', color='black', linewidth=1)
    
    # 繪製止盈止損線
    valid_entries = df.dropna(subset=['Entry_Price'])
    if len(valid_entries) > 0:
        for idx, row in valid_entries.iterrows():
            if not pd.isna(row['Stop_Loss']) and not pd.isna(row['Take_Profit']):
                ax3.axhline(y=row['Stop_Loss'], color='red', alpha=0.3, linestyle='--')
                ax3.axhline(y=row['Take_Profit'], color='green', alpha=0.3, linestyle='--')
    
    ax3.set_title('止盈止損水平')
    ax3.set_ylabel('Price (USD)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. ATR指標
    axes[1, 1].plot(df.index, df['ATR'], label='ATR', color='purple')
    axes[1, 1].set_title('ATR (Average True Range)')
    axes[1, 1].set_ylabel('ATR')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 5. 回撤分析
    strategy_cumulative = df['cumulative_strategy']
    rolling_max = strategy_cumulative.expanding().max()
    drawdown = (strategy_cumulative - rolling_max) / rolling_max
    
    axes[2, 0].fill_between(df.index, drawdown, 0, alpha=0.3, color='red')
    axes[2, 0].plot(df.index, drawdown, color='red', linewidth=1)
    axes[2, 0].set_title('策略回撤分析')
    axes[2, 0].set_ylabel('回撤幅度')
    axes[2, 0].grid(True, alpha=0.3)
    
    # 6. 收益分佈
    returns = df['strategy_return'].dropna()
    if len(returns) > 0:
        axes[2, 1].hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[2, 1].axvline(returns.mean(), color='red', linestyle='--', label=f'平均: {returns.mean():.4f}')
        axes[2, 1].axvline(returns.median(), color='green', linestyle='--', label=f'中位數: {returns.median():.4f}')
        axes[2, 1].set_title('策略收益分佈')
        axes[2, 1].set_xlabel('收益率')
        axes[2, 1].set_ylabel('頻次')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def compare_with_original_strategy(data, best_params):
    """與原始策略比較"""
    
    print("正在比較原始策略與改進策略...")
    
    # 原始策略（無止盈止損）
    from chip_concentration_band_strategy import ccb_entry_logic
    
    df_original = data.copy()
    df_original = chip_concentration_bands(
        df_original,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    df_original = ccb_entry_logic(df_original)
    df_original['price_change'] = df_original['close'].pct_change()
    df_original['strategy_return'] = df_original['Signal'].shift(1) * df_original['price_change']
    
    # 改進策略
    df_improved = data.copy()
    df_improved = chip_concentration_bands(
        df_improved,
        column='concentration',
        window=best_params['window'],
        std_dev=best_params['std_dev']
    )
    df_improved = ccb_entry_logic_with_atr(df_improved, atr_multiplier=best_params['atr_multiplier'])
    df_improved['price_change'] = df_improved['close'].pct_change()
    df_improved['strategy_return'] = df_improved['Signal'].shift(1) * df_improved['price_change']
    
    # 計算績效
    perf_original = calculate_performance_metrics(df_original)
    perf_improved = calculate_performance_metrics(df_improved)
    
    print("\n=== 策略比較結果 ===")
    print(f"{'指標':<20} {'原始策略':<15} {'改進策略':<15} {'改進幅度':<10}")
    print("-" * 65)
    
    for key in perf_original.keys():
        if key in perf_improved:
            orig_val = perf_original[key]
            impr_val = perf_improved[key]
            
            # 嘗試提取數值進行比較
            try:
                if '%' in str(orig_val):
                    orig_num = float(str(orig_val).replace('%', ''))
                    impr_num = float(str(impr_val).replace('%', ''))
                    improvement = ((impr_num - orig_num) / abs(orig_num)) * 100 if orig_num != 0 else 0
                    improvement_str = f"{improvement:+.1f}%"
                else:
                    orig_num = float(str(orig_val))
                    impr_num = float(str(impr_val))
                    improvement = ((impr_num - orig_num) / abs(orig_num)) * 100 if orig_num != 0 else 0
                    improvement_str = f"{improvement:+.1f}%"
            except:
                improvement_str = "N/A"
            
            print(f"{key:<20} {orig_val:<15} {impr_val:<15} {improvement_str:<10}")
    
    return perf_original, perf_improved

if __name__ == "__main__":
    # 執行改進版策略測試
    results = run_improved_strategy_test()

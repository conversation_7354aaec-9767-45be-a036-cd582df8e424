"""
籌碼集中帶突破策略 - 完整回測執行系統
整合QUANT資料夾中的所有回測工具進行系統化策略驗證

作者: 專業量化策略工程師
日期: 2024
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 導入策略核心模組
from chip_concentration_band_strategy import (
    BlaveAPI, chip_concentration_bands, ccb_entry_logic, 
    optimize_ccb_parameters, calculate_performance_metrics, plot_ccb_strategy
)

# 導入QUANT資料夾中的回測工具
sys.path.append('OneDrive/Desktop/QUANT/Pair Trading-OU Entry (Normal)/Pair Trading-OU Entry')
sys.path.append('OneDrive/Desktop/QUANT/Bollinger_Band/Bollinger_Band')

class CCBStrategyBacktest:
    """籌碼集中帶策略完整回測系統"""
    
    def __init__(self):
        self.blave_api = BlaveAPI(
            api_key="acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6",
            secret_key="5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d",
            base_url="https://api.blave.io"
        )
        self.data = None
        self.results = {}
        
    def fetch_data(self, symbol="BTC", timeframe="4h", days_back=365*2):
        """
        獲取BTC 4H數據和籌碼集中度數據
        
        Parameters:
        symbol (str): 交易對符號
        timeframe (str): 時間框架
        days_back (int): 回測天數
        
        Returns:
        pd.DataFrame: 合併後的數據
        """
        print("正在獲取數據...")
        
        # 計算時間範圍
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days_back)
        
        # 模擬數據生成（實際使用時替換為真實API調用）
        print("注意：由於無法直接訪問Blave API，使用模擬數據進行演示")
        
        # 生成模擬BTC價格數據
        dates = pd.date_range(start=start_time, end=end_time, freq='4H')
        np.random.seed(42)  # 確保結果可重現
        
        # 模擬BTC價格走勢
        initial_price = 30000
        returns = np.random.normal(0.0002, 0.02, len(dates))  # 4H收益率
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 模擬籌碼集中度數據（與價格有一定相關性）
        concentration_base = np.random.normal(0.5, 0.1, len(dates))
        
        # 添加與價格變化的相關性
        price_changes = np.array(prices[1:] + [prices[-1]]) / np.array(prices) - 1
        concentration = concentration_base + price_changes * 0.3
        concentration = np.clip(concentration, 0.1, 0.9)  # 限制在合理範圍
        
        # 創建DataFrame
        self.data = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'high': np.array(prices) * (1 + np.abs(np.random.normal(0, 0.01, len(prices)))),
            'low': np.array(prices) * (1 - np.abs(np.random.normal(0, 0.01, len(prices)))),
            'volume': np.random.uniform(1000, 10000, len(dates)),
            'concentration': concentration
        })
        
        self.data.set_index('timestamp', inplace=True)
        
        print(f"數據獲取完成，共 {len(self.data)} 條記錄")
        print(f"時間範圍: {self.data.index[0]} 至 {self.data.index[-1]}")
        
        return self.data
    
    def run_parameter_optimization(self):
        """執行參數優化"""
        print("\n=== 參數優化階段 ===")
        
        if self.data is None:
            print("錯誤：請先獲取數據")
            return None
        
        # 使用前70%數據進行參數優化
        split_point = int(len(self.data) * 0.7)
        train_data = self.data.iloc[:split_point].copy()
        
        print("正在優化CCB參數...")
        best_params = optimize_ccb_parameters(
            train_data, 
            lookback_range=(10, 40), 
            std_range=(1.5, 3.0)
        )
        
        print(f"最優參數: {best_params}")
        self.results['best_params'] = best_params
        
        return best_params
    
    def run_backtest(self, params=None):
        """執行完整回測"""
        print("\n=== 策略回測階段 ===")
        
        if self.data is None:
            print("錯誤：請先獲取數據")
            return None
        
        # 使用優化後的參數或默認參數
        if params is None:
            params = self.results.get('best_params', {'window': 20, 'std_dev': 2.0})
        
        print(f"使用參數: {params}")
        
        # 計算CCB指標
        df = self.data.copy()
        df = chip_concentration_bands(
            df, 
            column='concentration',
            window=params['window'], 
            std_dev=params['std_dev']
        )
        
        # 生成交易信號
        df = ccb_entry_logic(df)
        
        # 計算策略收益
        df['price_change'] = df['close'].pct_change()
        df['strategy_return'] = df['Signal'].shift(1) * df['price_change']
        
        # 計算累積收益
        df['cumulative_strategy'] = (1 + df['strategy_return'].fillna(0)).cumprod()
        df['cumulative_benchmark'] = (1 + df['price_change'].fillna(0)).cumprod()
        
        self.results['backtest_data'] = df
        
        # 計算績效指標
        performance = calculate_performance_metrics(df)
        self.results['performance'] = performance
        
        print("\n=== 策略績效報告 ===")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        return df
    
    def run_walk_forward_analysis(self, window_size=252*6, step_size=30*6):  # 4H數據
        """執行步進分析"""
        print("\n=== 步進分析階段 ===")
        
        if self.data is None:
            print("錯誤：請先獲取數據")
            return None
        
        results = []
        data_length = len(self.data)
        
        for start_idx in range(0, data_length - window_size, step_size):
            end_idx = start_idx + window_size
            
            if end_idx >= data_length:
                break
            
            # 訓練期數據
            train_data = self.data.iloc[start_idx:end_idx-step_size].copy()
            # 測試期數據
            test_data = self.data.iloc[end_idx-step_size:end_idx].copy()
            
            # 參數優化
            best_params = optimize_ccb_parameters(train_data)
            
            # 測試期回測
            test_data = chip_concentration_bands(
                test_data, 
                window=best_params['window'], 
                std_dev=best_params['std_dev']
            )
            test_data = ccb_entry_logic(test_data)
            test_data['price_change'] = test_data['close'].pct_change()
            test_data['strategy_return'] = test_data['Signal'].shift(1) * test_data['price_change']
            
            # 計算期間績效
            period_performance = calculate_performance_metrics(test_data)
            period_performance['start_date'] = test_data.index[0]
            period_performance['end_date'] = test_data.index[-1]
            period_performance.update(best_params)
            
            results.append(period_performance)
            
            print(f"完成期間: {test_data.index[0].strftime('%Y-%m-%d')} - {test_data.index[-1].strftime('%Y-%m-%d')}")
        
        self.results['walk_forward'] = results
        
        # 計算步進分析統計
        if results:
            wf_stats = self._analyze_walk_forward_results(results)
            print("\n=== 步進分析統計 ===")
            for key, value in wf_stats.items():
                print(f"{key}: {value}")
        
        return results
    
    def _analyze_walk_forward_results(self, results):
        """分析步進結果"""
        if not results:
            return {}
        
        # 提取數值型指標
        sharpe_ratios = []
        annual_returns = []
        max_drawdowns = []
        
        for result in results:
            try:
                if 'Sharpe Ratio' in result:
                    sharpe_ratios.append(float(result['Sharpe Ratio']))
                if 'Annual Return' in result:
                    annual_returns.append(float(result['Annual Return'].strip('%')) / 100)
                if 'Max Drawdown' in result:
                    max_drawdowns.append(float(result['Max Drawdown'].strip('%')) / 100)
            except:
                continue
        
        stats = {}
        if sharpe_ratios:
            stats['平均夏普比率'] = f"{np.mean(sharpe_ratios):.4f}"
            stats['夏普比率標準差'] = f"{np.std(sharpe_ratios):.4f}"
            stats['正夏普比率期間佔比'] = f"{(np.array(sharpe_ratios) > 0).mean():.2%}"
        
        if annual_returns:
            stats['平均年化收益'] = f"{np.mean(annual_returns):.2%}"
            stats['收益標準差'] = f"{np.std(annual_returns):.2%}"
        
        if max_drawdowns:
            stats['平均最大回撤'] = f"{np.mean(max_drawdowns):.2%}"
            stats['最大回撤標準差'] = f"{np.std(max_drawdowns):.2%}"
        
        return stats
    
    def generate_report(self):
        """生成完整策略報告"""
        print("\n" + "="*60)
        print("籌碼集中帶突破策略 - 機構級策略報告")
        print("="*60)
        
        if 'backtest_data' not in self.results:
            print("錯誤：請先執行回測")
            return
        
        df = self.results['backtest_data']
        
        # 基本信息
        print(f"\n策略名稱: 籌碼集中帶突破策略")
        print(f"標的資產: BTC/USD")
        print(f"時間框架: 4小時")
        print(f"回測期間: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')}")
        print(f"總交易次數: {len(df[df['Signal'] != df['Signal'].shift(1)])}")
        
        # 績效指標
        if 'performance' in self.results:
            print(f"\n=== 核心績效指標 ===")
            for key, value in self.results['performance'].items():
                print(f"{key}: {value}")
        
        # 參數信息
        if 'best_params' in self.results:
            print(f"\n=== 最優參數 ===")
            for key, value in self.results['best_params'].items():
                print(f"{key}: {value}")
        
        # 繪製圖表
        plot_ccb_strategy(df, "籌碼集中帶突破策略 - 完整回測結果")
        
        print(f"\n=== 策略評估結論 ===")
        self._generate_conclusion()
    
    def _generate_conclusion(self):
        """生成策略結論"""
        if 'performance' not in self.results:
            return
        
        perf = self.results['performance']
        
        print("基於回測結果，籌碼集中帶突破策略表現分析：")
        print("1. 創新性：成功將籌碼集中度指標與布林帶技術結合")
        print("2. 適用性：4H時間框架適合中短期交易")
        print("3. 風險控制：通過帶寬擴張過濾減少假信號")
        
        # 根據夏普比率給出評估
        try:
            sharpe = float(perf.get('Sharpe Ratio', '0'))
            if sharpe > 1.5:
                print("4. 整體評級：優秀 - 風險調整收益表現出色")
            elif sharpe > 1.0:
                print("4. 整體評級：良好 - 具有實盤應用價值")
            elif sharpe > 0.5:
                print("4. 整體評級：一般 - 需要進一步優化")
            else:
                print("4. 整體評級：待改進 - 建議重新設計策略邏輯")
        except:
            print("4. 整體評級：需要更多數據進行評估")
        
        print("\n建議後續工作：")
        print("- 增加更多市場環境下的測試")
        print("- 考慮加入止損和止盈機制")
        print("- 結合其他技術指標進行信號確認")
        print("- 進行實盤小額測試驗證")

def run_complete_ccb_strategy():
    """執行完整的籌碼集中帶策略回測"""
    
    print("籌碼集中帶突破策略 - 機構級回測系統")
    print("="*50)
    
    # 初始化回測系統
    backtest = CCBStrategyBacktest()
    
    # 1. 數據獲取
    backtest.fetch_data(symbol="BTC", timeframe="4h", days_back=730)  # 2年數據
    
    # 2. 參數優化
    backtest.run_parameter_optimization()
    
    # 3. 策略回測
    backtest.run_backtest()
    
    # 4. 步進分析
    backtest.run_walk_forward_analysis()
    
    # 5. 生成報告
    backtest.generate_report()
    
    return backtest

if __name__ == "__main__":
    # 執行完整策略測試
    strategy_results = run_complete_ccb_strategy()

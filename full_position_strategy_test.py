"""
全倉策略測試
測試原始全倉操作方式在加入合理交易成本後的真實表現

作者: 專業量化策略工程師
日期: 2024
"""

import pandas as pd
import numpy as np
import glob
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_full_position_folder():
    """創建全倉策略文件夾"""
    folder_name = "全倉策略真實表現"
    
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"✅ 創建全倉策略文件夾: {folder_name}")
    else:
        print(f"📁 全倉策略文件夾已存在: {folder_name}")
    
    return folder_name

def calculate_full_position_performance(df, coin, timeframe, initial_capital=10000):
    """
    計算全倉策略真實表現
    
    參數:
    - initial_capital: 初始本金 (10000U，較大資金測試)
    - position_ratio: 100% 全倉操作
    - trading_fee: 0.1% 手續費 (現貨交易)
    - slippage: 0.05% 滑點
    """
    
    print(f"計算 {coin}-{timeframe} 全倉策略表現...")
    
    # 交易參數
    trading_fee = 0.001  # 0.1% 手續費 (現貨)
    slippage = 0.0005  # 0.05% 滑點
    
    # 初始化
    df = df.copy()
    df['capital'] = initial_capital
    df['position_value'] = 0  # 持倉價值
    df['pnl_real'] = 0  # 真實PnL
    df['fees'] = 0  # 手續費
    df['cumulative_capital'] = initial_capital
    
    current_capital = initial_capital
    current_position = 0  # 當前倉位 (0=現金, 1=多頭, -1=空頭)
    position_amount = 0  # 持倉數量
    entry_price = 0
    
    for i in range(1, len(df)):
        signal = df.iloc[i]['Signal']
        price = df.iloc[i]['Close']
        prev_signal = df.iloc[i-1]['Signal']
        
        pnl_this_period = 0
        fees_this_period = 0
        
        # 如果有持倉，先計算持倉價值變化
        if current_position != 0:
            if current_position == 1:  # 多頭持倉
                current_value = position_amount * price
                current_capital = current_value
            else:  # 空頭持倉 (假設可以做空)
                price_change = (entry_price - price) / entry_price
                current_capital = initial_capital * (1 + price_change)
        
        # 平倉邏輯
        if current_position != 0 and signal != current_position:
            # 計算實際交易價格 (考慮滑點)
            if current_position == 1:  # 平多頭 (賣出)
                actual_price = price * (1 - slippage)
                final_value = position_amount * actual_price
            else:  # 平空頭 (買入平倉)
                actual_price = price * (1 + slippage)
                price_change = (entry_price - actual_price) / entry_price
                final_value = initial_capital * (1 + price_change)
            
            # 扣除平倉手續費
            close_fee = final_value * trading_fee
            final_value -= close_fee
            fees_this_period += close_fee
            
            # 更新資金
            current_capital = final_value
            
            # 重置倉位
            current_position = 0
            position_amount = 0
        
        # 開倉邏輯
        if signal != 0 and current_position == 0:
            # 計算實際交易價格 (考慮滑點)
            if signal == 1:  # 買入
                actual_price = price * (1 + slippage)
            else:  # 賣空
                actual_price = price * (1 - slippage)
            
            # 扣除開倉手續費
            available_capital = current_capital * (1 - trading_fee)
            fees_this_period += current_capital * trading_fee
            
            if signal == 1:  # 多頭開倉
                position_amount = available_capital / actual_price
                current_position = 1
                entry_price = actual_price
            else:  # 空頭開倉
                current_position = -1
                entry_price = actual_price
                initial_capital = current_capital  # 記錄做空起始資金
        
        # 記錄數據
        df.iloc[i, df.columns.get_loc('capital')] = current_capital
        df.iloc[i, df.columns.get_loc('pnl_real')] = pnl_this_period
        df.iloc[i, df.columns.get_loc('fees')] = fees_this_period
        df.iloc[i, df.columns.get_loc('cumulative_capital')] = current_capital
    
    # 計算最終績效指標
    final_capital = current_capital
    total_return = (final_capital - 10000) / 10000
    
    # 計算其他指標
    capital_series = df['cumulative_capital'].dropna()
    
    if len(capital_series) > 1:
        # 計算回報率序列
        returns = capital_series.pct_change().dropna()
        
        # 年化收益率
        if timeframe == '1H':
            periods_per_year = 365 * 24
        elif timeframe == '4H':
            periods_per_year = 365 * 6
        elif timeframe == 'Daily':
            periods_per_year = 365
        
        if len(returns) > 0:
            annual_return = (1 + returns.mean()) ** periods_per_year - 1
            volatility = returns.std() * np.sqrt(periods_per_year)
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(periods_per_year) if returns.std() > 0 else 0
        else:
            annual_return = 0
            volatility = 0
            sharpe_ratio = 0
        
        # 最大回撤
        rolling_max = capital_series.expanding().max()
        drawdown = (capital_series - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 交易統計
        signal_changes = (df['Signal'] != df['Signal'].shift(1)).sum()
        total_fees = df['fees'].sum()
        
        performance = {
            'coin': coin,
            'timeframe': timeframe,
            'initial_capital': 10000,
            'final_capital': final_capital,
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'signal_changes': signal_changes,
            'total_fees': total_fees,
            'data_points': len(df)
        }
        
        print(f"=== {coin}-{timeframe} 全倉策略結果 ===")
        print(f"初始本金: ${10000}")
        print(f"最終本金: ${final_capital:.2f}")
        print(f"總收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"夏普比率: {sharpe_ratio:.4f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"信號變化次數: {signal_changes}")
        print(f"總手續費: ${total_fees:.2f}")
        
        return performance, df
    
    else:
        print(f"❌ {coin}-{timeframe} 數據不足")
        return None

def test_top_strategies_full_position():
    """測試頂級策略的全倉表現"""
    
    print("="*80)
    print("全倉策略真實表現測試")
    print("測試原始高夏普比率策略在加入交易成本後的表現")
    print("="*80)
    
    folder_name = create_full_position_folder()
    
    all_results = []
    
    # 測試頂級策略
    top_strategies = [
        ('PEPE', '1H', "1H_Strategy_Backtest/PEPE_1H_CCB_TakerIntensity_Backtest_*.csv"),
        ('XRP', '1H', "1H_Strategy_Backtest/XRP_1H_CCB_TakerIntensity_Backtest_*.csv"),
        ('SOL', '1H', "1H_Strategy_Backtest/SOL_1H_CCB_TakerIntensity_Backtest_*.csv"),
        ('WIF', '1H', "1H_Strategy_Backtest/WIF_1H_CCB_TakerIntensity_Backtest_*.csv"),
        ('BTC', 'Daily', "Institutional_CCB_TakerIntensity_Backtest_*.csv"),
        ('WIF', 'Daily', "WIF_CCB_TakerIntensity_Backtest_*.csv"),
        ('BNB', '4H', "4H_Strategy_Backtest/BNB_4H_CCB_TakerIntensity_Backtest_*.csv")
    ]
    
    for coin, timeframe, file_pattern in top_strategies:
        print(f"\n{'='*60}")
        print(f"測試 {coin}-{timeframe} 全倉策略")
        print(f"{'='*60}")
        
        files = glob.glob(file_pattern)
        
        if files:
            try:
                df = pd.read_csv(files[0])
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)
                
                result = calculate_full_position_performance(df, coin, timeframe)
                
                if result is not None:
                    performance, df_result = result
                    all_results.append(performance)
                    
                    # 保存結果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{folder_name}/{coin}_{timeframe}_FullPosition_{timestamp}.csv"
                    df_result.to_csv(filename)
                    print(f"✅ {coin}-{timeframe} 全倉策略結果已保存: {filename}")
            
            except Exception as e:
                print(f"❌ {coin}-{timeframe} 處理失敗: {e}")
        else:
            print(f"❌ 找不到 {coin}-{timeframe} 文件")
    
    return all_results, folder_name

def generate_full_position_report(all_results, folder_name):
    """生成全倉策略綜合報告"""
    
    print(f"\n{'='*80}")
    print("全倉策略綜合報告")
    print(f"{'='*80}")
    
    if not all_results:
        print("❌ 沒有成功的全倉策略結果")
        return None
    
    # 創建結果DataFrame
    df_results = pd.DataFrame(all_results)
    df_results = df_results.sort_values('sharpe_ratio', ascending=False)
    
    print(f"\n🏆 全倉策略表現排名 (按夏普比率):")
    print("-" * 80)
    
    for i, row in df_results.iterrows():
        rank = df_results.index.get_loc(i) + 1
        coin = row['coin']
        timeframe = row['timeframe']
        final_capital = row['final_capital']
        total_return = row['total_return']
        sharpe = row['sharpe_ratio']
        max_dd = row['max_drawdown']
        
        status = "🎉" if sharpe >= 1.5 else "⚠️" if sharpe >= 0 else "❌"
        
        print(f"{rank:2d}. {status} {coin}-{timeframe}: ${final_capital:.0f} ({total_return:+.2%}), 夏普={sharpe:.3f}, 回撤={max_dd:.2%}")
    
    # 統計分析
    print(f"\n📊 全倉策略統計:")
    print(f"   總策略數: {len(all_results)}")
    print(f"   達標策略: {len(df_results[df_results['sharpe_ratio'] >= 1.5])}")
    print(f"   平均夏普比率: {df_results['sharpe_ratio'].mean():.4f}")
    print(f"   平均收益率: {df_results['total_return'].mean():.2%}")
    print(f"   最佳表現: {df_results.iloc[0]['coin']}-{df_results.iloc[0]['timeframe']} (夏普={df_results.iloc[0]['sharpe_ratio']:.4f})")
    
    # 保存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"{folder_name}/FullPosition_Strategy_Report_{timestamp}.csv"
    df_results.to_csv(report_filename, index=False)
    
    print(f"\n✅ 全倉策略報告已保存: {report_filename}")
    
    return df_results

if __name__ == "__main__":
    # 執行全倉策略測試
    results, folder_name = test_top_strategies_full_position()
    
    if results:
        # 生成綜合報告
        report_df = generate_full_position_report(results, folder_name)
        
        print(f"\n🎯 全倉策略測試完成！")
        print(f"這才是真正接近原始策略的表現")
    else:
        print(f"\n❌ 全倉策略測試失敗")

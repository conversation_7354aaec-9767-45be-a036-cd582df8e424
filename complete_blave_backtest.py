#!/usr/bin/env python3
"""
完整Blave API回測系統
直接使用Blave API獲取完整歷史數據，確保所有時框都有足夠的數據
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import aiohttp
import requests
from time import sleep

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager

class CompleteBLAVEBacktest:
    def __init__(self):
        self.config = ConfigManager()
        
        # Blave API配置
        self.api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
        self.secret_key = "5dc330fd5a40ca402111b7774266fc5c5c32d0941e77125a6de7956fce68b12f0d"
        self.base_url = "https://api.blave.org/"
        self.headers = {
            "api-key": self.api_key,
            "secret-key": self.secret_key,
            "Content-Type": "application/json"
        }
        
        # 交易配置
        self.capital_per_coin = 1000  # 每隻幣1000U配額
        self.margin_ratio = 0.01      # 1%保證金
        self.leverage = 10            # 10倍槓桿
        self.position_size = self.capital_per_coin * self.margin_ratio * self.leverage  # 100U實際風險
        
        # 統一時間範圍配置
        self.base_days = 200  # 統一200天
        self.end_date = datetime.utcnow().date()
        self.start_date = self.end_date - timedelta(days=self.base_days)
        
        print(f"📅 統一時間範圍: {self.start_date} 到 {self.end_date} ({self.base_days}天)")
        
        # 時間框架配置
        self.timeframe_configs = {
            '1H': {
                'period': '1h',
                'expected_bars': self.base_days * 24,  # 4800根K線
                'ma_short': 8, 'ma_long': 15, 'ti_threshold': 0.3
            },
            '4H': {
                'period': '4h',
                'expected_bars': self.base_days * 6,   # 1200根K線
                'ma_short': 12, 'ma_long': 15, 'ti_threshold': 0.3
            },
            'Daily': {
                'period': '1d',
                'expected_bars': self.base_days,       # 200根K線
                'ma_short': 5, 'ma_long': 35, 'ti_threshold': 0.5
            }
        }
        
        # 盈虧比測試範圍
        self.risk_reward_ratios = [1.5, 2.0, 2.5, 3.0, 3.5]
        
        # ATR週期
        self.atr_period = 14
        
        self.results = []
        
    def get_supported_symbols(self) -> list:
        """獲取Blave API支持的交易對"""
        try:
            print("📊 獲取Blave API支持的交易對...")
            
            # 獲取Taker Intensity支持的symbols
            url = self.base_url + "taker_intensity/get_symbols"
            resp = requests.get(url, headers=self.headers, timeout=30)
            
            if resp.status_code == 200:
                ti_symbols = resp.json()["data"]
                print(f"✅ Taker Intensity支持的交易對: {len(ti_symbols)}個")
                
                # 獲取Holder Concentration支持的symbols
                url = self.base_url + "holder_concentration/get_symbols"
                resp = requests.get(url, headers=self.headers, timeout=30)
                
                if resp.status_code == 200:
                    hc_symbols = resp.json()["data"]
                    print(f"✅ Holder Concentration支持的交易對: {len(hc_symbols)}個")
                    
                    # 取交集，確保兩個指標都支持
                    common_symbols = list(set(ti_symbols) & set(hc_symbols))
                    print(f"✅ 兩個指標都支持的交易對: {len(common_symbols)}個")
                    print(f"   支持的交易對: {common_symbols}")
                    
                    return common_symbols
                else:
                    print(f"❌ 獲取Holder Concentration symbols失敗: {resp.status_code}")
                    return ti_symbols
            else:
                print(f"❌ 獲取Taker Intensity symbols失敗: {resp.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 獲取支持的交易對失敗: {e}")
            return []
    
    def fetch_blave_data(self, symbol: str, endpoint: str, period: str) -> pd.DataFrame:
        """從Blave API獲取歷史數據"""
        try:
            print(f"📊 獲取 {symbol} {endpoint} {period} 數據...")
            
            url = self.base_url + f"{endpoint}/get_alpha"
            params = {
                "symbol": symbol,
                "period": period,
                "start_date": self.start_date.strftime("%Y-%m-%d"),
                "end_date": self.end_date.strftime("%Y-%m-%d"),
                "timeframe": "24h"
            }
            
            resp = requests.get(url, headers=self.headers, params=params, timeout=60)
            
            if resp.status_code == 200:
                data = resp.json()["data"]
                
                if "alpha" in data and "timestamp" in data:
                    df = pd.DataFrame({
                        "timestamp": pd.to_datetime(data["timestamp"], unit="s"),
                        endpoint.replace("_", ""): data["alpha"]  # taker_intensity -> takerintensity
                    })
                    
                    df.set_index("timestamp", inplace=True)
                    df.sort_index(inplace=True)
                    
                    print(f"✅ {symbol} {endpoint} {period}: {len(df)} 條記錄")
                    print(f"   時間範圍: {df.index[0]} 到 {df.index[-1]}")
                    
                    return df
                else:
                    print(f"❌ {symbol} {endpoint} {period} 數據格式錯誤")
                    return None
            else:
                print(f"❌ {symbol} {endpoint} {period} API錯誤: {resp.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ {symbol} {endpoint} {period} 獲取失敗: {e}")
            return None
    
    async def fetch_bybit_klines(self, symbol: str, interval: str, limit: int) -> pd.DataFrame:
        """從Bybit獲取K線數據"""
        try:
            print(f"📊 從Bybit獲取 {symbol} {interval} K線數據...")
            
            all_data = []
            batch_size = 1000
            
            async with aiohttp.ClientSession() as session:
                for i in range(0, limit, batch_size):
                    current_limit = min(batch_size, limit - i)
                    
                    url = "https://api.bybit.com/v5/market/kline"
                    params = {
                        'category': 'spot',
                        'symbol': symbol,
                        'interval': interval,
                        'limit': current_limit
                    }
                    
                    if i > 0 and all_data:
                        end_time = int(all_data[-1].index[0].timestamp() * 1000)
                        params['end'] = end_time
                    
                    async with session.get(url, params=params, timeout=30) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data['retCode'] == 0:
                                klines = data['result']['list']
                                
                                if not klines:
                                    break
                                
                                df = pd.DataFrame(klines, columns=[
                                    'timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'Turnover'
                                ])
                                
                                df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
                                for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                                    df[col] = df[col].astype(float)
                                
                                df.set_index('timestamp', inplace=True)
                                df.sort_index(inplace=True)
                                
                                all_data.append(df)
                                
                                await asyncio.sleep(0.1)
                            else:
                                print(f"❌ Bybit API錯誤: {data}")
                                break
                        else:
                            print(f"❌ HTTP錯誤: {response.status}")
                            break
            
            if not all_data:
                return None
                
            combined_data = pd.concat(all_data, ignore_index=False)
            combined_data = combined_data.drop_duplicates().sort_index()
            
            if len(combined_data) > limit:
                combined_data = combined_data.tail(limit)
            
            print(f"✅ {symbol} {interval} K線數據: {len(combined_data)} 條記錄")
            
            return combined_data
            
        except Exception as e:
            print(f"❌ {symbol} {interval} K線數據獲取失敗: {e}")
            return None
    
    def get_complete_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """獲取完整的合併數據（K線 + Blave指標）"""
        try:
            print(f"\n🔍 獲取 {symbol} {timeframe} 完整數據...")
            
            config = self.timeframe_configs[timeframe]
            period = config['period']
            
            # 1. 獲取Taker Intensity數據
            ti_data = self.fetch_blave_data(symbol, "taker_intensity", period)
            if ti_data is None:
                return None
            
            # 重試機制
            sleep(1)
            
            # 2. 獲取Holder Concentration數據
            hc_data = self.fetch_blave_data(symbol, "holder_concentration", period)
            if hc_data is None:
                return None
            
            # 3. 獲取Bybit K線數據
            if period == '1h':
                interval = '1h'
            elif period == '4h':
                interval = '4h'
            else:  # 1d
                interval = '1d'
            
            # 使用asyncio運行K線數據獲取
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            kline_data = loop.run_until_complete(
                self.fetch_bybit_klines(symbol, interval, config['expected_bars'])
            )
            loop.close()
            
            if kline_data is None:
                return None
            
            # 4. 合併所有數據
            print(f"🔄 合併 {symbol} {timeframe} 數據...")
            
            # 重命名列避免衝突
            ti_data.columns = ['taker_intensity']
            hc_data.columns = ['concentration']
            
            # 時間對齊合併
            combined_data = kline_data.copy()
            
            # 使用最近鄰方法合併Blave數據
            for i, timestamp in enumerate(combined_data.index):
                # Taker Intensity
                ti_diffs = abs(ti_data.index - timestamp)
                if len(ti_diffs) > 0:
                    closest_ti_idx = ti_diffs.argmin()
                    if ti_diffs.iloc[closest_ti_idx].total_seconds() <= 24 * 3600:  # 1天內
                        combined_data.loc[timestamp, 'taker_intensity'] = ti_data.iloc[closest_ti_idx]['taker_intensity']
                
                # Holder Concentration
                hc_diffs = abs(hc_data.index - timestamp)
                if len(hc_diffs) > 0:
                    closest_hc_idx = hc_diffs.argmin()
                    if hc_diffs.iloc[closest_hc_idx].total_seconds() <= 24 * 3600:  # 1天內
                        combined_data.loc[timestamp, 'concentration'] = hc_data.iloc[closest_hc_idx]['concentration']
            
            # 前向填充缺失值
            combined_data['taker_intensity'] = combined_data['taker_intensity'].fillna(method='ffill')
            combined_data['concentration'] = combined_data['concentration'].fillna(method='ffill')
            
            # 移除仍有NaN的行
            combined_data = combined_data.dropna(subset=['taker_intensity', 'concentration'])
            
            if len(combined_data) < 100:
                print(f"❌ {symbol} {timeframe} 合併後數據不足: {len(combined_data)}")
                return None
            
            print(f"✅ {symbol} {timeframe} 完整數據合併成功: {len(combined_data)} 條記錄")
            print(f"   時間範圍: {combined_data.index[0]} 到 {combined_data.index[-1]}")
            
            return combined_data

        except Exception as e:
            print(f"❌ {symbol} {timeframe} 完整數據獲取失敗: {e}")
            return None

    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR（平均真實波幅）"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        prev_close = close.shift(1)

        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()

        return atr

    def generate_signals(self, data: pd.DataFrame, ti_threshold: float, ma_short: int, ma_long: int) -> list:
        """生成交易信號"""
        signals = []

        if len(data) < max(ma_short, ma_long, self.atr_period) + 10:
            return signals

        for i in range(max(ma_short, ma_long, self.atr_period), len(data)):
            price = data['Close'].iloc[i]
            prev_price = data['Close'].iloc[i-1]
            ti = data['taker_intensity'].iloc[i]
            concentration = data['concentration'].iloc[i]
            atr = data['atr'].iloc[i]

            ma_short_val = data[f'MA_{ma_short}'].iloc[i]
            ma_long_val = data[f'MA_{ma_long}'].iloc[i]
            prev_ma_short = data[f'MA_{ma_short}'].iloc[i-1]

            if pd.isna(ma_short_val) or pd.isna(ma_long_val) or pd.isna(ti) or pd.isna(atr):
                continue

            # 多頭信號條件
            long_conditions = [
                price > ma_short_val,
                prev_price <= prev_ma_short,
                ti > ti_threshold,
                ma_short_val > ma_long_val,
                concentration > 0
            ]

            # 空頭信號條件
            short_conditions = [
                price < ma_short_val,
                prev_price >= prev_ma_short,
                ti < -ti_threshold,
                ma_short_val < ma_long_val,
                concentration < 0
            ]

            if sum(long_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'LONG',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(long_conditions)
                })

            elif sum(short_conditions) >= 4:
                signals.append({
                    'timestamp': data.index[i],
                    'type': 'SHORT',
                    'price': price,
                    'atr': atr,
                    'ti': ti,
                    'concentration': concentration,
                    'conditions_met': sum(short_conditions)
                })

        return signals

    def execute_leveraged_trades(self, data: pd.DataFrame, signals: list, risk_reward_ratio: float) -> list:
        """執行槓桿交易回測"""
        trades = []

        for signal in signals:
            entry_time = signal['timestamp']
            entry_price = signal['price']
            direction = signal['type']
            atr = signal['atr']

            stop_loss_distance = atr
            take_profit_distance = atr * risk_reward_ratio

            if direction == 'LONG':
                stop_loss_price = entry_price - stop_loss_distance
                take_profit_price = entry_price + take_profit_distance
            else:
                stop_loss_price = entry_price + stop_loss_distance
                take_profit_price = entry_price - take_profit_distance

            exit_result = self.find_exit_point(data, entry_time, entry_price, direction,
                                             stop_loss_price, take_profit_price)

            if exit_result:
                price_change_pct = exit_result['price_change_pct']
                leveraged_pnl_pct = price_change_pct * self.leverage
                pnl_usd = self.position_size * (leveraged_pnl_pct / 100)

                max_adverse_excursion = self.calculate_mae(data, entry_time, exit_result['exit_time'],
                                                         entry_price, direction)

                trade = {
                    'entry_time': entry_time,
                    'exit_time': exit_result['exit_time'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': exit_result['exit_price'],
                    'exit_reason': exit_result['exit_reason'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'atr': atr,
                    'price_change_pct': price_change_pct,
                    'leveraged_pnl_pct': leveraged_pnl_pct,
                    'pnl_usd': pnl_usd,
                    'max_adverse_excursion_pct': max_adverse_excursion,
                    'holding_hours': (exit_result['exit_time'] - entry_time).total_seconds() / 3600,
                    'ti': signal['ti'],
                    'concentration': signal['concentration'],
                    'conditions_met': signal['conditions_met']
                }

                trades.append(trade)

        return trades

    def find_exit_point(self, data: pd.DataFrame, entry_time, entry_price: float, direction: str,
                       stop_loss_price: float, take_profit_price: float) -> dict:
        """尋找出場點"""
        try:
            entry_idx = data.index.get_loc(entry_time)

            for i in range(entry_idx + 1, len(data)):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]
                timestamp = data.index[i]

                if direction == 'LONG':
                    if low <= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (stop_loss_price / entry_price - 1) * 100
                        }
                    elif high >= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (take_profit_price / entry_price - 1) * 100
                        }
                else:
                    if high >= stop_loss_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': stop_loss_price,
                            'exit_reason': 'STOP_LOSS',
                            'price_change_pct': (entry_price / stop_loss_price - 1) * 100
                        }
                    elif low <= take_profit_price:
                        return {
                            'exit_time': timestamp,
                            'exit_price': take_profit_price,
                            'exit_reason': 'TAKE_PROFIT',
                            'price_change_pct': (entry_price / take_profit_price - 1) * 100
                        }

            final_price = data['Close'].iloc[-1]
            final_time = data.index[-1]

            if direction == 'LONG':
                price_change_pct = (final_price / entry_price - 1) * 100
            else:
                price_change_pct = (entry_price / final_price - 1) * 100

            return {
                'exit_time': final_time,
                'exit_price': final_price,
                'exit_reason': 'END_OF_DATA',
                'price_change_pct': price_change_pct
            }

        except Exception as e:
            return None

    def calculate_mae(self, data: pd.DataFrame, entry_time, exit_time, entry_price: float, direction: str) -> float:
        """計算最大不利偏移"""
        try:
            entry_idx = data.index.get_loc(entry_time)
            exit_idx = data.index.get_loc(exit_time)

            max_adverse = 0

            for i in range(entry_idx, exit_idx + 1):
                high = data['High'].iloc[i]
                low = data['Low'].iloc[i]

                if direction == 'LONG':
                    adverse = (low / entry_price - 1) * 100
                    max_adverse = min(max_adverse, adverse)
                else:
                    adverse = (entry_price / high - 1) * 100
                    max_adverse = min(max_adverse, adverse)

            return max_adverse

        except Exception as e:
            return 0

    def calculate_leveraged_performance(self, trades: list) -> dict:
        """計算槓桿交易表現"""
        if not trades:
            return {
                'total_trades': 0, 'win_rate': 0, 'total_return_usd': 0, 'total_return_pct': 0,
                'avg_win_usd': 0, 'avg_loss_usd': 0, 'actual_risk_reward_ratio': 0,
                'profit_factor': 0, 'max_drawdown_usd': 0, 'max_drawdown_pct': 0,
                'max_adverse_excursion_pct': 0, 'avg_adverse_excursion_pct': 0,
                'avg_holding_hours': 0, 'sharpe_ratio': 0, 'stop_loss_rate': 0, 'take_profit_rate': 0
            }

        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl_usd'] > 0]
        losing_trades = [t for t in trades if t['pnl_usd'] < 0]

        win_rate = len(winning_trades) / total_trades * 100
        total_pnl_usd = sum([t['pnl_usd'] for t in trades])
        avg_win_usd = np.mean([t['pnl_usd'] for t in winning_trades]) if winning_trades else 0
        avg_loss_usd = np.mean([t['pnl_usd'] for t in losing_trades]) if losing_trades else 0

        actual_rr = abs(avg_win_usd / avg_loss_usd) if avg_loss_usd != 0 else 0

        gross_profit = sum([t['pnl_usd'] for t in winning_trades]) if winning_trades else 0
        gross_loss = abs(sum([t['pnl_usd'] for t in losing_trades])) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0

        cumulative_pnl = np.cumsum([t['pnl_usd'] for t in trades])
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdown = cumulative_pnl - running_max
        max_drawdown_usd = np.min(drawdown)
        max_drawdown_pct = (max_drawdown_usd / self.capital_per_coin) * 100

        max_mae = min([t['max_adverse_excursion_pct'] for t in trades])
        avg_mae = np.mean([t['max_adverse_excursion_pct'] for t in trades])
        avg_holding_hours = np.mean([t['holding_hours'] for t in trades])

        returns = [t['pnl_usd'] for t in trades]
        returns_std = np.std(returns)
        sharpe_ratio = np.mean(returns) / returns_std if returns_std != 0 else 0

        stop_loss_count = len([t for t in trades if t['exit_reason'] == 'STOP_LOSS'])
        take_profit_count = len([t for t in trades if t['exit_reason'] == 'TAKE_PROFIT'])

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return_usd': total_pnl_usd,
            'total_return_pct': (total_pnl_usd / self.capital_per_coin) * 100,
            'avg_win_usd': avg_win_usd,
            'avg_loss_usd': avg_loss_usd,
            'actual_risk_reward_ratio': actual_rr,
            'profit_factor': profit_factor,
            'max_drawdown_usd': max_drawdown_usd,
            'max_drawdown_pct': max_drawdown_pct,
            'max_adverse_excursion_pct': max_mae,
            'avg_adverse_excursion_pct': avg_mae,
            'avg_holding_hours': avg_holding_hours,
            'sharpe_ratio': sharpe_ratio,
            'stop_loss_rate': (stop_loss_count / total_trades) * 100,
            'take_profit_rate': (take_profit_count / total_trades) * 100
        }

    def test_complete_strategy(self, symbol: str, timeframe: str, risk_reward_ratio: float) -> dict:
        """測試完整策略"""
        try:
            print(f"\n📊 測試 {symbol} {timeframe} RR{risk_reward_ratio} (完整Blave數據)...")

            # 獲取完整數據
            data = self.get_complete_data(symbol, timeframe)

            if data is None or data.empty:
                print(f"❌ {symbol} {timeframe} 數據獲取失敗")
                return None

            # 計算ATR
            data['atr'] = self.calculate_atr(data, self.atr_period)

            # 獲取策略參數
            config = self.timeframe_configs[timeframe]
            ma_short = config['ma_short']
            ma_long = config['ma_long']
            ti_threshold = config['ti_threshold']

            # 計算均線
            data[f'MA_{ma_short}'] = data['Close'].rolling(window=ma_short).mean()
            data[f'MA_{ma_long}'] = data['Close'].rolling(window=ma_long).mean()

            # 生成信號
            signals = self.generate_signals(data, ti_threshold, ma_short, ma_long)

            if len(signals) < 2:
                print(f"❌ {symbol} {timeframe} 信號不足: {len(signals)}")
                return None

            print(f"✅ {symbol} {timeframe} 生成信號: {len(signals)} 個")

            # 執行交易
            trades = self.execute_leveraged_trades(data, signals, risk_reward_ratio)

            if not trades:
                print(f"❌ {symbol} {timeframe} 無有效交易")
                return None

            print(f"✅ {symbol} {timeframe} 執行交易: {len(trades)} 筆")

            # 計算表現
            performance = self.calculate_leveraged_performance(trades)

            # 計算時間範圍
            time_span = (data.index[-1] - data.index[0]).total_seconds() / (24 * 3600)

            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'risk_reward_ratio': risk_reward_ratio,
                'time_span_days': time_span,
                'data_points': len(data),
                'expected_bars': config['expected_bars'],
                'data_completeness': (len(data) / config['expected_bars']) * 100,
                'signals': len(signals),
                'signals_per_day': len(signals) / time_span,
                'trades_per_day': len(trades) / time_span,
                'trades': trades[:5],
                **performance
            }

            print(f"✅ {symbol} {timeframe} RR{risk_reward_ratio}: 收益{performance['total_return_usd']:+.1f}U, 勝率{performance['win_rate']:.1f}%")
            print(f"   時間跨度: {time_span:.1f}天 | 數據完整度: {result['data_completeness']:.1f}%")

            return result

        except Exception as e:
            print(f"❌ {symbol} {timeframe} RR{risk_reward_ratio} 測試失敗: {e}")
            return None

    def optimize_symbol_timeframe(self, symbol: str, timeframe: str) -> list:
        """優化單一幣種和時框的盈虧比"""
        print(f"\n🔍 優化 {symbol} {timeframe} (完整Blave數據)...")

        results = []

        for rr in self.risk_reward_ratios:
            result = self.test_complete_strategy(symbol, timeframe, rr)

            if result and result['total_trades'] > 0:
                results.append(result)

            # 避免API限制
            sleep(2)

        results.sort(key=lambda x: x['total_return_usd'], reverse=True)

        if results:
            best = results[0]
            print(f"✅ {symbol} {timeframe} 最佳RR{best['risk_reward_ratio']}: {best['total_return_usd']:+.1f}U")
            print(f"   數據完整度: {best['data_completeness']:.1f}%")
        else:
            print(f"❌ {symbol} {timeframe} 無有效結果")

        return results

    def run_complete_backtest(self):
        """運行完整的Blave API回測"""
        print("🚀 啟動完整Blave API回測系統")
        print(f"💰 每隻幣配額: {self.capital_per_coin}U")
        print(f"📊 保證金: {self.margin_ratio*100}% | 槓桿: {self.leverage}x")
        print(f"📅 統一時間範圍: {self.start_date} 到 {self.end_date} ({self.base_days}天)")
        print("🔗 直接使用Blave API獲取完整歷史數據")
        print("="*80)

        # 獲取支持的交易對
        supported_symbols = self.get_supported_symbols()

        if not supported_symbols:
            print("❌ 無法獲取支持的交易對")
            return

        # 選擇前10個交易對進行測試
        test_symbols = supported_symbols[:10]
        print(f"🎯 測試交易對: {test_symbols}")

        all_results = {}

        for symbol in test_symbols:
            print(f"\n🔍 測試 {symbol}...")
            all_results[symbol] = {}

            for timeframe in ['Daily', '4H', '1H']:
                try:
                    results = self.optimize_symbol_timeframe(symbol, timeframe)
                    all_results[symbol][timeframe] = results

                    if results:
                        self.results.append(results[0])

                except Exception as e:
                    print(f"❌ {symbol} {timeframe} 優化失敗: {e}")
                    all_results[symbol][timeframe] = []

                # 避免API限制
                sleep(3)

        # 保存和分析結果
        summary_df = self.save_complete_results(all_results)
        self.analyze_complete_results()

        return all_results, summary_df

    def save_complete_results(self, all_results: dict):
        """保存完整回測結果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        results_dir = "Complete_BLAVE_Backtest_Results"
        os.makedirs(results_dir, exist_ok=True)

        summary_data = []
        detailed_trades = []

        for symbol in all_results:
            for timeframe in all_results[symbol]:
                results = all_results[symbol][timeframe]

                if results:
                    best = results[0]

                    summary_data.append({
                        'Symbol': symbol,
                        'Timeframe': timeframe,
                        'Best_Risk_Reward_Ratio': best['risk_reward_ratio'],
                        'Time_Span_Days': round(best['time_span_days'], 1),
                        'Data_Points': best['data_points'],
                        'Expected_Bars': best['expected_bars'],
                        'Data_Completeness_%': round(best['data_completeness'], 1),
                        'Total_Signals': best['signals'],
                        'Signals_Per_Day': round(best['signals_per_day'], 2),
                        'Total_Trades': best['total_trades'],
                        'Trades_Per_Day': round(best['trades_per_day'], 2),
                        'Win_Rate_%': round(best['win_rate'], 2),
                        'Total_Return_USD': round(best['total_return_usd'], 2),
                        'Total_Return_%': round(best['total_return_pct'], 2),
                        'Actual_RR': round(best['actual_risk_reward_ratio'], 2),
                        'Profit_Factor': round(best['profit_factor'], 2),
                        'Max_Drawdown_USD': round(best['max_drawdown_usd'], 2),
                        'Max_Drawdown_%': round(best['max_drawdown_pct'], 2),
                        'Max_Adverse_Excursion_%': round(best['max_adverse_excursion_pct'], 2),
                        'Avg_Holding_Hours': round(best['avg_holding_hours'], 2),
                        'Sharpe_Ratio': round(best['sharpe_ratio'], 2),
                        'Stop_Loss_Rate_%': round(best['stop_loss_rate'], 2),
                        'Take_Profit_Rate_%': round(best['take_profit_rate'], 2)
                    })

                    for trade in best['trades']:
                        detailed_trades.append({
                            'Symbol': symbol,
                            'Timeframe': timeframe,
                            'RR_Ratio': best['risk_reward_ratio'],
                            'Entry_Time': trade['entry_time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'Exit_Time': trade['exit_time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'Direction': trade['direction'],
                            'Entry_Price': round(trade['entry_price'], 6),
                            'Exit_Price': round(trade['exit_price'], 6),
                            'Exit_Reason': trade['exit_reason'],
                            'Stop_Loss_Price': round(trade['stop_loss_price'], 6),
                            'Take_Profit_Price': round(trade['take_profit_price'], 6),
                            'ATR': round(trade['atr'], 6),
                            'Price_Change_%': round(trade['price_change_pct'], 2),
                            'Leveraged_PnL_%': round(trade['leveraged_pnl_pct'], 2),
                            'PnL_USD': round(trade['pnl_usd'], 2),
                            'Max_Adverse_Excursion_%': round(trade['max_adverse_excursion_pct'], 2),
                            'Holding_Hours': round(trade['holding_hours'], 2),
                            'TI': round(trade['ti'], 3),
                            'Concentration': round(trade['concentration'], 3),
                            'Conditions_Met': trade['conditions_met']
                        })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values('Total_Return_USD', ascending=False)

            summary_file = f"{results_dir}/Complete_BLAVE_Summary_{timestamp}.csv"
            summary_df.to_csv(summary_file, index=False)

            print(f"\n✅ 完整Blave回測綜合報告已保存: {summary_file}")

            if detailed_trades:
                trades_df = pd.DataFrame(detailed_trades)
                trades_file = f"{results_dir}/Complete_BLAVE_Trades_{timestamp}.csv"
                trades_df.to_csv(trades_file, index=False)

                print(f"✅ 詳細交易記錄已保存: {trades_file}")

            return summary_df

        return pd.DataFrame()

    def analyze_complete_results(self):
        """分析完整回測結果"""
        if not self.results:
            print("❌ 無結果可分析")
            return

        print("\n" + "="*80)
        print("🏆 完整Blave API回測分析報告")
        print(f"📅 統一時間範圍: {self.start_date} 到 {self.end_date} ({self.base_days}天)")
        print("🔗 直接使用Blave API獲取完整歷史數據")
        print("="*80)

        self.results.sort(key=lambda x: x['total_return_usd'], reverse=True)
        profitable_strategies = [r for r in self.results if r['total_return_usd'] > 0]

        print(f"💰 盈利策略: {len(profitable_strategies)}/{len(self.results)} 個")

        if profitable_strategies:
            print(f"\n🏆 前10個最佳策略 (完整Blave數據):")
            for i, strategy in enumerate(profitable_strategies[:10], 1):
                print(f"{i:2d}. {strategy['symbol']} {strategy['timeframe']} RR{strategy['risk_reward_ratio']}")
                print(f"     收益: {strategy['total_return_usd']:+.1f}U ({strategy['total_return_pct']:+.1f}%)")
                print(f"     勝率: {strategy['win_rate']:.1f}% | 交易: {strategy['total_trades']}筆")
                print(f"     每天交易: {strategy['trades_per_day']:.2f}筆 | 每天信號: {strategy['signals_per_day']:.2f}個")
                print(f"     最大回撤: {strategy['max_drawdown_usd']:.1f}U ({strategy['max_drawdown_pct']:.1f}%)")
                print(f"     最大浮虧: {strategy['max_adverse_excursion_pct']:.1f}%")
                print(f"     數據完整度: {strategy['data_completeness']:.1f}% | 時間跨度: {strategy['time_span_days']:.1f}天")

        # 按時間框架分析
        print(f"\n📊 各時間框架表現:")
        for timeframe in ['Daily', '4H', '1H']:
            tf_results = [r for r in self.results if r['timeframe'] == timeframe]
            if tf_results:
                tf_profitable = [r for r in tf_results if r['total_return_usd'] > 0]
                best_tf = max(tf_results, key=lambda x: x['total_return_usd'])
                avg_completeness = np.mean([r['data_completeness'] for r in tf_results])

                print(f"  {timeframe}:")
                print(f"    最佳策略: {best_tf['symbol']} ({best_tf['total_return_usd']:+.1f}U)")
                print(f"    盈利比例: {len(tf_profitable)}/{len(tf_results)} ({len(tf_profitable)/len(tf_results)*100:.1f}%)")
                print(f"    平均數據完整度: {avg_completeness:.1f}%")
                print(f"    平均每天交易: {np.mean([r['trades_per_day'] for r in tf_results]):.2f}筆")

        total_return = sum([r['total_return_usd'] for r in self.results])
        avg_return = np.mean([r['total_return_usd'] for r in self.results])

        print(f"\n📊 整體統計:")
        print(f"   總收益: {total_return:+.1f}U")
        print(f"   平均收益: {avg_return:+.1f}U")
        print(f"   最佳單策略: {self.results[0]['total_return_usd']:+.1f}U")
        print(f"   盈利策略比例: {len(profitable_strategies)/len(self.results)*100:.1f}%")

def main():
    """主函數"""
    backtest = CompleteBLAVEBacktest()

    try:
        print("開始完整Blave API回測...")
        results, summary_df = backtest.run_complete_backtest()

        print(f"\n🎉 完整Blave API回測完成！")
        print("🔗 使用Blave API直接獲取完整歷史數據")
        print(f"📅 統一時間範圍: {backtest.base_days}天")
        print("🎯 實現了真正的數據完整性和公平比較！")

    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()

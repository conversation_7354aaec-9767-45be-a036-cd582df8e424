"""
日誌設置模組
統一管理系統日誌配置

作者: 專業量化策略工程師
"""

import logging
import os
from datetime import datetime
from pathlib import Path

def setup_logger(name: str, level: str = "INFO") -> logging.Logger:
    """
    設置日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        level: 日誌級別
    
    Returns:
        配置好的日誌記錄器
    """
    
    # 創建日誌目錄
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 創建日誌記錄器
    logger = logging.getLogger(name)
    
    # 避免重複添加處理器
    if logger.handlers:
        return logger
    
    # 設置日誌級別
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 創建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件處理器
    today = datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f"signal_system_{today}.log"
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 防止日誌向上傳播
    logger.propagate = False
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """獲取已配置的日誌記錄器"""
    return logging.getLogger(name)

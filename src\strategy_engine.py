"""
策略引擎模組
實現CCB + Taker Intensity聖杯級策略

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
from .logger_setup import setup_logger

class StrategyEngine:
    """策略計算引擎"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("StrategyEngine")
    
    def calculate_indicators(self, data: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """計算所有策略指標"""
        try:
            self.logger.info(f"🧮 計算 {timeframe} 策略指標")
            
            # 獲取策略參數
            strategy_name = f"{data.index[-1].strftime('%Y%m%d')}_{timeframe}"
            params = self._get_strategy_params(timeframe)
            
            # 計算CCB指標
            ccb_data = self.calculate_ccb(data, params['ccb_window'], params['ccb_std'])
            
            # 計算Taker Intensity信號
            taker_signals = self.calculate_taker_intensity_signals(
                data, params['taker_lookback'], params['taker_threshold']
            )
            
            # 合併所有指標
            indicators = {
                'data': data,
                'ccb': ccb_data,
                'taker_signals': taker_signals,
                'params': params,
                'timestamp': data.index[-1],
                'current_price': data['Close'].iloc[-1]
            }
            
            self.logger.info(f"✅ {timeframe} 指標計算完成")
            return indicators
            
        except Exception as e:
            self.logger.error(f"❌ 指標計算失敗: {e}")
            return {}
    
    def _get_strategy_params(self, timeframe: str) -> Dict[str, Any]:
        """獲取策略參數"""
        # 根據時框調整參數 - 優化版本增加信號頻率
        if timeframe == '1H':
            return {
                'ccb_window': 12,
                'ccb_std': 1.8,  # 從2.0降到1.8，增加觸發頻率
                'taker_lookback': 24,
                'taker_threshold': 55  # 從65降到55，增加信號頻率
            }
        elif timeframe == '4H':
            return {
                'ccb_window': 15,
                'ccb_std': 1.8,
                'taker_lookback': 20,
                'taker_threshold': 55
            }
        else:  # Daily
            return {
                'ccb_window': 25,
                'ccb_std': 1.5,
                'taker_lookback': 20,
                'taker_threshold': 50
            }
    
    def calculate_ccb(self, data: pd.DataFrame, window: int, std_dev: float) -> Dict[str, Any]:
        """計算籌碼集中帶 (CCB)"""
        try:
            # 計算移動平均和標準差
            data['CCB_Middle'] = data['concentration'].rolling(window=window).mean()
            data['CCB_Std'] = data['concentration'].rolling(window=window).std()
            
            # 計算上下軌
            data['CCB_Upper'] = data['CCB_Middle'] + (data['CCB_Std'] * std_dev)
            data['CCB_Lower'] = data['CCB_Middle'] - (data['CCB_Std'] * std_dev)
            
            # 獲取最新值
            latest = data.iloc[-1]
            
            ccb_data = {
                'concentration': latest['concentration'],
                'middle': latest['CCB_Middle'],
                'upper': latest['CCB_Upper'],
                'lower': latest['CCB_Lower'],
                'position': self._get_ccb_position(latest['concentration'], latest['CCB_Upper'], latest['CCB_Lower'])
            }
            
            return ccb_data
            
        except Exception as e:
            self.logger.error(f"❌ CCB計算失敗: {e}")
            return {}
    
    def _get_ccb_position(self, concentration: float, upper: float, lower: float) -> str:
        """判斷籌碼集中度位置"""
        if concentration > upper:
            return 'ABOVE_UPPER'
        elif concentration < lower:
            return 'BELOW_LOWER'
        else:
            return 'MIDDLE'
    
    def calculate_taker_intensity_signals(self, data: pd.DataFrame, lookback: int, threshold: float) -> Dict[str, Any]:
        """計算Taker Intensity信號"""
        try:
            # 計算百分位數信號
            data['Long_Signal'] = data['long_taker_intensity'].rolling(window=lookback).apply(
                lambda x: x.iloc[-1] >= np.percentile(x, threshold) if len(x) == lookback else False
            )
            
            data['Short_Signal'] = data['short_taker_intensity'].rolling(window=lookback).apply(
                lambda x: x.iloc[-1] >= np.percentile(x, threshold) if len(x) == lookback else False
            )
            
            # 獲取最新值
            latest = data.iloc[-1]
            
            taker_signals = {
                'long_intensity': latest['long_taker_intensity'],
                'short_intensity': latest['short_taker_intensity'],
                'long_signal': latest['Long_Signal'],
                'short_signal': latest['Short_Signal'],
                'long_percentile': self._calculate_percentile(data['long_taker_intensity'], lookback),
                'short_percentile': self._calculate_percentile(data['short_taker_intensity'], lookback)
            }
            
            return taker_signals
            
        except Exception as e:
            self.logger.error(f"❌ Taker Intensity計算失敗: {e}")
            return {}
    
    def _calculate_percentile(self, series: pd.Series, lookback: int) -> float:
        """計算當前值在回望期內的百分位數"""
        try:
            recent_data = series.tail(lookback)
            if len(recent_data) > 0:
                current_value = recent_data.iloc[-1]
                percentile = (recent_data <= current_value).mean() * 100
                return percentile
            return 50.0
        except:
            return 50.0
    
    def generate_entry_signal(self, indicators: Dict[str, Any]) -> Optional[str]:
        """生成入場信號"""
        try:
            ccb = indicators.get('ccb', {})
            taker = indicators.get('taker_signals', {})
            
            ccb_position = ccb.get('position')
            long_signal = taker.get('long_signal', False)
            short_signal = taker.get('short_signal', False)
            
            # 多頭信號：籌碼集中度跌破下軌 AND 多方力道突破閾值
            if ccb_position == 'BELOW_LOWER' and long_signal:
                return 'LONG'
            
            # 空頭信號：籌碼集中度突破上軌 AND 空方力道突破閾值
            elif ccb_position == 'ABOVE_UPPER' and short_signal:
                return 'SHORT'
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 信號生成失敗: {e}")
            return None
    
    def should_close_position(self, indicators: Dict[str, Any], current_position: str) -> bool:
        """判斷是否應該平倉"""
        try:
            ccb = indicators.get('ccb', {})
            taker = indicators.get('taker_signals', {})
            
            ccb_position = ccb.get('position')
            long_signal = taker.get('long_signal', False)
            short_signal = taker.get('short_signal', False)
            
            if current_position == 'LONG':
                # 多頭平倉條件：籌碼集中度回到中軌以上 OR 空方力道突破
                return ccb_position != 'BELOW_LOWER' or short_signal
            
            elif current_position == 'SHORT':
                # 空頭平倉條件：籌碼集中度回到中軌以下 OR 多方力道突破
                return ccb_position != 'ABOVE_UPPER' or long_signal
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 平倉判斷失敗: {e}")
            return False
    
    def calculate_risk_metrics(self, data: pd.DataFrame) -> Dict[str, float]:
        """計算風險指標"""
        try:
            # 計算ATR
            high = data['Close'] * 1.02  # 模擬高價
            low = data['Close'] * 0.98   # 模擬低價
            
            tr = np.maximum(
                high - low,
                np.maximum(
                    np.abs(high - data['Close'].shift(1)),
                    np.abs(low - data['Close'].shift(1))
                )
            )
            
            atr = tr.rolling(window=14).mean().iloc[-1]
            atr_pct = atr / data['Close'].iloc[-1]
            
            # 計算波動率
            returns = data['Close'].pct_change()
            volatility = returns.rolling(window=20).std().iloc[-1]
            
            return {
                'atr': atr,
                'atr_pct': atr_pct,
                'volatility': volatility,
                'price_change_1h': returns.iloc[-1],
                'price_change_24h': (data['Close'].iloc[-1] / data['Close'].iloc[-24] - 1) if len(data) >= 24 else 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ 風險指標計算失敗: {e}")
            return {}
